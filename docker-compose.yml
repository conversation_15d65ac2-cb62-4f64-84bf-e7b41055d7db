version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: orbit-postgres
    environment:
      POSTGRES_DB: orbit
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./shared/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - orbit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d orbit"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: orbit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - orbit-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: orbit-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/orbit
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./shared:/app/shared
      - /app/node_modules
    networks:
      - orbit-network
    command: npm run dev

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: orbit-frontend
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000/api
      - VITE_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
    ports:
      - "8080:8080"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - orbit-network
    command: npm run dev

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  orbit-network:
    driver: bridge
