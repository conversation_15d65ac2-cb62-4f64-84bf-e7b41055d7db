[{"id": "49c601c3-853e-4675-86be-41d4ac86ad0e", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Deploy Orbit + CRM in working state to GCP", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-16T01:06:31.12433+00:00", "updated_at": "2025-07-16T01:06:50.713926+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "ebe3de8d-5380-48db-b3a4-c41ef15c6753", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Add Google Account <PERSON>gins", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "to-do", "created_at": "2025-07-16T01:07:28.545082+00:00", "updated_at": "2025-07-16T01:07:28.545082+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "e129b68c-e3d4-4dc6-b999-8544babbfd6c", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Clean up and document Deployment process", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "to-do", "created_at": "2025-07-16T01:07:49.222152+00:00", "updated_at": "2025-07-16T01:07:49.222152+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "41ca3fc9-0cda-417f-9ded-f641475a6997", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Define Dashboard Requirements ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:21:26.175036+00:00", "updated_at": "2025-07-16T03:21:26.175036+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "930d2320-f999-4068-912e-35ef72d0cc1f", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Add Projects from Sheets ", "description": "Add all jobs with appropriate leads from the google sheet <PERSON><PERSON> and <PERSON> made. ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:23:48.011952+00:00", "updated_at": "2025-07-16T03:23:48.011952+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "9ec4650c-06f5-474a-a45d-c7dc4974dfa4", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "implementing start date, end date ", "description": "having color coded project's to see what is close to deadline and what isn't incorporated into statuses of each project.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:59:02.41148+00:00", "updated_at": "2025-07-16T03:59:05.279862+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "a93cd5ce-c743-4e6f-b495-e4d45ab357ed", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Talk with team", "description": "ask the team their opinion and add what they think would be good for them ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T04:01:35.572644+00:00", "updated_at": "2025-07-16T04:01:35.572644+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "3b93a105-9f77-4457-a37e-0b236a644d58", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Deploy ", "description": "So everyone can input their projects they are working on and enter their tasks. ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T04:02:47.929927+00:00", "updated_at": "2025-07-16T04:29:00.775984+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "7fb428a6-9e80-42a7-833b-f3a9c1cfb276", "project_id": "0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22", "name": "Mvp internal delivery ", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-17", "status": "to-do", "created_at": "2025-07-16T04:25:59.318566+00:00", "updated_at": "2025-07-16T04:25:59.318566+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "90b9dc51-0a09-4258-8df2-57ba967190ac", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Make done tasks green background and put an option to not show done tasks", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-15", "status": "done", "created_at": "2025-07-16T04:30:19.77468+00:00", "updated_at": "2025-07-16T07:52:08.223709+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "53f9b90a-5e5f-445c-8bdb-5a053fbc889e", "project_id": "b04eba31-b738-40f7-9ba8-eed12d725ff2", "name": "Fishbowl MCP Creation ", "description": "Model Context Protocol (MCP) server for the Fishbowl Inventory system to enable AI agents to interact with inventory, order, and shipment data programmatically. The server will act as an interface layer between Fishbowl’s APIs and AI-driven agents, facilitating structured queries, automated decision-making, and workflow execution within existing infrastructure. Key capabilities include inventory lookups, order status checks, shipment tracking, and low-stock alerts, all accessible via standardized MCP endpoints. Built on Google Cloud with n8n orchestration and secured via OAuth, the server will support integrations with Supabase, Microsoft 365, and other business systems. The goal is to improve inventory visibility, reduce manual query overhead, and enable intelligent automation for supply chain operations.", "assignee": "<PERSON><PERSON> ", "due_date": "2025-07-14", "status": "to-do", "created_at": "2025-07-16T21:12:16.275503+00:00", "updated_at": "2025-07-16T21:12:16.275503+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "5f5a8665-7ec7-4a3d-a914-e704281687d8", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "create archive function and change toggle to show archive", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T21:13:57.648147+00:00", "updated_at": "2025-07-17T00:25:05.389488+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "85c5c296-f2b1-42fa-b0f1-e58c7ca96514", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add the ability to add priority details on creation of project", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T22:00:43.190871+00:00", "updated_at": "2025-07-17T00:31:10.456682+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "3044e5a8-0780-485c-9e11-b2432c4dd7ac", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add group by lead", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T22:02:11.795317+00:00", "updated_at": "2025-07-17T00:55:01.039586+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "7653456d-c8b3-4351-9ac6-c35184ee90ca", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Clean up organisation and overall UI", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T00:34:52.236466+00:00", "updated_at": "2025-07-17T00:34:52.236466+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "8c0819d0-f414-403b-91df-9a5382a33629", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "not having the ability to covert to task or make edits to a project that in archive  ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:00:46.186649+00:00", "updated_at": "2025-07-17T01:16:17.996497+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "d68dc239-c019-4621-a90f-eb1d162ac526", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Web (SPA) ", "description": "Web Application (SPA)", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:09:04.196026+00:00", "updated_at": "2025-07-17T01:09:04.196026+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "2c780c93-6d56-42f8-9e0f-ac184b4deacc", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Gateway Service ", "description": "The Gateway Service will act as the centralized entry point for all traffic across the AI platform, handling authentication, request routing, rate limiting, and logging. It will provide a consistent interface for clients and services, improve security by enforcing unified access controls, and simplify integration with internal tools and external APIs. This service ensures scalability, observability, and reliability as the platform grows.", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:15:51.06267+00:00", "updated_at": "2025-07-17T01:15:51.06267+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "bdd7677d-6d6d-41a4-89f3-8fceda414f48", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extended Web to feature CRM ", "description": "Extend the existing web application to include a CRM dashboard as a new feature. This dashboard will provide a centralized view of customer interactions, status updates, and communication history. It will be designed with a clean, user-friendly interface and integrate with existing user and project data. The CRM module will support filtering, search, tagging, and activity tracking, and will be built using the same tech stack as the core app to ensure seamless integration. Role-based access will control visibility, and all CRM data will be stored securely in the platform’s primary database.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:15:59.415004+00:00", "updated_at": "2025-07-17T01:15:59.415004+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "d48a892f-4736-4ba3-af47-10e3e6ad5a3a", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Database Setup", "description": "Centralized PostgreSQL database to support our AI platform’s core services. This database will store structured data such as user activity, model configurations, and product metadata. It will include role-based access control, daily automated backups with 30-day retention, and be provisioned using standardized schema templates for consistency across services. The setup will support separate environments for development, staging, and production, with monitoring in place for performance and cost tracking. The goal is to ensure a secure, scalable, and easy-to-manage foundation for all data-driven features on the platform.", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:16:05.839093+00:00", "updated_at": "2025-07-17T01:16:05.839093+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "2102b468-ed9f-47d7-9f78-285a2c811247", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extend Gateway For CRM API spec", "description": "Extend the existing Gateway Service to support the new CRM API, enabling secure and consistent access to CRM-related endpoints. This includes routing requests to the CRM service, enforcing authentication via API keys or JWT, and applying rate limiting per client. The CRM API spec will follow RESTful conventions and include endpoints for managing contacts, companies, interactions, and pipelines. Gateway logging and monitoring will capture usage metrics and errors for CRM traffic, ensuring visibility and reliability across the full request lifecycle.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:16:12.977615+00:00", "updated_at": "2025-07-17T01:16:12.977615+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "d938f07a-f3f5-4206-bad7-5bac6211d552", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extended DB for CRM ", "description": "extend the existing PostgreSQL database to support the CRM service by adding new tables for contacts, companies, interactions, and pipeline stages. These tables will be relational and linked to existing user and project data. The schema will support activity tracking, custom fields, and be managed through versioned migrations. All CRM data will be included in existing backup and monitoring processes.", "assignee": "<PERSON> ", "due_date": "2025-07-30", "status": "done", "created_at": "2025-07-17T01:29:30.403247+00:00", "updated_at": "2025-07-17T01:29:35.992318+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "9b43e939-ae6a-4520-829d-8d3ba3e99fce", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Set Up Supabase Backend", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:35:20.077067+00:00", "updated_at": "2025-07-17T02:35:41.12302+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "15fc06f2-efee-4e88-a5b1-1943bea6d37e", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Design Frontend with Lovable", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:35:39.412503+00:00", "updated_at": "2025-07-17T02:35:39.412503+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "89075347-65f0-44a2-96ed-c0e5f056b117", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Create Email Ingestion Workflow in n8n", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:39:30.774856+00:00", "updated_at": "2025-07-17T02:39:30.774856+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "19ed9f27-2f38-47d6-8862-0700129f7c77", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Build AI Role Classifier (Prompt + Node)", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:39:43.921895+00:00", "updated_at": "2025-07-17T02:39:43.921895+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "70527071-188a-492e-b195-f825f8a11e35", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Implement CV Scoring via AI & Rubric", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:40:02.728997+00:00", "updated_at": "2025-07-17T02:40:02.728997+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "b47fcca8-2200-403e-bc4b-43db81f41d4e", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Set Up Frontend Score Display", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:40:18.812398+00:00", "updated_at": "2025-07-17T02:40:18.812398+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "fc622004-1396-4128-aaf1-a9b0bfe31e01", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Test with sample data ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-17T02:43:05.175199+00:00", "updated_at": "2025-07-17T02:43:09.091026+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "62a81c58-13de-4e65-8864-5767474ef589", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "fix marking rubric sections issue ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-17T02:43:51.304703+00:00", "updated_at": "2025-07-17T02:43:56.007949+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "acc7c5e4-31fe-435d-9971-49692572b93f", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "create impact type table", "description": "change the imapct types to a table that can be changed dynamically. id, impact type, description.\n-then use the able data to populate the impact type", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T07:04:59.400231+00:00", "updated_at": "2025-07-17T08:05:52.445245+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "eb2d97ae-4287-4071-bf9a-9e8542d6b6db", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "retain session view", "description": "when sorting, filtering or grouping the list. if the user clicks into a project - when they go back the list filter, sort, group should survive - at the moment it clears all filters", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T07:06:41.89828+00:00", "updated_at": "2025-07-17T08:19:49.702004+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "2e0ae0f3-bcfb-45e4-a626-19b936ebe3ed", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add list titles on grouping", "description": "when the list is grouped - you should still be able to see the column titles - currently they are not showing in group view", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "to-do", "created_at": "2025-07-17T07:10:35.042435+00:00", "updated_at": "2025-07-17T07:10:35.042435+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}]