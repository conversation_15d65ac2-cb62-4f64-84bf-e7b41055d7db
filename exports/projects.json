[{"id": "e3ead8ad-944e-469a-88f8-02c53ece4734", "name": "AR Automation Agent ", "type": "external", "customer_name": "<PERSON> ", "project_lead": "<PERSON><PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "0414 343 237", "description": "The AR Automation Agent is an AI-powered system designed to streamline accounts receivable processes by automating monthly invoice outreach, capturing customer payment intentions via interactive email buttons, and escalating unresolved cases based on business rules. Integrated with Xero, Microsoft 365, and optionally Fishbowl, the agent reduces manual follow-up time by 80% and provides real-time visibility through a live dashboard. Built on Google Cloud using n8n, Supabase, and Microsoft Graph API, it supports configurable templates, escalation thresholds, and follow-up sequences, with a scalable architecture capable of handling up to 100,000 customers. A phased rollout ensures seamless integration, high adoption, and measurable improvements in AR efficiency and response tracking.", "status": "not-started", "created_at": "2025-07-14T03:35:33.271595+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-20", "end_date": "2025-07-31", "original_end_date": null, "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Integrated Supply Group (ISG)", "prd_document_link": "https://docs.google.com/document/d/1MKvytd5EKM0dkkm11oltJ-nnLNl9hxHyhSGyGDORocE/edit?tab=t.0", "priority_order": 5, "poc_url": "", "status_changed_at": "2025-07-15T07:08:57.770426+00:00", "priority_level": "P2", "effort_estimate": "L", "impact_type": "Revenue", "priority_assigned_at": "2025-07-16T04:26:40.764238+00:00", "last_reviewed_at": "2025-07-16T04:26:40.764238+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "efd7ddfd-876f-45d7-93cc-4b82e071be47"}, {"id": "b04eba31-b738-40f7-9ba8-eed12d725ff2", "name": "Supply Chain Intel Agent ", "type": "external", "customer_name": "<PERSON> ", "project_lead": "<PERSON><PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "", "description": "The ISG Supply Chain Intelligence Agent automates the ingestion and processing of supply chain data from emails, PDFs, and manual entries to replace a manual, reactive system with a proactive, rule-based exception monitoring platform. Built on Google Cloud with n8n, Supabase, and a React frontend, the agent identifies delays, updates dashboards in real time, and automates customer communications. Key features include exception detection, human-in-the-loop escalation, real-time container tracking, and business intelligence dashboards. Integration with Microsoft 365, SharePoint, Xero, and Fishbowl supports comprehensive data flow, with a phased rollout planned over 8 weeks to ensure reliability, accuracy, and user adoption.", "status": "backlog", "created_at": "2025-07-14T03:37:32.171551+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-07-31", "original_end_date": "2025-07-31", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Integrated Supply Group (ISG)", "prd_document_link": "https://docs.google.com/document/d/1yKH90hbz7z4BgzUurmpvQMtFvLbmrkMhCTqt0cCvYMk/edit?tab=t.0", "priority_order": 11, "poc_url": null, "status_changed_at": "2025-07-15T07:08:57.770426+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-15T20:09:47.907074+00:00", "last_reviewed_at": "2025-07-15T20:09:47.907074+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22", "name": "Transcript Analyzer", "type": "external", "customer_name": "<PERSON> & <PERSON>ie <PERSON>", "project_lead": "<PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "", "description": "The Meeting Transcript Analysis Agent is an AI-powered tool designed to automate the extraction of customer pain points from meeting transcripts and match them to predefined business solutions (“Problems We Solve”). Built on Google Cloud and integrated with OneDrive, Supabase, and Vertex AI, the system drastically reduces analysis time from hours to minutes while achieving over 80% accuracy. Targeted at Directors and Product Managers, the tool offers a secure React web interface with drag-and-drop upload, scoring dashboards, and rubric management. Key capabilities include NLP-driven theme analysis, multi-factor scoring, and solution recommendations, with a planned 8-week rollout across MVP, enhancement, and scaling phases.", "status": "not-started", "created_at": "2025-07-14T03:42:24.661612+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-07-30", "original_end_date": null, "project_lead_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Sprouta", "prd_document_link": "https://docs.google.com/document/d/1ZrQeuLWXw_CJinDwROl_UIJ9i_Jf_os7NpwLO4eAv10/edit?tab=t.0#heading=h.eazeg5qpsb7g", "priority_order": 2, "poc_url": "https://sprout-transcript-insights.lovable.app/", "status_changed_at": "2025-07-16T04:15:33.582434+00:00", "priority_level": "P0", "effort_estimate": "XL", "impact_type": "R&D", "priority_assigned_at": "2025-07-16T04:24:27.661676+00:00", "last_reviewed_at": "2025-07-16T04:24:27.661676+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "516d941e-fc49-483f-ac76-79b8f944812f"}, {"id": "159d7520-8b08-4cbb-bd05-e91ea85498fd", "name": "PRD Service", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The PRD Management Service is a centralized tool that helps teams at our AI company create, edit, and track product requirement documents in one place. Instead of using scattered tools like Google Docs or Notion, this service provides a consistent format, version control, and collaboration features. It connects directly to our project management tools, making it easy to link PRDs to tasks and track progress. This ensures everyone—from product to engineering—is aligned, speeding up development and reducing miscommunication.", "status": "backlog", "created_at": "2025-07-14T07:48:35.704504+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-15", "end_date": "2025-07-20", "original_end_date": null, "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1, "poc_url": "", "status_changed_at": "2025-07-16T02:32:03.975963+00:00", "priority_level": "P0", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-16T04:22:55.712262+00:00", "last_reviewed_at": "2025-07-16T04:22:55.712262+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Orbit Setup + CRM Service ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "Should be viewable on internal.dev.twodot.ai\n\nWe will develop a dedicated CRM Service to support customer relationship management across the platform. This service will operate as its own backend module, responsible for storing and managing client records, communication logs, pipeline stages, and task assignments. It will expose a secure API for integration with the main web application and other internal services. The CRM Service will be designed for scalability, supporting role-based access, activity tracking, and future integrations with external tools like email and calendar systems.", "status": "in-progress", "created_at": "2025-07-14T07:54:57.593347+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-09", "end_date": "2025-07-16", "original_end_date": "2025-07-16", "project_lead_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 4, "poc_url": "", "status_changed_at": "2025-07-16T10:02:58.522579+00:00", "priority_level": "P0", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-16T04:32:57.925644+00:00", "last_reviewed_at": "2025-07-16T04:32:57.925644+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Project Dashboard ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "A project dashboard that displays all active projects along with their current progress, attached PRDs, and linked POCs. Each project will be shown with a clear status indicator and ordered by priority to help teams focus on the most important work first. The dashboard will provide a quick overview of what’s in motion, what’s completed, and what’s still in planning, making it easy to track progress and ensure alignment across teams.", "status": "in-progress", "created_at": "2025-07-15T05:52:04.291704+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-10", "end_date": "2025-07-18", "original_end_date": null, "project_lead_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 7, "poc_url": "https://core-project-pulse.lovable.app/", "status_changed_at": "2025-07-17T07:04:37.051226+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-15T20:09:47.907074+00:00", "last_reviewed_at": "2025-07-15T20:09:47.907074+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "90299ea5-9603-4436-b1a4-ccaf3154e391", "name": "Expense Creation Agent", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The Accounts Email Bill Agent for Xero is designed to automate the processing of bill-related emails sent to `<EMAIL>`. It parses PDF and image attachments, extracts key invoice data, applies Australian tax and multi-currency rules, infers appropriate account codes, and drafts bills in Xero. The agent uses learning from user corrections over time and integrates with Orbit’s internal dashboard for visibility, feedback, and full auditability of each step.\n\nTargeted at Twodot’s internal accounts team, the agent supports both real-time and historical email ingestion, emphasizes accuracy over volume (<100 emails/month), and ensures compliance with audit and security standards. It leverages existing Orbit and Xero systems, with core success metrics tied to high parsing accuracy, low review rates, and significant time savings in manual processing.", "status": "not-started", "created_at": "2025-07-15T23:34:23.981896+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-19", "end_date": "2025-07-30", "original_end_date": "2025-07-30", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "https://drive.google.com/file/d/1mxL6B7Mc8FH5K8kmzdAWL950w74erj7n/view?usp=drive_link", "priority_order": 14, "poc_url": "", "status_changed_at": "2025-07-15T23:34:23.981896+00:00", "priority_level": "P4", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-15T23:50:54.442244+00:00", "last_reviewed_at": "2025-07-15T23:50:54.442244+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "e0500b35-0dd9-47ec-8d03-141e394259ed", "name": "Meta Agent Platform", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "AI Agent platform to generate and execute AI Agents.", "status": "backlog", "created_at": "2025-07-16T09:42:06.266509+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-08-14", "original_end_date": "2025-08-14", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "", "status_changed_at": "2025-07-16T09:42:06.266509+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-16T09:42:06.266509+00:00", "last_reviewed_at": "2025-07-16T09:42:06.266509+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "72b99eaf-e3d7-4d3e-910b-8f5e4f206279", "name": "orbit UI/UX templates and components", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON><PERSON><PERSON>", "customer_lead": null, "customer_contact": "", "description": "create the inital figma file to outline the look and feel of the two dot orbit", "status": "in-progress", "created_at": "2025-07-16T22:00:06.095637+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-16", "end_date": "2025-07-17", "original_end_date": "2025-07-17", "project_lead_id": "ee2f08a6-a525-4937-9795-658cebccfed0", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "", "status_changed_at": "2025-07-17T06:06:57.44551+00:00", "priority_level": "P0", "effort_estimate": "S", "impact_type": "Platform", "priority_assigned_at": "2025-07-16T22:01:05.578552+00:00", "last_reviewed_at": "2025-07-16T22:01:05.578552+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Candidate <PERSON><PERSON><PERSON> ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The Candidate Assessor is an automated AI-powered system that processes job applications sent to the company’s Careers email. When a candidate submits their CV and cover letter via email, an n8n workflow captures the incoming message, extracts the email body and CV attachment, and logs the metadata. The workflow then uses AI to determine which job role the candidate is applying for by analyzing the language in the email and cross-referencing it with open roles in a Supabase database. If a matching role is found, the system retrieves the corresponding scoring rubric and passes the candidate’s CV and email content through an AI model, which evaluates the application against key criteria such as technical fit, communication skills, and relevant experience. The model returns a score out of 100, which is stored alongside the candidate’s information and resume in the Supabase backend. The hiring team can then review candidates through a clean, user-friendly frontend built using Lovable.dev, which displays applications, scores, and rubrics in a structured and interactive interface. This end-to-end workflow streamlines candidate screening while ensuring consistent, rubric-based evaluations at scale.", "status": "in-progress", "created_at": "2025-07-17T02:29:26.577145+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-09", "end_date": "2025-07-21", "original_end_date": "2025-07-21", "project_lead_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "https://preview--core-project-pulse.lovable.app/projects/new", "status_changed_at": "2025-07-17T02:29:26.577145+00:00", "priority_level": "P2", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-17T02:34:19.693161+00:00", "last_reviewed_at": "2025-07-17T02:34:19.693161+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}]