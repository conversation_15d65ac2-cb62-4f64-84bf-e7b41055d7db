{"name": "core-project-pulse-backend", "version": "1.0.0", "description": "Backend API for Core Project Pulse - Enterprise Project Management Platform", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "tsx src/server.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/database/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:rbac": "tsx src/scripts/test-rbac.ts", "debug:db": "tsx src/scripts/debug-db.ts", "seed:test": "tsx src/scripts/seed-test-data.ts", "test:integration": "tsx src/scripts/test-integration.ts", "test:oauth": "tsx src/scripts/test-oauth.ts", "export:data": "tsx src/scripts/export-supabase-data.ts", "migrate:data": "tsx src/scripts/migrate-data.ts", "add:real-data": "tsx src/scripts/add-real-data.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "keywords": ["project-management", "express", "typescript", "prisma", "postgresql", "rbac", "o<PERSON>h"], "author": "Core Project Pulse Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}