{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "rootDir": "./src", "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/config/*": ["./config/*"], "@/controllers/*": ["./controllers/*"], "@/middleware/*": ["./middleware/*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/services/*": ["./services/*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"]}, "resolveJsonModule": true, "allowJs": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}