-- RBAC System Seed Data
-- This script populates the roles and permissions for the RBAC system

-- Insert system roles
INSERT INTO roles (id, name, description, is_system_role) VALUES
    ('00000000-0000-0000-0000-000000000001', 'guest', 'Guest user with read-only access to public content', true),
    ('00000000-0000-0000-0000-000000000002', 'member', 'Regular team member with project access', true),
    ('00000000-0000-0000-0000-000000000003', 'admin', 'Administrator with full project management access', true),
    ('00000000-0000-0000-0000-000000000004', 'super_admin', 'Super administrator with system-wide access', true);

-- Insert permissions
INSERT INTO permissions (id, name, resource, action, description) VALUES
    -- Project permissions
    ('10000000-0000-0000-0000-000000000001', 'projects.read', 'projects', 'read', 'View projects'),
    ('10000000-0000-0000-0000-000000000002', 'projects.create', 'projects', 'create', 'Create new projects'),
    ('10000000-0000-0000-0000-000000000003', 'projects.update', 'projects', 'update', 'Update existing projects'),
    ('10000000-0000-0000-0000-000000000004', 'projects.delete', 'projects', 'delete', 'Delete projects'),
    
    -- Task permissions
    ('10000000-0000-0000-0000-000000000011', 'tasks.read', 'tasks', 'read', 'View tasks'),
    ('10000000-0000-0000-0000-000000000012', 'tasks.create', 'tasks', 'create', 'Create new tasks'),
    ('10000000-0000-0000-0000-000000000013', 'tasks.update', 'tasks', 'update', 'Update existing tasks'),
    ('10000000-0000-0000-0000-000000000014', 'tasks.delete', 'tasks', 'delete', 'Delete tasks'),
    
    -- User management permissions
    ('10000000-0000-0000-0000-000000000021', 'users.read', 'users', 'read', 'View user information'),
    ('10000000-0000-0000-0000-000000000022', 'users.create', 'users', 'create', 'Create new users'),
    ('10000000-0000-0000-0000-000000000023', 'users.update', 'users', 'update', 'Update user information'),
    ('10000000-0000-0000-0000-000000000024', 'users.delete', 'users', 'delete', 'Delete users'),
    
    -- Role management permissions
    ('10000000-0000-0000-0000-000000000031', 'roles.read', 'roles', 'read', 'View roles'),
    ('10000000-0000-0000-0000-000000000032', 'roles.assign', 'roles', 'assign', 'Assign roles to users'),
    ('10000000-0000-0000-0000-000000000033', 'roles.manage', 'roles', 'manage', 'Manage role definitions'),
    
    -- Requirements permissions (for future Phase 1)
    ('10000000-0000-0000-0000-000000000041', 'requirements.read', 'requirements', 'read', 'View requirements'),
    ('10000000-0000-0000-0000-000000000042', 'requirements.create', 'requirements', 'create', 'Create requirements'),
    ('10000000-0000-0000-0000-000000000043', 'requirements.update', 'requirements', 'update', 'Update requirements'),
    ('10000000-0000-0000-0000-000000000044', 'requirements.delete', 'requirements', 'delete', 'Delete requirements'),
    
    -- System permissions
    ('10000000-0000-0000-0000-000000000051', 'system.admin', 'system', 'admin', 'System administration access'),
    ('10000000-0000-0000-0000-000000000052', 'system.backup', 'system', 'backup', 'Database backup access'),
    ('10000000-0000-0000-0000-000000000053', 'system.logs', 'system', 'logs', 'System logs access');

-- Assign permissions to roles

-- Guest role (read-only access to public content)
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001'), -- projects.read
    ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000011'); -- tasks.read

-- Member role (project participation)
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001'), -- projects.read
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002'), -- projects.create
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003'), -- projects.update
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000011'), -- tasks.read
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000012'), -- tasks.create
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000013'), -- tasks.update
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000041'), -- requirements.read
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000042'), -- requirements.create
    ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000043'); -- requirements.update

-- Admin role (full project management)
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001'), -- projects.read
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000002'), -- projects.create
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000003'), -- projects.update
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000004'), -- projects.delete
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000011'), -- tasks.read
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000012'), -- tasks.create
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000013'), -- tasks.update
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000014'), -- tasks.delete
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000021'), -- users.read
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000031'), -- roles.read
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000032'), -- roles.assign
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000041'), -- requirements.read
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000042'), -- requirements.create
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000043'), -- requirements.update
    ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000044'); -- requirements.delete

-- Super Admin role (system-wide access)
INSERT INTO role_permissions (role_id, permission_id) 
SELECT '00000000-0000-0000-0000-000000000004', id FROM permissions;
