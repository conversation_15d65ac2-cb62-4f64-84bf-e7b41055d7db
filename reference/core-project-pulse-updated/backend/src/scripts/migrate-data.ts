/**
 * Data Migration Script
 * Migrates data from Supabase to PostgreSQL
 */

import { prisma, connectDatabase } from '../config/database';
import { connectRedis } from '../config/redis';
import { logger } from '../utils/logger';
import fs from 'fs';
import path from 'path';

interface SupabaseProject {
  id: string;
  name: string;
  company_name?: string;
  type: 'internal' | 'external';
  customer_name?: string;
  project_lead: string;
  customer_lead?: string;
  customer_contact?: string;
  description?: string;
  status: string;
  start_date?: string;
  end_date?: string;
  priority_order?: number;
  priority_level?: string;
  effort_estimate?: string;
  created_at: string;
  updated_at: string;
}

interface SupabaseTask {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  assignee?: string;
  due_date?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// Status mapping from Supabase to PostgreSQL
const statusMapping = {
  project: {
    'backlog': 'active',
    'not-started': 'active', 
    'in-progress': 'active',
    'completed': 'completed',
    'archived': 'cancelled',
  },
  task: {
    'to-do': 'to_do',
    'in-progress': 'in_progress',
    'done': 'done',
  },
};

const priorityMapping = {
  'P0': 'P0',
  'P1': 'P1', 
  'P2': 'P2',
  'P3': 'P3',
  'P4': 'P4',
};

const effortMapping = {
  'S': 'S',
  'M': 'M',
  'L': 'L', 
  'XL': 'XL',
};

async function migrateData() {
  try {
    console.log('🔄 Starting Data Migration...\n');

    // Initialize connections
    await connectDatabase();
    await connectRedis();
    console.log('✅ Database and Redis connected\n');

    // Check if data files exist
    const dataDir = path.join(process.cwd(), 'data-export');
    const projectsFile = path.join(dataDir, 'projects.json');
    const tasksFile = path.join(dataDir, 'tasks.json');

    if (!fs.existsSync(projectsFile) || !fs.existsSync(tasksFile)) {
      console.log('❌ Data export files not found!');
      console.log('📋 Please follow these steps to export your data:');
      console.log('');
      console.log('1. Start Docker Desktop');
      console.log('2. Run: npx supabase start');
      console.log('3. Run: npm run export:data');
      console.log('4. Then run this migration script again');
      console.log('');
      console.log('Or manually create data-export/projects.json and data-export/tasks.json files');
      return;
    }

    // Read exported data
    console.log('📖 Reading exported data...');
    const projectsData: SupabaseProject[] = JSON.parse(fs.readFileSync(projectsFile, 'utf8'));
    const tasksData: SupabaseTask[] = JSON.parse(fs.readFileSync(tasksFile, 'utf8'));

    console.log(`Found ${projectsData.length} projects and ${tasksData.length} tasks`);

    // Get or create a default user for data migration
    let defaultUser = await prisma.user.findFirst({
      where: { email: { not: '<EMAIL>' } }
    });

    if (!defaultUser) {
      console.log('⚠️ No real user found, using test user as default');
      defaultUser = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });
    }

    if (!defaultUser) {
      throw new Error('No user found for data migration');
    }

    console.log(`Using user: ${defaultUser.email} as default creator`);

    // Clear existing test data
    console.log('\n🧹 Clearing test data...');
    await prisma.task.deleteMany({
      where: {
        project: {
          name: {
            in: [
              'E-commerce Platform Redesign',
              'Internal Dashboard Development', 
              'Mobile App MVP',
              'Integration Test Project'
            ]
          }
        }
      }
    });

    await prisma.project.deleteMany({
      where: {
        name: {
          in: [
            'E-commerce Platform Redesign',
            'Internal Dashboard Development',
            'Mobile App MVP', 
            'Integration Test Project'
          ]
        }
      }
    });

    console.log('✅ Test data cleared');

    // Migrate projects
    console.log('\n📁 Migrating projects...');
    const projectIdMapping: Record<string, string> = {};

    for (const supabaseProject of projectsData) {
      try {
        // Map status
        const mappedStatus = statusMapping.project[supabaseProject.status as keyof typeof statusMapping.project] || 'active';
        
        // Map priority and effort
        const mappedPriority = priorityMapping[supabaseProject.priority_level as keyof typeof priorityMapping] || 'P3';
        const mappedEffort = effortMapping[supabaseProject.effort_estimate as keyof typeof effortMapping] || 'M';

        const newProject = await prisma.project.create({
          data: {
            name: supabaseProject.name,
            type: supabaseProject.type,
            customerName: supabaseProject.customer_name,
            projectLead: supabaseProject.project_lead,
            customerLead: supabaseProject.customer_lead,
            customerContact: supabaseProject.customer_contact,
            description: supabaseProject.description,
            status: mappedStatus as any,
            priorityLevel: mappedPriority as any,
            effortEstimate: mappedEffort as any,
            createdBy: defaultUser.id,
            createdAt: new Date(supabaseProject.created_at),
            updatedAt: new Date(supabaseProject.updated_at),
          },
        });

        projectIdMapping[supabaseProject.id] = newProject.id;
        console.log(`✅ Migrated project: ${supabaseProject.name}`);
      } catch (error) {
        console.error(`❌ Failed to migrate project ${supabaseProject.name}:`, error);
      }
    }

    // Migrate tasks
    console.log('\n✅ Migrating tasks...');
    let migratedTasks = 0;

    for (const supabaseTask of tasksData) {
      try {
        const newProjectId = projectIdMapping[supabaseTask.project_id];
        if (!newProjectId) {
          console.log(`⚠️ Skipping task ${supabaseTask.name} - project not found`);
          continue;
        }

        // Map task status
        const mappedStatus = statusMapping.task[supabaseTask.status as keyof typeof statusMapping.task] || 'to_do';

        await prisma.task.create({
          data: {
            projectId: newProjectId,
            name: supabaseTask.name,
            description: supabaseTask.description,
            status: mappedStatus as any,
            dueDate: supabaseTask.due_date ? new Date(supabaseTask.due_date) : null,
            priorityLevel: 'P3',
            effortEstimate: 'M',
            createdBy: defaultUser.id,
            createdAt: new Date(supabaseTask.created_at),
            updatedAt: new Date(supabaseTask.updated_at),
          },
        });

        migratedTasks++;
        console.log(`✅ Migrated task: ${supabaseTask.name}`);
      } catch (error) {
        console.error(`❌ Failed to migrate task ${supabaseTask.name}:`, error);
      }
    }

    // Summary
    console.log('\n📊 Migration Summary:');
    const finalProjectCount = await prisma.project.count();
    const finalTaskCount = await prisma.task.count();
    
    console.log(`✅ Total projects in database: ${finalProjectCount}`);
    console.log(`✅ Total tasks in database: ${finalTaskCount}`);
    console.log(`✅ Successfully migrated ${migratedTasks} tasks`);

    console.log('\n🎉 Data migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run the migration
if (require.main === module) {
  migrateData()
    .then(() => {
      console.log('\n✅ Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    });
}

export { migrateData };
