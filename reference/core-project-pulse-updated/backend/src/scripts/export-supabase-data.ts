/**
 * Export Supabase Data Script
 * Exports data from Supabase to JSON files for migration
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Supabase configuration (local instance)
const SUPABASE_URL = 'http://localhost:54321';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function exportSupabaseData() {
  try {
    console.log('📤 Exporting Supabase Data...\n');

    // Create data export directory
    const dataDir = path.join(process.cwd(), 'data-export');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Export projects
    console.log('1. Exporting projects...');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: true });

    if (projectsError) {
      throw new Error(`Failed to export projects: ${projectsError.message}`);
    }

    const projectsFile = path.join(dataDir, 'projects.json');
    fs.writeFileSync(projectsFile, JSON.stringify(projects, null, 2));
    console.log(`✅ Exported ${projects?.length || 0} projects to ${projectsFile}`);

    // Export tasks
    console.log('\n2. Exporting tasks...');
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: true });

    if (tasksError) {
      throw new Error(`Failed to export tasks: ${tasksError.message}`);
    }

    const tasksFile = path.join(dataDir, 'tasks.json');
    fs.writeFileSync(tasksFile, JSON.stringify(tasks, null, 2));
    console.log(`✅ Exported ${tasks?.length || 0} tasks to ${tasksFile}`);

    // Export sub_tasks if they exist
    console.log('\n3. Exporting sub_tasks...');
    const { data: subTasks, error: subTasksError } = await supabase
      .from('sub_tasks')
      .select('*')
      .order('created_at', { ascending: true });

    if (!subTasksError && subTasks && subTasks.length > 0) {
      const subTasksFile = path.join(dataDir, 'sub_tasks.json');
      fs.writeFileSync(subTasksFile, JSON.stringify(subTasks, null, 2));
      console.log(`✅ Exported ${subTasks.length} sub_tasks to ${subTasksFile}`);
    } else {
      console.log('ℹ️ No sub_tasks found or table doesn\'t exist');
    }

    // Export team_members if they exist
    console.log('\n4. Exporting team_members...');
    const { data: teamMembers, error: teamMembersError } = await supabase
      .from('team_members')
      .select('*')
      .order('created_at', { ascending: true });

    if (!teamMembersError && teamMembers && teamMembers.length > 0) {
      const teamMembersFile = path.join(dataDir, 'team_members.json');
      fs.writeFileSync(teamMembersFile, JSON.stringify(teamMembers, null, 2));
      console.log(`✅ Exported ${teamMembers.length} team_members to ${teamMembersFile}`);
    } else {
      console.log('ℹ️ No team_members found or table doesn\'t exist');
    }

    // Export project_integrations if they exist
    console.log('\n5. Exporting project_integrations...');
    const { data: integrations, error: integrationsError } = await supabase
      .from('project_integrations')
      .select('*')
      .order('created_at', { ascending: true });

    if (!integrationsError && integrations && integrations.length > 0) {
      const integrationsFile = path.join(dataDir, 'project_integrations.json');
      fs.writeFileSync(integrationsFile, JSON.stringify(integrations, null, 2));
      console.log(`✅ Exported ${integrations.length} project_integrations to ${integrationsFile}`);
    } else {
      console.log('ℹ️ No project_integrations found or table doesn\'t exist');
    }

    // Create a summary file
    const summary = {
      export_date: new Date().toISOString(),
      total_projects: projects?.length || 0,
      total_tasks: tasks?.length || 0,
      total_sub_tasks: subTasks?.length || 0,
      total_team_members: teamMembers?.length || 0,
      total_integrations: integrations?.length || 0,
      files_created: [
        'projects.json',
        'tasks.json',
        ...(subTasks && subTasks.length > 0 ? ['sub_tasks.json'] : []),
        ...(teamMembers && teamMembers.length > 0 ? ['team_members.json'] : []),
        ...(integrations && integrations.length > 0 ? ['project_integrations.json'] : []),
      ],
    };

    const summaryFile = path.join(dataDir, 'export_summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));

    console.log('\n📊 Export Summary:');
    console.log(`✅ Projects: ${summary.total_projects}`);
    console.log(`✅ Tasks: ${summary.total_tasks}`);
    console.log(`✅ Sub Tasks: ${summary.total_sub_tasks}`);
    console.log(`✅ Team Members: ${summary.total_team_members}`);
    console.log(`✅ Integrations: ${summary.total_integrations}`);

    console.log(`\n📁 Files created in: ${dataDir}`);
    summary.files_created.forEach(file => {
      console.log(`   - ${file}`);
    });

    console.log('\n🎉 Data export completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Review the exported data files');
    console.log('2. Run: npm run migrate:data');

  } catch (error) {
    console.error('❌ Export failed:', error);
    throw error;
  }
}

// Run the export
if (require.main === module) {
  exportSupabaseData()
    .then(() => {
      console.log('\n✅ Export completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Export failed:', error);
      process.exit(1);
    });
}

export { exportSupabaseData };
