/**
 * Debug Database Connection
 * Simple script to test database queries
 */

import { prisma, connectDatabase } from '../config/database';

async function debugDatabase() {
  try {
    console.log('🔍 Debugging Database Connection...\n');

    // Initialize connection
    await connectDatabase();
    console.log('✅ Database connected\n');

    // Test raw query
    console.log('1. Testing raw SQL query...');
    const rawRoles = await prisma.$queryRaw`SELECT * FROM roles LIMIT 5`;
    console.log('Raw query result:', rawRoles);

    // Test Prisma query
    console.log('\n2. Testing Prisma query...');
    const prismaRoles = await prisma.role.findMany({
      take: 5,
    });
    console.log('Prisma query result:', prismaRoles);

    // Test count
    console.log('\n3. Testing count...');
    const roleCount = await prisma.role.count();
    console.log('Role count:', roleCount);

    // Test permissions
    console.log('\n4. Testing permissions...');
    const permissionCount = await prisma.permission.count();
    console.log('Permission count:', permissionCount);

    console.log('\n✅ Debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error);
    throw error;
  }
}

// Run the debug
if (require.main === module) {
  debugDatabase()
    .then(() => {
      console.log('\n✅ Debug successful!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Debug failed:', error);
      process.exit(1);
    });
}

export { debugDatabase };
