/**
 * Add Real Data Script
 * Adds your actual project data to replace test data
 */

import { prisma, connectDatabase } from '../config/database';
import { connectRedis } from '../config/redis';
import { logger } from '../utils/logger';

async function addRealData() {
  try {
    console.log('📊 Adding Real Project Data...\n');

    // Initialize connections
    await connectDatabase();
    await connectRedis();
    console.log('✅ Database and Redis connected\n');

    // Get the authenticated user (you)
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      throw new Error('User not found. Please login first.');
    }

    console.log(`👤 Using user: ${user.name} (${user.email})`);

    // Clear existing test data
    console.log('\n🧹 Clearing test data...');
    await prisma.task.deleteMany({
      where: {
        project: {
          name: {
            in: [
              'E-commerce Platform Redesign',
              'Internal Dashboard Development',
              'Mobile App MVP',
              'Integration Test Project'
            ]
          }
        }
      }
    });

    await prisma.project.deleteMany({
      where: {
        name: {
          in: [
            'E-commerce Platform Redesign',
            'Internal Dashboard Development',
            'Mobile App MVP',
            'Integration Test Project'
          ]
        }
      }
    });

    console.log('✅ Test data cleared');

    // Add your real projects
    console.log('\n📁 Adding real projects...');

    // Project 1: Core Project Pulse Enhancement
    const project1 = await prisma.project.create({
      data: {
        name: 'Core Project Pulse Enhancement',
        type: 'internal',
        projectLead: 'Kavi Koneti',
        description: 'Enhancing the Core Project Pulse system with AI-powered requirements capturing, Google OAuth, and RBAC.',
        status: 'active',
        priorityLevel: 'P0',
        effortEstimate: 'XL',
        createdBy: user.id,
      },
    });

    // Project 2: TwoDot AI Platform
    const project2 = await prisma.project.create({
      data: {
        name: 'TwoDot AI Platform',
        type: 'internal',
        projectLead: 'Kavi Koneti',
        description: 'AI-powered platform for intelligent automation and decision making.',
        status: 'active',
        priorityLevel: 'P1',
        effortEstimate: 'XL',
        createdBy: user.id,
      },
    });

    // Project 3: Client Management System
    const project3 = await prisma.project.create({
      data: {
        name: 'Client Management System',
        type: 'external',
        customerName: 'Enterprise Client',
        projectLead: 'Kavi Koneti',
        customerLead: 'Client Lead',
        customerContact: '<EMAIL>',
        description: 'Comprehensive client management and CRM system.',
        status: 'active',
        priorityLevel: 'P2',
        effortEstimate: 'L',
        createdBy: user.id,
      },
    });

    console.log(`✅ Created 3 real projects`);

    // Add tasks for Project 1 (Core Project Pulse Enhancement)
    console.log('\n✅ Adding tasks...');

    const tasks = [
      // Phase 0 tasks
      {
        projectId: project1.id,
        name: 'PostgreSQL + Docker Setup',
        description: 'Set up PostgreSQL database with Docker Compose',
        status: 'done',
        priorityLevel: 'P0',
        effortEstimate: 'M',
      },
      {
        projectId: project1.id,
        name: 'Backend Architecture Setup',
        description: 'Express.js + TypeScript + Prisma backend setup',
        status: 'done',
        priorityLevel: 'P0',
        effortEstimate: 'L',
      },
      {
        projectId: project1.id,
        name: 'Google OAuth Integration',
        description: 'Implement Google OAuth 2.0 authentication flow',
        status: 'done',
        priorityLevel: 'P0',
        effortEstimate: 'L',
      },
      {
        projectId: project1.id,
        name: 'RBAC System Implementation',
        description: 'Role-based access control with permissions',
        status: 'done',
        priorityLevel: 'P0',
        effortEstimate: 'XL',
      },
      {
        projectId: project1.id,
        name: 'Frontend API Migration',
        description: 'Migrate frontend from Supabase to new backend',
        status: 'in_progress',
        priorityLevel: 'P0',
        effortEstimate: 'L',
      },
      {
        projectId: project1.id,
        name: 'Data Migration',
        description: 'Migrate existing data from Supabase to PostgreSQL',
        status: 'in_progress',
        priorityLevel: 'P0',
        effortEstimate: 'M',
      },
      // Phase 1 tasks
      {
        projectId: project1.id,
        name: 'Requirements Capturing Module',
        description: 'AI-powered multi-modal requirements capturing',
        status: 'to_do',
        priorityLevel: 'P1',
        effortEstimate: 'XL',
      },
      {
        projectId: project1.id,
        name: 'OpenAI Integration',
        description: 'Integrate OpenAI APIs for AI processing',
        status: 'to_do',
        priorityLevel: 'P1',
        effortEstimate: 'L',
      },

      // TwoDot AI Platform tasks
      {
        projectId: project2.id,
        name: 'AI Model Architecture',
        description: 'Design and implement core AI model architecture',
        status: 'in_progress',
        priorityLevel: 'P1',
        effortEstimate: 'XL',
      },
      {
        projectId: project2.id,
        name: 'Data Pipeline Setup',
        description: 'Set up data ingestion and processing pipeline',
        status: 'to_do',
        priorityLevel: 'P1',
        effortEstimate: 'L',
      },

      // Client Management System tasks
      {
        projectId: project3.id,
        name: 'Database Schema Design',
        description: 'Design database schema for client management',
        status: 'done',
        priorityLevel: 'P2',
        effortEstimate: 'M',
      },
      {
        projectId: project3.id,
        name: 'API Development',
        description: 'Develop REST APIs for client operations',
        status: 'in_progress',
        priorityLevel: 'P2',
        effortEstimate: 'L',
      },
      {
        projectId: project3.id,
        name: 'Frontend Dashboard',
        description: 'Create client management dashboard',
        status: 'to_do',
        priorityLevel: 'P2',
        effortEstimate: 'L',
      },
    ];

    for (const taskData of tasks) {
      await prisma.task.create({
        data: {
          ...taskData,
          createdBy: user.id,
        } as any,
      });
    }

    console.log(`✅ Created ${tasks.length} tasks`);

    // Summary
    console.log('\n📊 Real Data Summary:');
    const projectCount = await prisma.project.count();
    const taskCount = await prisma.task.count();
    const userCount = await prisma.user.count();

    console.log(`✅ Total projects: ${projectCount}`);
    console.log(`✅ Total tasks: ${taskCount}`);
    console.log(`✅ Total users: ${userCount}`);

    console.log('\n🎉 Real data added successfully!');
    console.log('🔄 Refresh your frontend to see the new data');

  } catch (error) {
    console.error('❌ Failed to add real data:', error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  addRealData()
    .then(() => {
      console.log('\n✅ Real data added successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Failed to add real data:', error);
      process.exit(1);
    });
}

export { addRealData };
