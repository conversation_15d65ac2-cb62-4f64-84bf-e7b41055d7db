/**
 * Project Routes
 * Handles project management operations
 */

import { Router } from 'express';
import { asyncHand<PERSON>, createError } from '@/middleware/errorHandler';
import {
  requireProjectRead,
  requireProjectCreate,
  requireProjectUpdate,
  requireProjectDelete,
} from '@/middleware/rbac';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @route   GET /api/v1/projects
 * @desc    Get all projects
 * @access  Private (requires projects.read permission)
 */
router.get('/', requireProjectRead, asyncHandler(async (req, res) => {
  const { status, limit, offset } = req.query;

  const where: any = {};
  if (status) {
    where.status = status as string;
  }

  const projects = await prisma.project.findMany({
    where,
    include: {
      tasks: {
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          assigneeId: true,
          dueDate: true,
          priorityLevel: true,
          effortEstimate: true,
          createdAt: true,
          updatedAt: true,
        },
      },
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: [
      { priorityLevel: 'asc' },
      { createdAt: 'desc' },
    ],
    take: limit ? parseInt(limit as string) : undefined,
    skip: offset ? parseInt(offset as string) : undefined,
  });

  res.status(200).json({
    status: 'success',
    data: projects,
  });
}));

/**
 * @route   GET /api/v1/projects/:id
 * @desc    Get project by ID
 * @access  Private (requires projects.read permission)
 */
router.get('/:id', requireProjectRead, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await prisma.project.findUnique({
    where: { id },
    include: {
      tasks: {
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          assigneeId: true,
          dueDate: true,
          priorityLevel: true,
          effortEstimate: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: 'asc' },
      },
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  res.status(200).json({
    status: 'success',
    data: project,
  });
}));

/**
 * @route   POST /api/v1/projects
 * @desc    Create new project
 * @access  Private (requires projects.create permission)
 */
router.post('/', requireProjectCreate, asyncHandler(async (req, res) => {
  const {
    name,
    type,
    customerName,
    projectLead,
    customerLead,
    customerContact,
    description,
    status,
    priorityLevel,
    effortEstimate,
  } = req.body;

  if (!name || !type || !projectLead) {
    throw createError('Name, type, and project lead are required', 400);
  }

  const project = await prisma.project.create({
    data: {
      name,
      type,
      customerName,
      projectLead,
      customerLead,
      customerContact,
      description,
      status: status || 'active',
      priorityLevel: priorityLevel || 'P3',
      effortEstimate: effortEstimate || 'M',
      createdBy: req.user!.id,
    },
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  logger.info(`Project created: ${project.name}`, {
    projectId: project.id,
    createdBy: req.user!.id,
  });

  res.status(201).json({
    status: 'success',
    data: project,
  });
}));

/**
 * @route   PUT /api/v1/projects/:id
 * @desc    Update project
 * @access  Private (requires projects.update permission)
 */
router.put('/:id', requireProjectUpdate, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // Remove fields that shouldn't be updated directly
  delete updateData.id;
  delete updateData.createdAt;
  delete updateData.createdBy;

  const project = await prisma.project.update({
    where: { id },
    data: updateData,
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  logger.info(`Project updated: ${project.name}`, {
    projectId: project.id,
    updatedBy: req.user!.id,
  });

  res.status(200).json({
    status: 'success',
    data: project,
  });
}));

/**
 * @route   DELETE /api/v1/projects/:id
 * @desc    Delete project
 * @access  Private (requires projects.delete permission)
 */
router.delete('/:id', requireProjectDelete, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if project exists
  const project = await prisma.project.findUnique({
    where: { id },
    select: { id: true, name: true },
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  // Delete project (this will cascade delete tasks due to foreign key constraints)
  await prisma.project.delete({
    where: { id },
  });

  logger.info(`Project deleted: ${project.name}`, {
    projectId: project.id,
    deletedBy: req.user!.id,
  });

  res.status(200).json({
    status: 'success',
    message: 'Project deleted successfully',
  });
}));

export default router;
