/**
 * Authentication Routes
 * Handles user authentication, registration, and OAuth
 */

import { Router } from 'express';
import passport from '@/config/passport';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { authMiddleware } from '@/middleware/auth';
import { authService } from '@/services/authService';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', asyncHandler(async (req, res) => {
  // TODO: Implement user registration
  res.status(501).json({
    status: 'error',
    message: 'Registration endpoint not implemented yet',
  });
}));

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', asyncHandler(async (req, res) => {
  // TODO: Implement user login
  res.status(501).json({
    status: 'error',
    message: 'Login endpoint not implemented yet',
  });
}));

/**
 * @route   GET /api/v1/auth/google
 * @desc    Google OAuth login
 * @access  Public
 */
router.get('/google',
  passport.authenticate('google', {
    scope: ['profile', 'email'],
    session: false
  })
);

/**
 * @route   GET /api/v1/auth/google/callback
 * @desc    Google OAuth callback
 * @access  Public
 */
router.get('/google/callback',
  passport.authenticate('google', {
    session: false,
    failureRedirect: `${process.env.FRONTEND_URL}/auth/error`
  }),
  asyncHandler(async (req, res) => {
    try {
      const authResult = req.user as any;

      if (!authResult) {
        throw createError('Authentication failed', 401);
      }

      const { user, tokens } = authResult;

      // Redirect to frontend with tokens
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
      const redirectUrl = `${frontendUrl}/auth/callback?token=${tokens.accessToken}&refresh=${tokens.refreshToken}`;

      res.redirect(redirectUrl);
    } catch (error) {
      logger.error('Google OAuth callback error:', error);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
      res.redirect(`${frontendUrl}/auth/error`);
    }
  })
);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw createError('Refresh token required', 400);
  }

  const result = await authService.refreshAccessToken(refreshToken);

  res.status(200).json({
    status: 'success',
    data: result,
  });
}));

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout', authMiddleware, asyncHandler(async (req, res) => {
  await authService.logout(req.user!.id);

  res.status(200).json({
    status: 'success',
    message: 'Logged out successfully',
  });
}));

/**
 * @route   GET /api/v1/auth/me
 * @desc    Get current user
 * @access  Private
 */
router.get('/me', authMiddleware, asyncHandler(async (req, res) => {
  res.status(200).json({
    status: 'success',
    data: {
      user: req.user,
    },
  });
}));

export default router;
