/**
 * RBAC Management Routes
 * Handles role and permission management operations
 */

import { Router } from 'express';
import { asyncHand<PERSON>, createError } from '@/middleware/errorHandler';
import { 
  requireRoleRead,
  requireRoleAssign,
  requireRoleManage,
  requireAdmin,
} from '@/middleware/rbac';
import { rbacService } from '@/services/rbacService';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * @route   GET /api/v1/rbac/roles
 * @desc    Get all roles
 * @access  Private (requires roles.read permission)
 */
router.get('/roles', requireRoleRead, asyncHandler(async (req, res) => {
  const roles = await rbacService.getAllRoles();
  
  res.status(200).json({
    status: 'success',
    data: {
      roles,
    },
  });
}));

/**
 * @route   GET /api/v1/rbac/permissions
 * @desc    Get all permissions
 * @access  Private (requires roles.read permission)
 */
router.get('/permissions', requireR<PERSON>Read, asyncHandler(async (req, res) => {
  const permissions = await rbacService.getAllPermissions();
  
  res.status(200).json({
    status: 'success',
    data: {
      permissions,
    },
  });
}));

/**
 * @route   GET /api/v1/rbac/users/:userId/permissions
 * @desc    Get user permissions
 * @access  Private (Admin only)
 */
router.get('/users/:userId/permissions', requireAdmin, asyncHandler(async (req, res) => {
  const { userId } = req.params;
  
  const userPermissions = await rbacService.getUserPermissions(userId);
  
  res.status(200).json({
    status: 'success',
    data: {
      userPermissions,
    },
  });
}));

/**
 * @route   POST /api/v1/rbac/users/:userId/roles
 * @desc    Assign role to user
 * @access  Private (requires roles.assign permission)
 */
router.post('/users/:userId/roles', requireRoleAssign, asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { roleName } = req.body;
  
  if (!roleName) {
    throw createError('Role name is required', 400);
  }
  
  await rbacService.assignRole(userId, roleName, req.user!.id);
  
  logger.info(`Role '${roleName}' assigned to user ${userId} by ${req.user!.email}`, {
    userId,
    roleName,
    assignedBy: req.user!.id,
  });
  
  res.status(200).json({
    status: 'success',
    message: `Role '${roleName}' assigned successfully`,
  });
}));

/**
 * @route   DELETE /api/v1/rbac/users/:userId/roles/:roleName
 * @desc    Remove role from user
 * @access  Private (requires roles.assign permission)
 */
router.delete('/users/:userId/roles/:roleName', requireRoleAssign, asyncHandler(async (req, res) => {
  const { userId, roleName } = req.params;
  
  await rbacService.removeRole(userId, roleName);
  
  logger.info(`Role '${roleName}' removed from user ${userId} by ${req.user!.email}`, {
    userId,
    roleName,
    removedBy: req.user!.id,
  });
  
  res.status(200).json({
    status: 'success',
    message: `Role '${roleName}' removed successfully`,
  });
}));

/**
 * @route   POST /api/v1/rbac/check-permission
 * @desc    Check if current user has specific permission
 * @access  Private
 */
router.post('/check-permission', asyncHandler(async (req, res) => {
  const { permission } = req.body;
  
  if (!permission) {
    throw createError('Permission name is required', 400);
  }
  
  const hasPermission = await rbacService.hasPermission(req.user!.id, permission);
  
  res.status(200).json({
    status: 'success',
    data: {
      hasPermission,
      permission,
    },
  });
}));

/**
 * @route   POST /api/v1/rbac/check-role
 * @desc    Check if current user has specific role
 * @access  Private
 */
router.post('/check-role', asyncHandler(async (req, res) => {
  const { role } = req.body;
  
  if (!role) {
    throw createError('Role name is required', 400);
  }
  
  const hasRole = await rbacService.hasRole(req.user!.id, role);
  
  res.status(200).json({
    status: 'success',
    data: {
      hasRole,
      role,
    },
  });
}));

/**
 * @route   DELETE /api/v1/rbac/cache
 * @desc    Clear RBAC cache
 * @access  Private (Admin only)
 */
router.delete('/cache', requireAdmin, asyncHandler(async (req, res) => {
  await rbacService.clearAllCaches();
  
  logger.info(`RBAC cache cleared by ${req.user!.email}`, {
    clearedBy: req.user!.id,
  });
  
  res.status(200).json({
    status: 'success',
    message: 'RBAC cache cleared successfully',
  });
}));

export default router;
