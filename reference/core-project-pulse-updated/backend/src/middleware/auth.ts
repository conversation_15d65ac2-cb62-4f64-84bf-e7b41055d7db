/**
 * Authentication Middleware
 * Handles JWT token verification and user authentication
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '@/config/database';
import { createError, asyncHandler } from './errorHandler';
import { logger } from '@/utils/logger';
import { loadUserPermissions } from './rbac';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name: string;
        role: string;
        permissions?: string[];
      };
    }
  }
}

interface JWTPayload {
  id: string;
  email: string;
  name: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Extract token from request headers
 */
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return null;
};

/**
 * Verify JWT token
 */
const verifyToken = (token: string): JWTPayload => {
  const secret = process.env.JWT_SECRET;
  
  if (!secret) {
    throw createError('JWT secret not configured', 500);
  }
  
  try {
    return jwt.verify(token, secret) as JWTPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw createError('Token expired', 401);
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw createError('Invalid token', 401);
    }
    throw createError('Token verification failed', 401);
  }
};

/**
 * Main authentication middleware
 */
export const authMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  // Extract token from request
  const token = extractToken(req);
  
  if (!token) {
    throw createError('Access token required', 401);
  }
  
  // Verify token
  const payload = verifyToken(token);
  
  // Get user from database to ensure they still exist and are active
  const user = await prisma.user.findUnique({
    where: { id: payload.id },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      isActive: true,
    },
  });
  
  if (!user) {
    throw createError('User not found', 401);
  }
  
  if (!user.isActive) {
    throw createError('Account deactivated', 401);
  }
  
  // Attach user to request
  req.user = {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
  };
  
  // Log authentication
  logger.debug(`User authenticated: ${user.email}`, {
    userId: user.id,
    role: user.role,
    route: req.originalUrl,
  });

  // Load user permissions
  await loadUserPermissions(req, res, () => {});

  next();
});

/**
 * Optional authentication middleware
 * Doesn't throw error if no token provided
 */
export const optionalAuthMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const token = extractToken(req);
  
  if (!token) {
    return next();
  }
  
  try {
    const payload = verifyToken(token);
    
    const user = await prisma.user.findUnique({
      where: { id: payload.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
      },
    });
    
    if (user && user.isActive) {
      req.user = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
      };
    }
  } catch (error) {
    // Ignore authentication errors in optional middleware
    logger.debug('Optional auth failed:', error);
  }
  
  next();
});

/**
 * Role-based authorization middleware
 */
export const requireRole = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw createError('Authentication required', 401);
    }
    
    if (!roles.includes(req.user.role)) {
      throw createError('Insufficient permissions', 403);
    }
    
    next();
  };
};

/**
 * Admin role requirement middleware
 */
export const requireAdmin = requireRole('admin', 'super_admin');

/**
 * Super admin role requirement middleware
 */
export const requireSuperAdmin = requireRole('super_admin');

/**
 * Check if user owns resource or has admin privileges
 */
export const requireOwnershipOrAdmin = (userIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw createError('Authentication required', 401);
    }
    
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    // Allow if user owns the resource or is admin/super_admin
    if (req.user.id === resourceUserId || ['admin', 'super_admin'].includes(req.user.role)) {
      return next();
    }
    
    throw createError('Access denied', 403);
  };
};

/**
 * Rate limiting by user
 */
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.id;
    const now = Date.now();
    const userRequests = requests.get(userId);
    
    if (!userRequests || now > userRequests.resetTime) {
      requests.set(userId, { count: 1, resetTime: now + windowMs });
      return next();
    }
    
    if (userRequests.count >= maxRequests) {
      throw createError('Too many requests', 429);
    }
    
    userRequests.count++;
    next();
  };
};
