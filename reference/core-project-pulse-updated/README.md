# Core Project Pulse

A comprehensive project management system with SDLC-based organization and real-time collaboration features.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker Desktop (for local Supabase)
- Git

### Development Setup

#### Option 1: Local Development (Recommended)
```bash
# 1. Install dependencies
npm install

# 2. Start Docker Desktop

# 3. Set up local Supabase with real data
npm run supabase:setup

# 4. Switch to local environment and start development
npm run dev:local
```

#### Option 2: Production Database (Use with caution)
```bash
# 1. Install dependencies
npm install

# 2. Switch to production environment and start development
npm run dev:prod
```

## 🗄️ Database Management

### Local Supabase Commands
```bash
# Start local Supabase
npm run supabase:start

# Stop local Supabase
npm run supabase:stop

# Reset local database (re-runs migrations)
npm run supabase:reset

# Full setup (start + migrate + seed)
npm run supabase:setup
```

### Environment Switching
```bash
# Switch to local development
npm run env:local

# Switch to production (be careful!)
npm run env:prod
```

### Data Management
```bash
# Export data from remote Supabase
npm run export-data

# The exported data is saved to:
# - supabase/seed/remote-data.sql (SQL format)
# - supabase/seed/remote-data.json (JSON backup)
```

## 🏗️ Architecture

### SDLC-Based Organization
The application organizes projects into Software Development Life Cycle phases:

1. **Requirements** - User stories, acceptance criteria, business requirements
2. **Planning** - Timeline, milestones, resource allocation, risk assessment
3. **Tasks** - Development tasks, sprint management, progress tracking
4. **Implementation** - Code repositories, technical specs, environments
5. **Testing** - Test plans, test cases, bug tracking, QA processes
6. **Documentation** - Technical docs, user manuals, API documentation
7. **Deployment** - Deployment checklists, environment configs, releases
8. **Monitoring** - Performance metrics, user feedback, maintenance

### Technology Stack
- **Frontend**: React 18, TypeScript, Tailwind CSS, Radix UI
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **State Management**: TanStack Query
- **Routing**: React Router v6
- **UI Components**: shadcn/ui, Lucide Icons

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run dev:local` - Start with local Supabase
- `npm run dev:prod` - Start with production Supabase
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

### Environment Configuration
The app uses environment variables for Supabase configuration:

```env
# Local Development (.env.local)
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_ENVIRONMENT=local

# Production (.env.production)
VITE_SUPABASE_URL=https://hhdxsyocvrmyxcuiqtzn.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_ENVIRONMENT=production
```

## 📊 Features

### Current Features
- ✅ Project management with priority system
- ✅ Task management with subtasks
- ✅ Team member management
- ✅ SDLC-based project organization
- ✅ Real-time updates
- ✅ Responsive design
- ✅ Local development environment

### Planned Features
- 🔄 Requirements management
- 🔄 Planning and milestone tracking
- 🔄 Implementation tracking
- 🔄 Testing management
- 🔄 Documentation system
- 🔄 Deployment automation
- 🔄 Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Set up local development environment: `npm run supabase:setup`
4. Make your changes and test locally: `npm run dev:local`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

---

**Original Lovable Project**: https://lovable.dev/projects/9f2806a4-3739-4638-b549-9a474b7fa16f
