#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create a complete database dump (schema + data) from remote Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Remote Supabase configuration
const REMOTE_SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const REMOTE_SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

const supabase = createClient(REMOTE_SUPABASE_URL, REMOTE_SUPABASE_KEY);

// Table schemas based on the existing structure
const TABLE_SCHEMAS = {
  team_members: `
CREATE TABLE IF NOT EXISTS public.team_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  role TEXT NOT NULL,
  department TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  impact_types: `
CREATE TABLE IF NOT EXISTS public.impact_types (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  label TEXT NOT NULL,
  description TEXT,
  color TEXT,
  bg_color TEXT,
  border_color TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  sort_order INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  projects: `
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('internal', 'external')),
  customer_name TEXT,
  project_lead TEXT NOT NULL,
  customer_lead TEXT,
  customer_contact TEXT,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'on_hold', 'archived')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  start_date DATE,
  end_date DATE,
  budget DECIMAL,
  progress INTEGER DEFAULT 0,
  requirements_status TEXT,
  prd_document_link TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  tasks: `
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  assignee TEXT NOT NULL,
  due_date DATE,
  status TEXT NOT NULL DEFAULT 'to-do' CHECK (status IN ('to-do', 'in-progress', 'done')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  estimated_hours INTEGER,
  actual_hours INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  sub_tasks: `
CREATE TABLE IF NOT EXISTS public.sub_tasks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  assignee TEXT NOT NULL,
  due_date DATE,
  status TEXT NOT NULL DEFAULT 'to-do' CHECK (status IN ('to-do', 'in-progress', 'done')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  priority_history: `
CREATE TABLE IF NOT EXISTS public.priority_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  old_priority TEXT,
  new_priority TEXT NOT NULL,
  reason TEXT,
  changed_by TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  priority_rules: `
CREATE TABLE IF NOT EXISTS public.priority_rules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  from_priority TEXT NOT NULL,
  to_priority TEXT NOT NULL,
  max_days INTEGER NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`,

  project_integrations: `
CREATE TABLE IF NOT EXISTS public.project_integrations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  integration_type TEXT NOT NULL CHECK (integration_type IN ('google_drive', 'slack_webhook')),
  integration_url TEXT NOT NULL,
  integration_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);`
};

// Tables to export (in dependency order)
const TABLES_TO_EXPORT = [
  'team_members',
  'impact_types',
  'projects',
  'tasks',
  'sub_tasks',
  'priority_history',
  'priority_rules',
  'project_integrations'
];

async function exportTable(tableName) {
  console.log(`Exporting ${tableName}...`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');

    if (error) {
      console.error(`Error exporting ${tableName}:`, error);
      return null;
    }

    if (!data || data.length === 0) {
      console.log(`No data found in ${tableName}`);
      return null;
    }

    console.log(`Found ${data.length} records in ${tableName}`);
    return data;
  } catch (err) {
    console.error(`Exception exporting ${tableName}:`, err);
    return null;
  }
}

function generateInsertSQL(tableName, data) {
  if (!data || data.length === 0) {
    return `-- No data to insert for ${tableName}\n`;
  }

  const columns = Object.keys(data[0]);
  let sql = `-- Insert data for ${tableName}\n`;
  sql += `TRUNCATE TABLE ${tableName} RESTART IDENTITY CASCADE;\n`;
  
  // Generate INSERT statements
  for (const row of data) {
    const values = columns.map(col => {
      const value = row[col];
      if (value === null) return 'NULL';
      if (typeof value === 'string') {
        // Escape single quotes
        return `'${value.replace(/'/g, "''")}'`;
      }
      if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
      if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      return value;
    });
    
    sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
  }
  
  sql += '\n';
  return sql;
}

async function createCompleteDump() {
  console.log('Creating complete database dump (schema + data)...');
  
  // Create export directory
  const exportDir = path.join(process.cwd(), 'supabase', 'seed');
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true });
  }

  let completeDump = `-- Complete Database Dump (Schema + Data)
-- Generated on: ${new Date().toISOString()}
-- Source: ${REMOTE_SUPABASE_URL}

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tables (schema)
`;

  // Add table schemas
  for (const tableName of TABLES_TO_EXPORT) {
    if (TABLE_SCHEMAS[tableName]) {
      completeDump += `\n-- Schema for ${tableName}\n`;
      completeDump += TABLE_SCHEMAS[tableName] + '\n';
    }
  }

  completeDump += `\n-- Insert data\nSET session_replication_role = replica;\n\n`;

  // Export data for each table
  for (const tableName of TABLES_TO_EXPORT) {
    const data = await exportTable(tableName);
    const sql = generateInsertSQL(tableName, data);
    completeDump += sql;
  }

  completeDump += `\nSET session_replication_role = DEFAULT;\n`;

  // Write complete dump to file
  const dumpFile = path.join(exportDir, 'complete-dump.sql');
  fs.writeFileSync(dumpFile, completeDump);
  
  console.log(`Complete database dump created: ${dumpFile}`);
  console.log('✅ Dump includes both schema and data!');
  
  return dumpFile;
}

// Run the dump creation
createCompleteDump().catch(console.error);
