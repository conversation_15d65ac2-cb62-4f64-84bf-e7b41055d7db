#!/usr/bin/env node

/**
 * <PERSON>ript to export data from remote Supabase instance
 * This will create SQL files that can be imported into local Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Remote Supabase configuration
const REMOTE_SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const REMOTE_SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

const supabase = createClient(REMOTE_SUPABASE_URL, REMOTE_SUPABASE_KEY);

// Tables to export (in dependency order)
const TABLES_TO_EXPORT = [
  'team_members',
  'impact_types',
  'projects',
  'tasks',
  'sub_tasks',
  'priority_history',
  'priority_rules',
  'project_integrations'
];

async function exportTable(tableName) {
  console.log(`Exporting ${tableName}...`);

  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');

    if (error) {
      console.error(`Error exporting ${tableName}:`, error);
      return null;
    }

    if (!data || data.length === 0) {
      console.log(`No data found in ${tableName}`);
      return null;
    }

    console.log(`Found ${data.length} records in ${tableName}`);
    return data;
  } catch (err) {
    console.error(`Exception exporting ${tableName}:`, err);
    return null;
  }
}

async function getTableSchema(tableName) {
  console.log(`Getting schema for ${tableName}...`);

  try {
    // Get column information
    const { data: columns, error } = await supabase
      .rpc('get_table_schema', { table_name: tableName });

    if (error) {
      console.log(`Could not get schema for ${tableName}, will use basic structure`);
      return null;
    }

    return columns;
  } catch (err) {
    console.log(`Could not get schema for ${tableName}, will use basic structure`);
    return null;
  }
}

function generateInsertSQL(tableName, data) {
  if (!data || data.length === 0) {
    return `-- No data to insert for ${tableName}\n`;
  }

  const columns = Object.keys(data[0]);
  let sql = `-- Insert data for ${tableName}\n`;
  sql += `DELETE FROM ${tableName};\n`;
  
  // Generate INSERT statements
  for (const row of data) {
    const values = columns.map(col => {
      const value = row[col];
      if (value === null) return 'NULL';
      if (typeof value === 'string') {
        // Escape single quotes
        return `'${value.replace(/'/g, "''")}'`;
      }
      if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
      if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      if (typeof value === 'object') return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      return value;
    });
    
    sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
  }
  
  sql += '\n';
  return sql;
}

async function exportAllData() {
  console.log('Starting data export from remote Supabase...');
  
  // Create export directory
  const exportDir = path.join(process.cwd(), 'supabase', 'seed');
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true });
  }

  let allSQL = `-- Exported data from remote Supabase
-- Generated on: ${new Date().toISOString()}
-- 
-- This file contains all the data from the remote Supabase instance
-- Run this after setting up local Supabase to populate with real data

SET session_replication_role = replica;

`;

  // Export each table
  for (const tableName of TABLES_TO_EXPORT) {
    const data = await exportTable(tableName);
    const sql = generateInsertSQL(tableName, data);
    allSQL += sql;
  }

  allSQL += `
SET session_replication_role = DEFAULT;

-- Update sequences to prevent ID conflicts
SELECT setval(pg_get_serial_sequence('team_members', 'id'), COALESCE(MAX(id), 1)) FROM team_members;
SELECT setval(pg_get_serial_sequence('impact_types', 'id'), COALESCE(MAX(id), 1)) FROM impact_types;
-- Add more sequence updates as needed for other tables with serial IDs
`;

  // Write to file
  const exportFile = path.join(exportDir, 'remote-data.sql');
  fs.writeFileSync(exportFile, allSQL);
  
  console.log(`Data exported to: ${exportFile}`);
  console.log('Export completed successfully!');
  
  // Also create a JSON backup
  const jsonFile = path.join(exportDir, 'remote-data.json');
  const jsonData = {};
  
  for (const tableName of TABLES_TO_EXPORT) {
    const data = await exportTable(tableName);
    jsonData[tableName] = data;
  }
  
  fs.writeFileSync(jsonFile, JSON.stringify(jsonData, null, 2));
  console.log(`JSON backup created: ${jsonFile}`);
}

// Run the export
exportAllData().catch(console.error);
