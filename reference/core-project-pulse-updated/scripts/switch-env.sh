#!/bin/bash

# Environment Switcher for Supabase Configuration
# Usage: ./scripts/switch-env.sh [local|production]

set -e

ENV=${1:-"local"}

case $ENV in
  "local")
    echo "🔄 Switching to LOCAL Supabase environment..."
    cp .env.local .env
    echo "✅ Switched to local environment"
    echo "📋 Local Supabase URL: http://localhost:54321"
    echo "🎯 Run 'npm run dev' to start with local Supabase"
    ;;
  "production"|"prod")
    echo "🔄 Switching to PRODUCTION Supabase environment..."
    cp .env.production .env
    echo "✅ Switched to production environment"
    echo "📋 Production Supabase URL: https://hhdxsyocvrmyxcuiqtzn.supabase.co"
    echo "⚠️  You are now connected to the LIVE database!"
    ;;
  *)
    echo "❌ Invalid environment: $ENV"
    echo "Usage: $0 [local|production]"
    exit 1
    ;;
esac

echo ""
echo "🔍 Current configuration:"
if [ -f ".env" ]; then
  grep "VITE_SUPABASE_URL" .env || echo "No VITE_SUPABASE_URL found"
  grep "VITE_ENVIRONMENT" .env || echo "No VITE_ENVIRONMENT found"
else
  echo "No .env file found"
fi
