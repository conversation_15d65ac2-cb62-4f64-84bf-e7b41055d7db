-- Update existing task statuses to new values
UPDATE public.tasks 
SET status = CASE 
    WHEN status = 'to-do' THEN 'not-started'
    WHEN status = 'done' THEN 'completed'
    ELSE status -- 'in-progress' stays the same
END;

-- Update existing sub_task statuses to new values  
UPDATE public.sub_tasks 
SET status = CASE 
    WHEN status = 'to-do' THEN 'not-started'
    WHEN status = 'done' THEN 'completed'
    ELSE status -- 'in-progress' stays the same
END;

-- Update the calculate_project_progress function to handle new status values
CREATE OR REPLACE FUNCTION public.calculate_project_progress(project_uuid uuid)
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
DECLARE
  total_tasks INTEGER;
  completed_tasks INTEGER;
  total_subtasks INTEGER;
  completed_subtasks INTEGER;
  progress INTEGER;
BEGIN
  -- Count total and completed tasks (now using 'completed' instead of 'done')
  SELECT COUNT(*), COUNT(CASE WHEN status = 'completed' THEN 1 END)
  INTO total_tasks, completed_tasks
  FROM public.tasks
  WHERE project_id = project_uuid;
  
  -- Count total and completed subtasks (now using 'completed' instead of 'done')
  SELECT COUNT(st.*), COUNT(CASE WHEN st.status = 'completed' THEN 1 END)
  INTO total_subtasks, completed_subtasks
  FROM public.sub_tasks st
  JOIN public.tasks t ON st.task_id = t.id
  WHERE t.project_id = project_uuid;
  
  -- Calculate progress percentage
  IF (total_tasks + total_subtasks) = 0 THEN
    RETURN 0;
  ELSE
    progress := ROUND(((completed_tasks + completed_subtasks) * 100.0) / (total_tasks + total_subtasks));
    RETURN progress;
  END IF;
END;
$function$;

-- Update the update_completed_at trigger function to handle new status values
CREATE OR REPLACE FUNCTION public.update_completed_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- For projects table
  IF TG_TABLE_NAME = 'projects' THEN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'completed' AND OLD.status = 'completed' THEN
      NEW.completed_at = NULL;
    END IF;
  END IF;
  
  -- For tasks and sub_tasks tables (now using 'completed' instead of 'done')
  IF TG_TABLE_NAME IN ('tasks', 'sub_tasks') THEN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'completed' AND OLD.status = 'completed' THEN
      NEW.completed_at = NULL;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$function$;