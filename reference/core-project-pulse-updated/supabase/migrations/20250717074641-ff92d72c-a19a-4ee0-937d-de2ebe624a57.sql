-- Create impact_types table
CREATE TABLE public.impact_types (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  label TEXT NOT NULL,
  description TEXT NOT NULL,
  color TEXT NOT NULL,
  bg_color TEXT NOT NULL,
  border_color TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.impact_types ENABLE ROW LEVEL SECURITY;

-- Create policies for read access
CREATE POLICY "Impact types are viewable by everyone" 
ON public.impact_types 
FOR SELECT 
USING (true);

-- Seed with existing impact types
INSERT INTO public.impact_types (name, label, description, color, bg_color, border_color, sort_order) VALUES
  ('Revenue', 'Revenue', 'Direct customer/sales impact', 'hsl(var(--green))', 'hsl(var(--green) / 0.1)', 'hsl(var(--green) / 0.3)', 1),
  ('Platform', 'Platform', 'Core infrastructure, agent framework, future mission', 'hsl(var(--blue))', 'hsl(var(--blue) / 0.1)', 'hsl(var(--blue) / 0.3)', 2),
  ('Bug Fix', 'Bug Fix', 'Resolving existing issues/technical problems', 'hsl(var(--purple))', 'hsl(var(--purple) / 0.1)', 'hsl(var(--purple) / 0.3)', 3),
  ('R&D', 'R&D', 'Research, experimentation, proof of concepts', 'hsl(var(--orange))', 'hsl(var(--orange) / 0.1)', 'hsl(var(--orange) / 0.3)', 4);

-- Add impact_type_id foreign key to projects table
ALTER TABLE public.projects 
ADD COLUMN impact_type_id UUID REFERENCES public.impact_types(id);

-- Create index for better performance
CREATE INDEX idx_projects_impact_type_id ON public.projects(impact_type_id);

-- Migrate existing data: populate impact_type_id based on current impact_type enum
UPDATE public.projects 
SET impact_type_id = (
  SELECT id FROM public.impact_types 
  WHERE name = projects.impact_type::text
)
WHERE impact_type IS NOT NULL;

-- Add trigger for timestamp updates
CREATE TRIGGER update_impact_types_updated_at
BEFORE UPDATE ON public.impact_types
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();