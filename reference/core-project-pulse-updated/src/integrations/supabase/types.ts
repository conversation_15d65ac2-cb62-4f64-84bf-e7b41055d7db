export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      impact_types: {
        Row: {
          bg_color: string
          border_color: string
          color: string
          created_at: string
          description: string
          id: string
          is_active: boolean
          label: string
          name: string
          sort_order: number
          updated_at: string
        }
        Insert: {
          bg_color: string
          border_color: string
          color: string
          created_at?: string
          description: string
          id?: string
          is_active?: boolean
          label: string
          name: string
          sort_order?: number
          updated_at?: string
        }
        Update: {
          bg_color?: string
          border_color?: string
          color?: string
          created_at?: string
          description?: string
          id?: string
          is_active?: boolean
          label?: string
          name?: string
          sort_order?: number
          updated_at?: string
        }
        Relationships: []
      }
      priority_history: {
        Row: {
          auto_escalated: boolean | null
          change_reason: string | null
          changed_by: string | null
          created_at: string
          id: string
          new_priority: Database["public"]["Enums"]["priority_level"]
          old_priority: Database["public"]["Enums"]["priority_level"] | null
          project_id: string
        }
        Insert: {
          auto_escalated?: boolean | null
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string
          id?: string
          new_priority: Database["public"]["Enums"]["priority_level"]
          old_priority?: Database["public"]["Enums"]["priority_level"] | null
          project_id: string
        }
        Update: {
          auto_escalated?: boolean | null
          change_reason?: string | null
          changed_by?: string | null
          created_at?: string
          id?: string
          new_priority?: Database["public"]["Enums"]["priority_level"]
          old_priority?: Database["public"]["Enums"]["priority_level"] | null
          project_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "priority_history_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      priority_rules: {
        Row: {
          created_at: string
          from_priority: Database["public"]["Enums"]["priority_level"]
          id: string
          is_active: boolean | null
          max_days: number
          to_priority: Database["public"]["Enums"]["priority_level"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          from_priority: Database["public"]["Enums"]["priority_level"]
          id?: string
          is_active?: boolean | null
          max_days: number
          to_priority: Database["public"]["Enums"]["priority_level"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          from_priority?: Database["public"]["Enums"]["priority_level"]
          id?: string
          is_active?: boolean | null
          max_days?: number
          to_priority?: Database["public"]["Enums"]["priority_level"]
          updated_at?: string
        }
        Relationships: []
      }
      project_integrations: {
        Row: {
          created_at: string
          id: string
          integration_data: Json | null
          integration_type: string
          integration_url: string
          project_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          integration_data?: Json | null
          integration_type: string
          integration_url: string
          project_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          integration_data?: Json | null
          integration_type?: string
          integration_url?: string
          project_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_integrations_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          archived_at: string | null
          auto_escalated: boolean | null
          company_name: string
          completed_at: string | null
          created_at: string
          customer_contact: string | null
          customer_lead: string | null
          customer_lead_id: string | null
          customer_name: string | null
          description: string | null
          effort_estimate: Database["public"]["Enums"]["effort_estimate"] | null
          end_date: string | null
          id: string
          impact_type: Database["public"]["Enums"]["impact_type"] | null
          impact_type_id: string | null
          last_reviewed_at: string | null
          name: string
          original_end_date: string | null
          poc_url: string | null
          prd_document_link: string | null
          priority_assigned_at: string | null
          priority_level: Database["public"]["Enums"]["priority_level"] | null
          priority_order: number | null
          project_lead: string
          project_lead_id: string | null
          start_date: string | null
          status: string
          status_changed_at: string | null
          type: string
          updated_at: string
        }
        Insert: {
          archived_at?: string | null
          auto_escalated?: boolean | null
          company_name?: string
          completed_at?: string | null
          created_at?: string
          customer_contact?: string | null
          customer_lead?: string | null
          customer_lead_id?: string | null
          customer_name?: string | null
          description?: string | null
          effort_estimate?:
            | Database["public"]["Enums"]["effort_estimate"]
            | null
          end_date?: string | null
          id?: string
          impact_type?: Database["public"]["Enums"]["impact_type"] | null
          impact_type_id?: string | null
          last_reviewed_at?: string | null
          name: string
          original_end_date?: string | null
          poc_url?: string | null
          prd_document_link?: string | null
          priority_assigned_at?: string | null
          priority_level?: Database["public"]["Enums"]["priority_level"] | null
          priority_order?: number | null
          project_lead: string
          project_lead_id?: string | null
          start_date?: string | null
          status?: string
          status_changed_at?: string | null
          type: string
          updated_at?: string
        }
        Update: {
          archived_at?: string | null
          auto_escalated?: boolean | null
          company_name?: string
          completed_at?: string | null
          created_at?: string
          customer_contact?: string | null
          customer_lead?: string | null
          customer_lead_id?: string | null
          customer_name?: string | null
          description?: string | null
          effort_estimate?:
            | Database["public"]["Enums"]["effort_estimate"]
            | null
          end_date?: string | null
          id?: string
          impact_type?: Database["public"]["Enums"]["impact_type"] | null
          impact_type_id?: string | null
          last_reviewed_at?: string | null
          name?: string
          original_end_date?: string | null
          poc_url?: string | null
          prd_document_link?: string | null
          priority_assigned_at?: string | null
          priority_level?: Database["public"]["Enums"]["priority_level"] | null
          priority_order?: number | null
          project_lead?: string
          project_lead_id?: string | null
          start_date?: string | null
          status?: string
          status_changed_at?: string | null
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_customer_lead_id_fkey"
            columns: ["customer_lead_id"]
            isOneToOne: false
            referencedRelation: "team_members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "projects_impact_type_id_fkey"
            columns: ["impact_type_id"]
            isOneToOne: false
            referencedRelation: "impact_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "projects_project_lead_id_fkey"
            columns: ["project_lead_id"]
            isOneToOne: false
            referencedRelation: "team_members"
            referencedColumns: ["id"]
          },
        ]
      }
      sub_tasks: {
        Row: {
          assignee: string
          assignee_id: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          name: string
          status: string
          task_id: string
          updated_at: string
        }
        Insert: {
          assignee: string
          assignee_id?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          name: string
          status?: string
          task_id: string
          updated_at?: string
        }
        Update: {
          assignee?: string
          assignee_id?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          name?: string
          status?: string
          task_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sub_tasks_assignee_id_fkey"
            columns: ["assignee_id"]
            isOneToOne: false
            referencedRelation: "team_members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sub_tasks_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assignee: string
          assignee_id: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          name: string
          project_id: string
          status: string
          updated_at: string
        }
        Insert: {
          assignee: string
          assignee_id?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          name: string
          project_id: string
          status?: string
          updated_at?: string
        }
        Update: {
          assignee?: string
          assignee_id?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          name?: string
          project_id?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assignee_id_fkey"
            columns: ["assignee_id"]
            isOneToOne: false
            referencedRelation: "team_members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      team_members: {
        Row: {
          created_at: string
          department: string | null
          email: string
          id: string
          is_active: boolean
          name: string
          role: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          department?: string | null
          email: string
          id?: string
          is_active?: boolean
          name: string
          role?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          department?: string | null
          email?: string
          id?: string
          is_active?: boolean
          name?: string
          role?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_days_in_priority: {
        Args: { priority_assigned_date: string }
        Returns: number
      }
      calculate_project_progress: {
        Args: { project_uuid: string }
        Returns: number
      }
      check_auto_escalation: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      convert_project_to_task: {
        Args: { source_project_id: string; target_project_id: string }
        Returns: string
      }
      convert_task_to_project: {
        Args: {
          task_id_param: string
          project_name: string
          project_description?: string
          project_type?: string
          company_name?: string
          start_date?: string
          end_date?: string
          customer_name?: string
          customer_lead?: string
          customer_contact?: string
          prd_document_link?: string
          poc_url?: string
          effort_estimate?: Database["public"]["Enums"]["effort_estimate"]
          impact_type?: Database["public"]["Enums"]["impact_type"]
          priority_level?: Database["public"]["Enums"]["priority_level"]
        }
        Returns: string
      }
      reorder_projects: {
        Args: { project_ids: string[]; new_orders: number[] }
        Returns: undefined
      }
    }
    Enums: {
      effort_estimate: "S" | "M" | "L" | "XL"
      impact_type: "Revenue" | "Platform" | "Bug Fix" | "R&D"
      priority_level: "P0" | "P1" | "P2" | "P3" | "P4"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      effort_estimate: ["S", "M", "L", "XL"],
      impact_type: ["Revenue", "Platform", "Bug Fix", "R&D"],
      priority_level: ["P0", "P1", "P2", "P3", "P4"],
    },
  },
} as const
