// Supabase client configuration with environment variable support
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Get configuration from environment variables or fallback to production
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

// Environment detection
const isLocal = import.meta.env.VITE_ENVIRONMENT === 'local' || SUPABASE_URL.includes('localhost');

console.log(`🔗 Supabase client connecting to: ${SUPABASE_URL} (${isLocal ? 'LOCAL' : 'REMOTE'})`);

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});

// Export environment info for debugging
export const supabaseConfig = {
  url: SUPABASE_URL,
  isLocal,
  environment: import.meta.env.VITE_ENVIRONMENT || 'production'
};