import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { ProjectCard } from './ProjectCard';
import { Project } from '@/types/project';

interface DraggableProjectCardProps {
  project: Project;
  tasks?: Array<{ status: string; sub_tasks?: Array<{ status: string }> }>;
}

export function DraggableProjectCard({ project, tasks }: DraggableProjectCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: project.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative will-change-transform
        ${isDragging ? 'opacity-20 pointer-events-none' : 'transition-all duration-200'}
      `}
      {...attributes}
      {...listeners}
    >
      <div className="group relative cursor-grab active:cursor-grabbing touch-none">
        <div
          className="absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
        <ProjectCard project={project} tasks={tasks} />
      </div>
    </div>
  );
}