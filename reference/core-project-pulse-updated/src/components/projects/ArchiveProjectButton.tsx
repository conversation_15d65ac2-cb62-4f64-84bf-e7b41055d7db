import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Archive, Loader2 } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";

interface ArchiveProjectButtonProps {
  projectId: string;
  projectName: string;
  className?: string;
  variant?: "default" | "outline" | "ghost" | "destructive" | "secondary" | "link";
}

export function ArchiveProjectButton({ 
  projectId, 
  projectName, 
  className,
  variant = "outline"
}: ArchiveProjectButtonProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  
  // Check if we're on the project detail page
  const isOnDetailPage = location.pathname.includes(`/projects/${projectId}`);

  const archiveProjectMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase
        .from('projects')
        .update({ status: 'archived' })
        .eq('id', projectId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', projectId] });
      toast({
        title: "Success",
        description: `"${projectName}" has been archived`,
      });
      setIsOpen(false);
      
      // Navigate to main page if we're on the project detail page
      if (isOnDetailPage) {
        navigate("/");
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to archive project. Please try again.",
        variant: "destructive",
      });
      console.error('Error archiving project:', error);
    },
  });

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          variant={variant} 
          size="sm" 
          className={className}
          disabled={archiveProjectMutation.isPending}
        >
          {archiveProjectMutation.isPending ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Archive className="w-4 h-4" />
          )}
          Archive
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Archive Project</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to archive "{projectName}"? This will move the project to archived status and hide it from the main project list.
            <br /><br />
            Archived projects can be viewed by enabling the "Show archived projects" toggle in the filters.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={() => archiveProjectMutation.mutate()}
            disabled={archiveProjectMutation.isPending}
          >
            {archiveProjectMutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Archiving...
              </>
            ) : (
              <>
                <Archive className="w-4 h-4 mr-2" />
                Archive Project
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}