import { ChevronDown, ChevronUp } from "lucide-react";
import { SortField, SortDirection } from "@/hooks/useProjectFilters";

interface ProjectListHeaderProps {
  sortBy: SortField;
  sortDirection: SortDirection;
  handleSort: (field: SortField) => void;
  getSortIcon: (field: SortField) => React.ReactNode;
}

export function ProjectListHeader({ 
  sortBy, 
  sortDirection, 
  handleSort, 
  getSortIcon 
}: ProjectListHeaderProps) {
  return (
    <div className="grid grid-cols-[2fr_0.8fr_0.8fr_1fr_1fr_1.5fr_1fr] gap-4 items-center px-4 py-2 text-sm font-medium text-muted-foreground border-b bg-muted/20">
      <button 
        onClick={() => handleSort("name")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        Project{getSortIcon("name")}
      </button>
      <button 
        onClick={() => handleSort("project_lead")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        Lead{getSortIcon("project_lead")}
      </button>
      <button 
        onClick={() => handleSort("customer_name")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        Company{getSortIcon("customer_name")}
      </button>
      <button 
        onClick={() => handleSort("start_date")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        Start Date{getSortIcon("start_date")}
      </button>
      <button 
        onClick={() => handleSort("end_date")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        End Date{getSortIcon("end_date")}
      </button>
      <button 
        onClick={() => handleSort("priority_level")}
        className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
      >
        Priority{getSortIcon("priority_level")}
      </button>
      <button 
        onClick={() => handleSort("status")}
        className="text-right hover:text-foreground transition-colors cursor-pointer flex items-center justify-end"
      >
        Status{getSortIcon("status")}
      </button>
    </div>
  );
}