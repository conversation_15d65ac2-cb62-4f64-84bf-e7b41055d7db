import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { useUpdateProjectStatus } from "@/hooks/useUpdateProjectStatus";
import { useToast } from "@/hooks/use-toast";
import { PROJECT_STATUSES } from "@/lib/constants";
import { RotateCcw, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface UnarchiveProjectButtonProps {
  projectId: string;
  projectName: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function UnarchiveProjectButton({ 
  projectId, 
  projectName, 
  variant = "outline", 
  size = "sm",
  className 
}: UnarchiveProjectButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("");
  const updateProjectStatus = useUpdateProjectStatus();
  const { toast } = useToast();

  // Filter out archived status for restoration options
  const restoreStatusOptions = PROJECT_STATUSES.filter(status => status.value !== 'archived');

  const handleUnarchive = async () => {
    if (!selectedStatus) {
      toast({
        title: "Status Required",
        description: "Please select a status to restore the project to.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateProjectStatus.mutateAsync({ 
        projectId, 
        status: selectedStatus 
      });
      
      toast({
        title: "Project Restored",
        description: `${projectName} has been restored to ${restoreStatusOptions.find(s => s.value === selectedStatus)?.label}.`,
      });
      
      setIsOpen(false);
      setSelectedStatus("");
    } catch (error) {
      toast({
        title: "Restore Failed",
        description: "Failed to restore project. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          className={cn("flex items-center gap-1", className)}
        >
          <RotateCcw className="h-3 w-3" />
          {size !== "icon" && "Restore"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Restore Project</DialogTitle>
          <DialogDescription>
            Select a status to restore "{projectName}" from the archive.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Restore to status:</label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status..." />
              </SelectTrigger>
              <SelectContent>
                {restoreStatusOptions.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleUnarchive}
            disabled={!selectedStatus || updateProjectStatus.isPending}
          >
            {updateProjectStatus.isPending && (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            )}
            Restore Project
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}