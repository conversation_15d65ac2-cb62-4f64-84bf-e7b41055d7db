import { useMemo, useState } from "react";
import { DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent, DragOverEvent, DragStartEvent, DragOverlay, CollisionDetection, rectIntersection } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { KanbanColumn } from './KanbanColumn';
import { DraggableProjectCard } from './DraggableProjectCard';
import { Project, ProjectWithDetails } from '@/types/project';
import { useToast } from '@/hooks/use-toast';

interface KanbanBoardProps {
  projects: ProjectWithDetails[];
  onReorder: (projectIds: string[], newOrders: number[]) => void;
  onStatusChange: (projectId: string, newStatus: string) => void;
}

export function KanbanBoard({ projects, onReorder, onStatusChange }: KanbanBoardProps) {
  const { toast } = useToast();
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [draggedOverColumn, setDraggedOverColumn] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  // Custom collision detection that prioritizes columns over cards
  const customCollisionDetection: CollisionDetection = (args) => {
    const { droppableContainers, active, pointerCoordinates } = args;
    
    if (!pointerCoordinates) return [];

    // First, check for column collisions
    const columnIds = ['not-started', 'in-progress', 'completed', 'backlog'];
    const columnCollisions = droppableContainers
      .filter(container => columnIds.includes(container.id as string))
      .map(container => {
        const rect = container.rect.current;
        if (!rect) return null;
        
        // Expand the collision area for better detection
        const expandedRect = {
          ...rect,
          top: rect.top - 10,
          bottom: rect.bottom + 10,
          left: rect.left - 10,
          right: rect.right + 10,
        };
        
        const isInside = pointerCoordinates.x >= expandedRect.left &&
                        pointerCoordinates.x <= expandedRect.right &&
                        pointerCoordinates.y >= expandedRect.top &&
                        pointerCoordinates.y <= expandedRect.bottom;
        
        return isInside ? {
          id: container.id,
          data: container.data,
        } : null;
      })
      .filter(Boolean);

    // If we found column collisions, return the first one
    if (columnCollisions.length > 0) {
      return columnCollisions;
    }

    // Fallback to card-level collision detection using rectIntersection
    return rectIntersection(args);
  };

  // Group projects by status
  const groupedProjects = useMemo(() => {
    const groups: Record<string, ProjectWithDetails[]> = {
      'not-started': [],
      'in-progress': [],
      'completed': [],
      'backlog': []
    };

    projects.forEach(project => {
      const status = project.status || 'backlog';
      if (groups[status]) {
        groups[status].push(project);
      } else {
        groups['backlog'].push(project);
      }
    });

    // Sort projects within each column by priority_order
    Object.keys(groups).forEach(status => {
      groups[status].sort((a, b) => (a.priority_order || 1000) - (b.priority_order || 1000));
    });

    return groups;
  }, [projects]);

  const handleDragStart = (event: DragStartEvent) => {
    setDraggedItem(event.active.id as string);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    if (over) {
      const overId = over.id as string;
      const isOverColumn = Object.keys(groupedProjects).includes(overId);
      setDraggedOverColumn(isOverColumn ? overId : null);
    } else {
      setDraggedOverColumn(null);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    console.log('Drag end:', { activeId: active.id, overId: over?.id, overData: over?.data });
    
    setDraggedItem(null);
    setDraggedOverColumn(null);
    
    if (!over || isUpdating) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the active project
    const activeProject = projects.find(p => p.id === activeId);
    if (!activeProject) return;

    // Determine if we're dropping over a column or a project
    const columnIds = ['not-started', 'in-progress', 'completed', 'backlog'];
    const isOverColumn = columnIds.includes(overId);
    
    console.log('Drop analysis:', { 
      activeProjectStatus: activeProject.status,
      overId,
      isOverColumn,
      columnIds
    });
    
    if (isOverColumn) {
      // Dropping over a column
      const newStatus = overId;
      if (activeProject.status !== newStatus) {
        setIsUpdating(true);
        try {
          await onStatusChange(activeId, newStatus);
          
          // Show success toast with status names
          const statusNames = {
            'not-started': "Haven't Started Yet",
            'in-progress': 'In Progress',
            'completed': 'Done',
            'backlog': 'Backlog'
          };
          
          toast({
            title: "Status Updated",
            description: `Moved to ${statusNames[newStatus as keyof typeof statusNames]}`,
          });
        } catch (error) {
          console.error('Failed to update project status:', error);
          toast({
            title: "Error",
            description: "Failed to update project status. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsUpdating(false);
        }
      }
    } else {
      // Dropping over another project - handle reordering
      const overProject = projects.find(p => p.id === overId);
      if (!overProject) return;

      const activeStatus = activeProject.status || 'backlog';
      const overStatus = overProject.status || 'backlog';

      if (activeStatus === overStatus) {
        // Reordering within the same column
        const columnProjects = groupedProjects[activeStatus];
        const oldIndex = columnProjects.findIndex(p => p.id === activeId);
        const newIndex = columnProjects.findIndex(p => p.id === overId);

        if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
          const newOrder = arrayMove(columnProjects, oldIndex, newIndex);
          const projectIds = newOrder.map(p => p.id);
          const newOrders = newOrder.map((_, index) => index + 1);
          onReorder(projectIds, newOrders);
        }
      } else {
        // Moving to a different column - change status
        setIsUpdating(true);
        try {
          await onStatusChange(activeId, overStatus);
          
          const statusNames = {
            'not-started': "Haven't Started Yet",
            'in-progress': 'In Progress', 
            'completed': 'Done',
            'backlog': 'Backlog'
          };
          
          toast({
            title: "Status Updated",
            description: `Moved to ${statusNames[overStatus as keyof typeof statusNames]}`,
          });
        } catch (error) {
          console.error('Failed to update project status:', error);
          toast({
            title: "Error",
            description: "Failed to update project status. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsUpdating(false);
        }
      }
    }
  };

  const columns = [
    { id: 'not-started', title: "Haven't Started Yet", projects: groupedProjects['not-started'] },
    { id: 'in-progress', title: 'In Progress', projects: groupedProjects['in-progress'] },
    { id: 'completed', title: 'Done', projects: groupedProjects['completed'] },
    { id: 'backlog', title: 'Backlog', projects: groupedProjects['backlog'] }
  ];

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={customCollisionDetection}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 h-[calc(100vh-300px)] min-h-[600px] ${draggedItem ? 'select-none' : ''}`}>
        {columns.map(column => (
          <div key={column.id} className={`relative ${draggedItem && draggedOverColumn !== column.id ? 'pointer-events-none' : ''}`}>
            <SortableContext items={column.projects.map(p => p.id)}>
              <KanbanColumn
                id={column.id}
                title={column.title}
                projects={column.projects}
                isDraggedOver={draggedOverColumn === column.id}
                draggedItemId={draggedItem}
                isUpdating={isUpdating}
              />
            </SortableContext>
          </div>
        ))}
      </div>
      
      <DragOverlay dropAnimation={{
        duration: 300,
        easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
      }}>
        {draggedItem ? (
          <div className="transform rotate-3 scale-105">
            <DraggableProjectCard
              project={projects.find(p => p.id === draggedItem)!}
              tasks={projects.find(p => p.id === draggedItem)?.tasks || []}
            />
          </div>
        ) : null}
      </DragOverlay>
      
      {/* Loading overlay */}
      {isUpdating && (
        <div className="fixed inset-0 bg-black/20 dark:bg-white/10 flex items-center justify-center z-50">
          <div className="bg-background border rounded-lg p-4 shadow-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              <span className="text-sm text-muted-foreground">Updating project...</span>
            </div>
          </div>
        </div>
      )}
    </DndContext>
  );
}