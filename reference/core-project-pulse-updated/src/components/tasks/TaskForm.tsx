import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { TASK_STATUSES } from "@/lib/constants";
import { Task, SubTask } from "@/types/project";
import { TeamMemberSelect } from "@/components/team/TeamMemberSelect";
import { cn } from "@/lib/utils";

const taskSchema = z.object({
  name: z.string().min(1, "Task name is required"),
  description: z.string().optional(),
  assignee_id: z.string().min(1, "Assignee is required"),
  due_date: z.date().optional(),
  status: z.enum(['to-do', 'in-progress', 'done']).default('to-do'),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskFormProps {
  task?: Task | SubTask;
  onSubmit: (data: TaskFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  isSubTask?: boolean;
  projectEndDate?: string;
  onUpdateProjectEndDate?: (newEndDate: string) => void;
}

export function TaskForm({ task, onSubmit, onCancel, isLoading, isSubTask = false, projectEndDate, onUpdateProjectEndDate }: TaskFormProps) {
  const [showDateConflictDialog, setShowDateConflictDialog] = useState(false);
  const [pendingData, setPendingData] = useState<TaskFormData | null>(null);
  
  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      name: task?.name || "",
      description: task?.description || "",
      assignee_id: task?.assignee_id || "",
      due_date: task?.due_date ? new Date(task.due_date) : undefined,
      status: task?.status || "to-do",
    },
  });

  const handleFormSubmit = (data: TaskFormData) => {
    // Check if task due date is after project end date
    if (data.due_date && projectEndDate) {
      const taskDueDate = new Date(data.due_date);
      const projEndDate = new Date(projectEndDate);
      
      if (taskDueDate > projEndDate) {
        setPendingData(data);
        setShowDateConflictDialog(true);
        return;
      }
    }
    
    // No conflict, proceed normally
    onSubmit(data);
  };


  const handleUpdateProjectDate = () => {
    if (pendingData && pendingData.due_date && onUpdateProjectEndDate) {
      const newEndDate = format(pendingData.due_date, "yyyy-MM-dd");
      onUpdateProjectEndDate(newEndDate);
      onSubmit(pendingData);
      setPendingData(null);
      setShowDateConflictDialog(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{isSubTask ? 'Sub-task' : 'Task'} Name</FormLabel>
              <FormControl>
                <Input placeholder={`Enter ${isSubTask ? 'sub-task' : 'task'} name`} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Enter description (optional)"
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="assignee_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Assignee</FormLabel>
                <FormControl>
                  <TeamMemberSelect
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Select assignee"
                    required
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {TASK_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="due_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Due Date (Optional)</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : task ? `Update ${isSubTask ? 'Sub-task' : 'Task'}` : `Create ${isSubTask ? 'Sub-task' : 'Task'}`}
          </Button>
        </div>
      </form>

      <AlertDialog open={showDateConflictDialog} onOpenChange={setShowDateConflictDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Task Due Date Conflict</AlertDialogTitle>
            <AlertDialogDescription>
              The task due date ({pendingData?.due_date ? format(pendingData.due_date, "PPP") : ""}) is after the project end date ({projectEndDate ? format(new Date(projectEndDate), "PPP") : ""}). 
              <br /><br />
              Would you like to:
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2">
            <AlertDialogCancel onClick={() => setShowDateConflictDialog(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleUpdateProjectDate}>
              Update Project End Date
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Form>
  );
}