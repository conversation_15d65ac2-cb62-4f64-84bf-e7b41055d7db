/**
 * Authentication Error Page
 * Displays authentication errors and provides retry options
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const AuthError: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleRetry = () => {
    login();
  };

  const handleGoHome = () => {
    navigate('/', { replace: true });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        <div className="space-y-2">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
          <h1 className="text-2xl font-bold">Authentication Failed</h1>
          <p className="text-muted-foreground">
            We encountered an issue while trying to sign you in. This could be due to:
          </p>
        </div>

        <div className="text-left space-y-2 bg-muted p-4 rounded-lg">
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Network connectivity issues</li>
            <li>• Google OAuth service temporarily unavailable</li>
            <li>• Account permissions or restrictions</li>
            <li>• Browser security settings blocking the request</li>
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={handleRetry} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <Button onClick={handleGoHome} variant="outline" className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            Go Home
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          If the problem persists, please contact support or try again later.
        </div>
      </div>
    </div>
  );
};

export default AuthError;
