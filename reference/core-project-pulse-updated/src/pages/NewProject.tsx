import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ProjectForm } from "@/components/projects/ProjectForm";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useTeamMembers } from "@/hooks/useTeamMembers";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

export default function NewProject() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { data: teamMembers = [] } = useTeamMembers(true);

  const createProjectMutation = useMutation({
    mutationFn: async (data: any) => {
      // Find team member names for the IDs
      const projectLead = teamMembers.find(member => member.id === data.project_lead_id);
      const customerLead = data.customer_lead_id ? teamMembers.find(member => member.id === data.customer_lead_id) : null;

      if (!projectLead) {
        throw new Error('Project lead not found');
      }

      // Transform empty strings to null for UUID fields and add names
      const cleanedData = {
        ...data,
        project_lead: projectLead.name,
        project_lead_id: data.project_lead_id || null,
        customer_lead: customerLead?.name || null,
        customer_lead_id: data.customer_lead_id || null,
      };
      
      const { error } = await supabase
        .from('projects')
        .insert([cleanedData]);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Success",
        description: "Project created successfully!",
      });
      navigate('/');
    },
    onError: (error) => {
      toast({
        title: "Error", 
        description: "Failed to create project. Please try again.",
        variant: "destructive",
      });
      console.error('Error creating project:', error);
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link to="/">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Project</h1>
          <p className="text-muted-foreground">
            Add a new project to start tracking tasks and progress
          </p>
        </div>
      </div>

      <div className="max-w-4xl">
        <ProjectForm
          onSubmit={(data) => createProjectMutation.mutate(data)}
          isLoading={createProjectMutation.isPending}
        />
      </div>
    </div>
  );
}