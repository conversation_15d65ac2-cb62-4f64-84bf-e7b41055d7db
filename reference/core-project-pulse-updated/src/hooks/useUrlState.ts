import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export function useUrlState<T>(
  key: string,
  defaultValue: T,
  serialize: (value: T) => string = String,
  deserialize: (value: string) => T = (value) => value as T
) {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get initial value from URL or use default
  const getInitialValue = useCallback(() => {
    const urlValue = searchParams.get(key);
    if (urlValue !== null) {
      try {
        return deserialize(urlValue);
      } catch {
        return defaultValue;
      }
    }
    return defaultValue;
  }, [key, defaultValue, searchParams, deserialize]);

  const [state, setState] = useState<T>(getInitialValue);

  // Update state when URL changes
  useEffect(() => {
    const newValue = getInitialValue();
    setState(newValue);
  }, [getInitialValue]);

  // Update URL when state changes
  const setValue = useCallback((value: T) => {
    setState(value);
    
    const newSearchParams = new URLSearchParams(searchParams);
    
    if (value === defaultValue || value === '' || value === 'all' || value === 'none' || value === false) {
      // Remove parameter if it's the default value
      newSearchParams.delete(key);
    } else {
      // Set parameter to serialized value
      newSearchParams.set(key, serialize(value));
    }
    
    // Use replace to avoid cluttering history
    setSearchParams(newSearchParams, { replace: true });
  }, [key, defaultValue, searchParams, setSearchParams, serialize]);

  return [state, setValue] as const;
}