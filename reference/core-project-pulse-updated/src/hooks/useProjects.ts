import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";

// Backend API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1';

// Helper function to make authenticated API calls
async function apiCall(endpoint: string, options: RequestInit = {}) {
  const token = localStorage.getItem('accessToken');

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }

  return response.json();
}

export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async (): Promise<ProjectWithDetails[]> => {
      try {
        const response = await apiCall('/projects');
        const projects = response.data || [];

        // Transform backend data to match frontend interface exactly
        return projects.map((project: any) => ({
          id: project.id,
          name: project.name,
          company_name: project.customerName || '',
          type: project.type,
          customer_name: project.customerName,
          project_lead: project.projectLead,
          project_lead_id: project.createdBy,
          customer_lead: project.customerLead,
          customer_lead_id: undefined,
          customer_contact: project.customerContact,
          description: project.description,
          status: project.status,
          start_date: undefined,
          end_date: undefined,
          original_end_date: undefined,
          prd_document_link: undefined,
          poc_url: undefined,
          priority_order: 0,
          created_at: project.createdAt,
          updated_at: project.updatedAt,
          completed_at: undefined,
          archived_at: undefined,
          status_changed_at: undefined,
          priority_level: project.priorityLevel,
          effort_estimate: project.effortEstimate,
          impact_type: undefined,
          impact_type_id: undefined,
          priority_assigned_at: undefined,
          last_reviewed_at: undefined,
          auto_escalated: false,
          // Transform tasks to match frontend interface
          tasks: project.tasks?.map((task: any) => ({
            id: task.id,
            project_id: project.id,
            name: task.name,
            description: task.description,
            assignee: task.assigneeId || '',
            assignee_id: task.assigneeId,
            due_date: task.dueDate,
            status: task.status.replace('_', '-'), // Convert to_do -> to-do, in_progress -> in-progress
            created_at: task.createdAt,
            updated_at: task.updatedAt,
            completed_at: undefined,
          })) || [],
          integrations: [],
          progress: 0,
        }));
      } catch (error) {
        console.error('Error fetching projects:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProjectsBasic() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async (): Promise<Project[]> => {
      try {
        const response = await apiCall('/projects');
        const projects = response.data || [];

        // Transform backend data to match frontend Project interface
        return projects.map((project: any) => ({
          id: project.id,
          name: project.name,
          company_name: project.customerName || '',
          type: project.type,
          customer_name: project.customerName,
          project_lead: project.projectLead,
          project_lead_id: project.createdBy,
          customer_lead: project.customerLead,
          customer_lead_id: undefined,
          customer_contact: project.customerContact,
          description: project.description,
          status: project.status,
          start_date: undefined,
          end_date: undefined,
          original_end_date: undefined,
          prd_document_link: undefined,
          poc_url: undefined,
          priority_order: 0,
          created_at: project.createdAt,
          updated_at: project.updatedAt,
          completed_at: undefined,
          archived_at: undefined,
          status_changed_at: undefined,
          priority_level: project.priorityLevel,
          effort_estimate: project.effortEstimate,
          impact_type: undefined,
          impact_type_id: undefined,
          priority_assigned_at: undefined,
          last_reviewed_at: undefined,
          auto_escalated: false,
        }));
      } catch (error) {
        console.error('Error fetching projects:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      try {
        const response = await apiCall('/projects');
        const projects = response.data || [];

        const progressMap: Record<string, number> = {};

        // Calculate progress for each project based on tasks
        for (const project of projects) {
          const tasks = project.tasks || [];
          const totalTasks = tasks.length;
          const completedTasks = tasks.filter((task: any) => task.status === 'done').length;
          const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

          progressMap[project.id] = progress;
        }

        return progressMap;
      } catch (error) {
        console.error('Error fetching project progress:', error);
        return {};
      }
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      // For now, just return success since the backend doesn't have reordering yet
      // This can be implemented later if needed
      console.log('Reordering projects:', { projectIds, newOrders });
      return Promise.resolve();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}