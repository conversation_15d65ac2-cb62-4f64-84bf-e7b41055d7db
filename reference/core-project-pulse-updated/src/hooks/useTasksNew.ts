/**
 * Tasks Hooks (New API Client)
 * Replaces Supabase-based task hooks with our backend API
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, ApiError } from "@/services/apiClient";
import { useToast } from "@/hooks/use-toast";
import { Task } from "./useProjectsNew";

// Task-specific interfaces
export interface CreateTaskData {
  projectId: string;
  name: string;
  description?: string;
  assigneeId?: string;
  dueDate?: string;
  status?: 'to-do' | 'in-progress' | 'done' | 'cancelled';
  priorityLevel?: 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
  effortEstimate?: 'XS' | 'S' | 'M' | 'L' | 'XL';
}

export interface UpdateTaskData {
  name?: string;
  description?: string;
  assigneeId?: string;
  dueDate?: string;
  status?: 'to-do' | 'in-progress' | 'done' | 'cancelled';
  priorityLevel?: 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
  effortEstimate?: 'XS' | 'S' | 'M' | 'L' | 'XL';
}

// Hooks
export function useTasks(params?: { projectId?: string; status?: string; assigneeId?: string }) {
  return useQuery({
    queryKey: ['tasks', params],
    queryFn: async (): Promise<Task[]> => {
      try {
        const response = await api.tasks.list(params);
        return response.data || [];
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useTask(id: string) {
  return useQuery({
    queryKey: ['task', id],
    queryFn: async (): Promise<Task> => {
      try {
        const response = await api.tasks.get(id);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useCreateTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskData: CreateTaskData) => {
      try {
        const response = await api.tasks.create(taskData);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.projectId] });
      
      toast({
        title: "Success",
        description: "Task created successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error creating task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create task. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useUpdateTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: UpdateTaskData }) => {
      try {
        const response = await api.tasks.update(id, updates);
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['task', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.projectId] });
      
      toast({
        title: "Success",
        description: "Task updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update task. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useDeleteTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        // Get task data before deletion to know which project to invalidate
        const taskResponse = await api.tasks.get(id);
        const task = taskResponse.data;
        
        await api.tasks.delete(id);
        return { id, projectId: task.projectId };
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.projectId] });
      
      toast({
        title: "Success",
        description: "Task deleted successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    },
  });
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ taskId, status }: { taskId: string; status: string }) => {
      try {
        const response = await api.tasks.update(taskId, { status });
        return response.data;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      // Optimistic update with immediate cache refresh
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['task', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.projectId] });
      
      toast({
        title: "Success",
        description: "Task status updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Error updating task status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update task status. Please try again.",
        variant: "destructive",
      });
    },
  });
}

// Utility hook for task statistics
export function useTaskStats(projectId?: string) {
  return useQuery({
    queryKey: ['task-stats', projectId],
    queryFn: async () => {
      try {
        const params = projectId ? { projectId } : undefined;
        const response = await api.tasks.list(params);
        const tasks = response.data || [];

        const stats = {
          total: tasks.length,
          todo: tasks.filter(t => t.status === 'to-do').length,
          inProgress: tasks.filter(t => t.status === 'in-progress').length,
          done: tasks.filter(t => t.status === 'done').length,
          cancelled: tasks.filter(t => t.status === 'cancelled').length,
        };

        return stats;
      } catch (error) {
        if (error instanceof ApiError) {
          throw new Error(error.message);
        }
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}
