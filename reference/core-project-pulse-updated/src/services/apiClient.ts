/**
 * API Client
 * Centralized HTTP client for backend API communication
 * Replaces Supabase client with our custom backend
 */

import { useAuth } from '@/contexts/AuthContext';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1';

export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private getAuthToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getAuthToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(
          data.message || data.error || 'API request failed',
          response.status,
          data
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0
      );
    }
  }

  // HTTP Methods
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // File upload
  async upload<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const token = this.getAuthToken();
    const formData = new FormData();
    
    formData.append('file', file);
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new ApiError(
        data.message || data.error || 'Upload failed',
        response.status,
        data
      );
    }

    return data;
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Convenience functions for common operations
export const api = {
  // Auth
  auth: {
    me: () => apiClient.get('/auth/me'),
    refresh: (refreshToken: string) => apiClient.post('/auth/refresh', { refreshToken }),
    logout: () => apiClient.post('/auth/logout'),
  },

  // Projects
  projects: {
    list: (params?: { status?: string; limit?: number; offset?: number }) => 
      apiClient.get('/projects', params),
    get: (id: string) => apiClient.get(`/projects/${id}`),
    create: (data: any) => apiClient.post('/projects', data),
    update: (id: string, data: any) => apiClient.put(`/projects/${id}`, data),
    delete: (id: string) => apiClient.delete(`/projects/${id}`),
  },

  // Tasks
  tasks: {
    list: (params?: { projectId?: string; status?: string; assigneeId?: string }) => 
      apiClient.get('/tasks', params),
    get: (id: string) => apiClient.get(`/tasks/${id}`),
    create: (data: any) => apiClient.post('/tasks', data),
    update: (id: string, data: any) => apiClient.put(`/tasks/${id}`, data),
    delete: (id: string) => apiClient.delete(`/tasks/${id}`),
  },

  // Users
  users: {
    list: () => apiClient.get('/users'),
    get: (id: string) => apiClient.get(`/users/${id}`),
    update: (id: string, data: any) => apiClient.put(`/users/${id}`, data),
    delete: (id: string) => apiClient.delete(`/users/${id}`),
  },

  // RBAC
  rbac: {
    roles: () => apiClient.get('/rbac/roles'),
    permissions: () => apiClient.get('/rbac/permissions'),
    userPermissions: (userId: string) => apiClient.get(`/rbac/users/${userId}/permissions`),
    assignRole: (userId: string, roleName: string) => 
      apiClient.post(`/rbac/users/${userId}/roles`, { roleName }),
    removeRole: (userId: string, roleName: string) => 
      apiClient.delete(`/rbac/users/${userId}/roles/${roleName}`),
    checkPermission: (permission: string) => 
      apiClient.post('/rbac/check-permission', { permission }),
    checkRole: (role: string) => 
      apiClient.post('/rbac/check-role', { role }),
  },

  // Admin
  admin: {
    users: () => apiClient.get('/admin/users'),
    updateUserRole: (userId: string, role: string) => 
      apiClient.put(`/admin/users/${userId}/role`, { role }),
    updateUserStatus: (userId: string, isActive: boolean) => 
      apiClient.put(`/admin/users/${userId}/status`, { isActive }),
    systemHealth: () => apiClient.get('/admin/system/health'),
    systemStats: () => apiClient.get('/admin/system/stats'),
  },
};

export default apiClient;
