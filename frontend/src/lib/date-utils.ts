import { differenceInDays } from "date-fns";
import { Project } from "@/types/project";

export function calculateDaysUntilDue(endDate: string | null | undefined): number | null {
  if (!endDate) return null;
  
  try {
    const end = new Date(endDate);
    const today = new Date();
    return differenceInDays(end, today);
  } catch {
    return null;
  }
}

export function getDueDateColorScheme(project: Project): {
  background: string;
  text: string;
  muted: string;
} {
  // Completed projects should always be green regardless of deadline performance
  if (project.status === 'completed') {
    return {
      background: 'card-bg-completed',
      text: 'card-text-completed',
      muted: 'card-muted-completed'
    };
  }
  
  const daysUntilDue = calculateDaysUntilDue(project.end_date);
  
  // No end date - grey
  if (daysUntilDue === null) {
    return {
      background: 'card-bg-no-date',
      text: 'card-text-no-date',
      muted: 'card-muted-no-date'
    };
  }
  
  // Overdue - red
  if (daysUntilDue < 0) {
    return {
      background: 'card-bg-overdue',
      text: 'card-text-overdue',
      muted: 'card-muted-overdue'
    };
  }
  
  // Due within 7 days - orange
  if (daysUntilDue <= 7) {
    return {
      background: 'card-bg-urgent',
      text: 'card-text-urgent',
      muted: 'card-muted-urgent'
    };
  }
  
  // Due within 30 days - yellow
  if (daysUntilDue <= 30) {
    return {
      background: 'card-bg-upcoming',
      text: 'card-text-upcoming',
      muted: 'card-muted-upcoming'
    };
  }
  
  // Due more than 30 days - green
  return {
    background: 'card-bg-future',
    text: 'card-text-future',
    muted: 'card-muted-future'
  };
}