const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

class ApiClient {
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('auth_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }

    if (response.status === 204) {
      return {} as T;
    }

    return response.json();
  }

  // Projects
  async getProjects() {
    return this.request('/projects');
  }

  async getProjectsBasic() {
    return this.request('/projects/basic');
  }

  async getProject(id: string) {
    return this.request(`/projects/${id}`);
  }

  async createProject(data: any) {
    return this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: any) {
    return this.request(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string) {
    return this.request(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  async reorderProjects(projectIds: string[], newOrders: number[]) {
    // Update each project's priority_order
    const updatePromises = projectIds.map((id, index) => 
      this.updateProject(id, { priority_order: newOrders[index] })
    );
    
    await Promise.all(updatePromises);
  }

  // Team Members
  async getTeamMembers(activeOnly = false) {
    const query = activeOnly ? '?active=true' : '';
    return this.request(`/team-members${query}`);
  }

  async createTeamMember(data: any) {
    return this.request('/team-members', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateTeamMember(id: string, data: any) {
    return this.request(`/team-members/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteTeamMember(id: string) {
    return this.request(`/team-members/${id}`, {
      method: 'DELETE',
    });
  }

  // Impact Types
  async getImpactTypes() {
    return this.request('/impact-types');
  }

  // Authentication
  async login(credential: string, profile: any) {
    return this.request('/auth/google', {
      method: 'POST',
      body: JSON.stringify({ token: credential, profile }),
    });
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  async refreshToken() {
    return this.request('/auth/refresh', {
      method: 'POST',
    });
  }

  async logout() {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async getUserPermissions() {
    return this.request('/auth/permissions');
  }

  // Admin endpoints
  async getUsers(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const query = searchParams.toString();
    return this.request(`/admin/users${query ? `?${query}` : ''}`);
  }

  async updateUserRoles(userId: string, roles: string[]) {
    return this.request(`/admin/users/${userId}/roles`, {
      method: 'PUT',
      body: JSON.stringify({ roles }),
    });
  }

  async updateUserStatus(userId: string, isActive: boolean) {
    return this.request(`/admin/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ is_active: isActive }),
    });
  }

  async getRoles() {
    return this.request('/admin/roles');
  }

  async getPermissions() {
    return this.request('/admin/permissions');
  }

  async initializeRBAC() {
    return this.request('/admin/initialize', {
      method: 'POST',
    });
  }

  // Generic HTTP methods for flexibility
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // Project Progress
  async getProjectProgress() {
    const projects = await this.getProjectsBasic();
    const progressMap: Record<string, number> = {};
    
    for (const project of projects) {
      // Get project with tasks to calculate progress
      const projectWithTasks = await this.getProject(project.id);
      
      let totalTasks = 0;
      let completedTasks = 0;

      projectWithTasks.tasks?.forEach((task: any) => {
        if (task.sub_tasks?.length > 0) {
          // If task has subtasks, count subtasks
          totalTasks += task.sub_tasks.length;
          completedTasks += task.sub_tasks.filter((st: any) => st.status === 'done').length;
        } else {
          // If no subtasks, count the task itself
          totalTasks += 1;
          if (task.status === 'done') completedTasks += 1;
        }
      });

      progressMap[project.id] = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    }

    return progressMap;
  }
}

export const apiClient = new ApiClient();
