const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

class ApiClient {
  private isRefreshing = false;
  private refreshPromise: Promise<void> | null = null;

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('auth_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private async refreshTokens(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      // Clear tokens and throw error
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    if (data.success) {
      localStorage.setItem('auth_token', data.data.accessToken);
      localStorage.setItem('refresh_token', data.data.refreshToken);
    } else {
      throw new Error('Token refresh failed');
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const makeRequest = async (): Promise<Response> => {
      const config: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders(),
          ...options.headers,
        },
        ...options,
      };

      return fetch(url, config);
    };

    let response = await makeRequest();

    // If we get a 401 and we're not already refreshing tokens, try to refresh
    if (response.status === 401 && !this.isRefreshing && endpoint !== '/auth/refresh') {
      if (this.refreshPromise) {
        // Wait for ongoing refresh
        await this.refreshPromise;
        // Retry the request
        response = await makeRequest();
      } else {
        // Start token refresh
        this.isRefreshing = true;
        this.refreshPromise = this.refreshTokens()
          .then(() => {
            this.isRefreshing = false;
            this.refreshPromise = null;
          })
          .catch((error) => {
            this.isRefreshing = false;
            this.refreshPromise = null;
            throw error;
          });

        try {
          await this.refreshPromise;
          // Retry the request with new token
          response = await makeRequest();
        } catch (error) {
          // Refresh failed, redirect to login
          window.location.href = '/login';
          throw error;
        }
      }
    }

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }

    if (response.status === 204) {
      return {} as T;
    }

    return response.json();
  }

  // Projects
  async getProjects() {
    return this.request('/projects');
  }

  async getProjectsBasic() {
    return this.request('/projects/basic');
  }

  async getProject(id: string) {
    return this.request(`/projects/${id}`);
  }

  async createProject(data: any) {
    return this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: any) {
    return this.request(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string) {
    return this.request(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  async reorderProjects(projectIds: string[], newOrders: number[]) {
    // Update each project's priority_order
    const updatePromises = projectIds.map((id, index) => 
      this.updateProject(id, { priority_order: newOrders[index] })
    );
    
    await Promise.all(updatePromises);
  }

  // Team Members
  async getTeamMembers(activeOnly = false) {
    const query = activeOnly ? '?active=true' : '';
    return this.request(`/team-members${query}`);
  }

  async createTeamMember(data: any) {
    return this.request('/team-members', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateTeamMember(id: string, data: any) {
    return this.request(`/team-members/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteTeamMember(id: string) {
    return this.request(`/team-members/${id}`, {
      method: 'DELETE',
    });
  }

  // Impact Types
  async getImpactTypes() {
    return this.request('/impact-types');
  }

  // Authentication
  async login(credential: string, profile: any) {
    return this.request('/auth/google', {
      method: 'POST',
      body: JSON.stringify({ token: credential, profile }),
    });
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  async refreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.request('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  async logout() {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async getUserPermissions() {
    return this.request('/auth/permissions');
  }

  // Admin endpoints
  async getUsers(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const query = searchParams.toString();
    return this.request(`/admin/users${query ? `?${query}` : ''}`);
  }

  async updateUserRoles(userId: string, roles: string[]) {
    return this.request(`/admin/users/${userId}/roles`, {
      method: 'PUT',
      body: JSON.stringify({ roles }),
    });
  }

  async updateUserStatus(userId: string, isActive: boolean) {
    return this.request(`/admin/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ is_active: isActive }),
    });
  }

  async getRoles() {
    return this.request('/admin/roles');
  }

  async getPermissions() {
    return this.request('/admin/permissions');
  }

  async initializeRBAC() {
    return this.request('/admin/initialize', {
      method: 'POST',
    });
  }

  // Generic HTTP methods for flexibility
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // Project Progress
  async getProjectProgress() {
    const projects = await this.getProjectsBasic();
    const progressMap: Record<string, number> = {};
    
    for (const project of projects) {
      // Get project with tasks to calculate progress
      const projectWithTasks = await this.getProject(project.id);
      
      let totalTasks = 0;
      let completedTasks = 0;

      projectWithTasks.tasks?.forEach((task: any) => {
        if (task.sub_tasks?.length > 0) {
          // If task has subtasks, count subtasks
          totalTasks += task.sub_tasks.length;
          completedTasks += task.sub_tasks.filter((st: any) => st.status === 'done').length;
        } else {
          // If no subtasks, count the task itself
          totalTasks += 1;
          if (task.status === 'done') completedTasks += 1;
        }
      });

      progressMap[project.id] = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    }

    return progressMap;
  }

  // Requirements API methods
  async getRequirements(projectId: string, filters?: {
    type?: string;
    status?: string;
    priority?: string;
  }): Promise<any[]> {
    const params = new URLSearchParams();
    if (filters?.type) params.append('type', filters.type);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.priority) params.append('priority', filters.priority);

    const queryString = params.toString();
    const endpoint = `/requirements/${projectId}${queryString ? `?${queryString}` : ''}`;

    const response = await this.request<{ success: boolean; data: any[] }>(endpoint);
    return response.data;
  }

  async createRequirement(projectId: string, requirement: {
    title: string;
    description?: string;
    type?: string;
    priority?: string;
    status?: string;
    source?: string;
    rationale?: string;
    acceptance_criteria?: string;
    order_index?: number;
  }): Promise<any> {
    const response = await this.request<{ success: boolean; data: any }>(`/requirements/${projectId}`, {
      method: 'POST',
      body: JSON.stringify(requirement),
    });
    return response.data;
  }

  async updateRequirement(requirementId: string, updates: {
    title?: string;
    description?: string;
    type?: string;
    priority?: string;
    status?: string;
    source?: string;
    rationale?: string;
    acceptance_criteria?: string;
    order_index?: number;
  }): Promise<any> {
    const response = await this.request<{ success: boolean; data: any }>(`/requirements/${requirementId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    return response.data;
  }

  async deleteRequirement(requirementId: string): Promise<void> {
    await this.request<{ success: boolean; message: string }>(`/requirements/${requirementId}`, {
      method: 'DELETE',
    });
  }

  async reorderRequirement(requirementId: string, newOrderIndex: number): Promise<void> {
    await this.request<{ success: boolean; message: string }>(`/requirements/${requirementId}/reorder`, {
      method: 'POST',
      body: JSON.stringify({ newOrderIndex }),
    });
  }
}

export const apiClient = new ApiClient();
