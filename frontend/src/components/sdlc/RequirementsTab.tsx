import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Search, Filter, FileText, Link as LinkIcon, CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface RequirementsTabProps {
  projectId: string;
}

interface Requirement {
  id: string;
  title: string;
  description: string;
  type: 'functional' | 'non_functional' | 'constraint' | 'assumption';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'approved' | 'implemented' | 'tested' | 'rejected';
  source: string;
  acceptanceCriteria: string;
  linkedTasks: number;
  linkedTests: number;
  createdAt: string;
}

export const RequirementsTab: React.FC<RequirementsTabProps> = ({ projectId }) => {
  const [requirements, setRequirements] = useState<Requirement[]>([
    {
      id: '1',
      title: 'User Authentication System',
      description: 'Users must be able to register, login, and manage their accounts securely',
      type: 'functional',
      priority: 'high',
      status: 'approved',
      source: 'Product Manager',
      acceptanceCriteria: 'User can register with email, login with credentials, reset password, and update profile',
      linkedTasks: 3,
      linkedTests: 5,
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      title: 'System Response Time',
      description: 'All API endpoints must respond within 200ms under normal load',
      type: 'non_functional',
      priority: 'medium',
      status: 'draft',
      source: 'Technical Lead',
      acceptanceCriteria: 'Load testing shows 95% of requests complete within 200ms',
      linkedTasks: 1,
      linkedTests: 2,
      createdAt: '2024-01-16'
    },
    {
      id: '3',
      title: 'Mobile Responsive Design',
      description: 'The application must work seamlessly on mobile devices',
      type: 'functional',
      priority: 'high',
      status: 'implemented',
      source: 'UX Designer',
      acceptanceCriteria: 'UI adapts to screen sizes from 320px to 1920px width',
      linkedTasks: 4,
      linkedTests: 8,
      createdAt: '2024-01-17'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'implemented': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'tested': return <CheckCircle className="h-4 w-4 text-purple-500" />;
      case 'draft': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'rejected': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'implemented': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'tested': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredRequirements = requirements.filter(req => {
    const matchesSearch = req.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         req.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || req.type === filterType;
    const matchesStatus = filterStatus === 'all' || req.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  const requirementStats = {
    total: requirements.length,
    approved: requirements.filter(r => r.status === 'approved').length,
    implemented: requirements.filter(r => r.status === 'implemented').length,
    tested: requirements.filter(r => r.status === 'tested').length,
    draft: requirements.filter(r => r.status === 'draft').length
  };

  return (
    <div className="space-y-6">
      {/* Requirements Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{requirementStats.total}</div>
            <p className="text-sm text-gray-600">Total Requirements</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{requirementStats.approved}</div>
            <p className="text-sm text-gray-600">Approved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{requirementStats.implemented}</div>
            <p className="text-sm text-gray-600">Implemented</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{requirementStats.tested}</div>
            <p className="text-sm text-gray-600">Tested</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{requirementStats.draft}</div>
            <p className="text-sm text-gray-600">Draft</p>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search requirements..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="functional">Functional</SelectItem>
              <SelectItem value="non_functional">Non-Functional</SelectItem>
              <SelectItem value="constraint">Constraint</SelectItem>
              <SelectItem value="assumption">Assumption</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="implemented">Implemented</SelectItem>
              <SelectItem value="tested">Tested</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Requirement
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Requirement</DialogTitle>
              <DialogDescription>
                Create a new requirement for this project
              </DialogDescription>
            </DialogHeader>
            <AddRequirementForm onClose={() => setIsAddDialogOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Requirements Table */}
      <Card>
        <CardHeader>
          <CardTitle>Requirements ({filteredRequirements.length})</CardTitle>
          <CardDescription>
            Manage project requirements and their implementation status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Requirement</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Links</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequirements.map((requirement) => (
                <TableRow key={requirement.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{requirement.title}</div>
                      <div className="text-sm text-gray-500 line-clamp-2">
                        {requirement.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {requirement.type.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityColor(requirement.priority)}>
                      {requirement.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(requirement.status)}
                      <Badge className={getStatusColor(requirement.status)}>
                        {requirement.status}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3 text-blue-500" />
                        <span>{requirement.linkedTasks} tasks</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-3 w-3 text-green-500" />
                        <span>{requirement.linkedTests} tests</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm">{requirement.source}</TableCell>
                  <TableCell className="text-sm">
                    {new Date(requirement.createdAt).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

// Add Requirement Form Component
const AddRequirementForm: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Title</label>
          <Input placeholder="Enter requirement title" />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Source</label>
          <Input placeholder="Who requested this?" />
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Description</label>
        <Textarea placeholder="Describe the requirement in detail" rows={3} />
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Type</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="functional">Functional</SelectItem>
              <SelectItem value="non_functional">Non-Functional</SelectItem>
              <SelectItem value="constraint">Constraint</SelectItem>
              <SelectItem value="assumption">Assumption</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Priority</label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Status</label>
          <Select defaultValue="draft">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">Acceptance Criteria</label>
        <Textarea placeholder="How will this requirement be verified?" rows={3} />
      </div>
      
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={onClose}>Create Requirement</Button>
      </div>
    </div>
  );
};
