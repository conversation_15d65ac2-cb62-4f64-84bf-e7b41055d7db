import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Calendar, Clock, Users, Target, Plus, Calendar as CalendarIcon } from 'lucide-react';

interface PlanningTabProps {
  projectId: string;
}

interface Phase {
  id: string;
  name: string;
  description: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked';
  startDate: string;
  endDate: string;
  progress: number;
  milestones: Milestone[];
}

interface Milestone {
  id: string;
  name: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTasks: number;
}

export const PlanningTab: React.FC<PlanningTabProps> = ({ projectId }) => {
  const [phases] = useState<Phase[]>([
    {
      id: '1',
      name: 'Requirements Gathering',
      description: 'Collect and document all project requirements',
      status: 'completed',
      startDate: '2024-01-15',
      endDate: '2024-02-15',
      progress: 100,
      milestones: [
        {
          id: '1',
          name: 'Requirements Document Complete',
          description: 'All functional and non-functional requirements documented',
          dueDate: '2024-02-10',
          status: 'completed',
          priority: 'high',
          assignedTasks: 8
        },
        {
          id: '2',
          name: 'Stakeholder Approval',
          description: 'Requirements approved by all stakeholders',
          dueDate: '2024-02-15',
          status: 'completed',
          priority: 'critical',
          assignedTasks: 3
        }
      ]
    },
    {
      id: '2',
      name: 'System Design',
      description: 'Create technical architecture and system design',
      status: 'completed',
      startDate: '2024-02-16',
      endDate: '2024-03-15',
      progress: 100,
      milestones: [
        {
          id: '3',
          name: 'Architecture Design',
          description: 'High-level system architecture completed',
          dueDate: '2024-03-01',
          status: 'completed',
          priority: 'high',
          assignedTasks: 5
        },
        {
          id: '4',
          name: 'Database Design',
          description: 'Database schema and relationships finalized',
          dueDate: '2024-03-10',
          status: 'completed',
          priority: 'high',
          assignedTasks: 4
        }
      ]
    },
    {
      id: '3',
      name: 'Development',
      description: 'Implementation of the system components',
      status: 'in_progress',
      startDate: '2024-03-16',
      endDate: '2024-05-15',
      progress: 65,
      milestones: [
        {
          id: '5',
          name: 'Backend API Complete',
          description: 'All backend services and APIs implemented',
          dueDate: '2024-04-15',
          status: 'pending',
          priority: 'critical',
          assignedTasks: 12
        },
        {
          id: '6',
          name: 'Frontend UI Complete',
          description: 'User interface implementation finished',
          dueDate: '2024-05-01',
          status: 'pending',
          priority: 'high',
          assignedTasks: 15
        }
      ]
    },
    {
      id: '4',
      name: 'Testing',
      description: 'Comprehensive testing of all system components',
      status: 'not_started',
      startDate: '2024-05-16',
      endDate: '2024-06-15',
      progress: 0,
      milestones: [
        {
          id: '7',
          name: 'Unit Testing Complete',
          description: 'All unit tests written and passing',
          dueDate: '2024-05-30',
          status: 'pending',
          priority: 'high',
          assignedTasks: 8
        },
        {
          id: '8',
          name: 'Integration Testing',
          description: 'System integration tests completed',
          dueDate: '2024-06-10',
          status: 'pending',
          priority: 'high',
          assignedTasks: 6
        }
      ]
    },
    {
      id: '5',
      name: 'Deployment',
      description: 'Deploy system to production environment',
      status: 'not_started',
      startDate: '2024-06-16',
      endDate: '2024-06-30',
      progress: 0,
      milestones: [
        {
          id: '9',
          name: 'Production Deployment',
          description: 'System deployed to production',
          dueDate: '2024-06-25',
          status: 'pending',
          priority: 'critical',
          assignedTasks: 4
        }
      ]
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'blocked': return 'bg-red-100 text-red-800 border-red-200';
      case 'not_started': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getMilestoneStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const totalMilestones = phases.reduce((sum, phase) => sum + phase.milestones.length, 0);
  const completedMilestones = phases.reduce((sum, phase) => 
    sum + phase.milestones.filter(m => m.status === 'completed').length, 0
  );
  const overdueMilestones = phases.reduce((sum, phase) => 
    sum + phase.milestones.filter(m => m.status === 'overdue').length, 0
  );

  return (
    <div className="space-y-6">
      {/* Planning Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{phases.length}</div>
                <p className="text-sm text-gray-600">Total Phases</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{completedMilestones}</div>
                <p className="text-sm text-gray-600">Completed Milestones</p>
                <div className="text-xs text-gray-500">of {totalMilestones} total</div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-red-500" />
              <div>
                <div className="text-2xl font-bold">{overdueMilestones}</div>
                <p className="text-sm text-gray-600">Overdue Items</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">8</div>
                <p className="text-sm text-gray-600">Team Members</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timeline View */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Project Timeline</CardTitle>
              <CardDescription>
                Visual representation of project phases and milestones
              </CardDescription>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Phase
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {phases.map((phase, index) => (
              <div key={phase.id} className="relative">
                {/* Timeline connector */}
                {index < phases.length - 1 && (
                  <div className="absolute left-6 top-16 w-0.5 h-16 bg-gray-200"></div>
                )}
                
                <div className="flex items-start space-x-4">
                  {/* Timeline dot */}
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    phase.status === 'completed' ? 'bg-green-500' :
                    phase.status === 'in_progress' ? 'bg-blue-500' :
                    phase.status === 'blocked' ? 'bg-red-500' : 'bg-gray-300'
                  }`}>
                    <span className="text-white font-bold">{index + 1}</span>
                  </div>
                  
                  {/* Phase content */}
                  <div className="flex-1">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="font-semibold text-lg">{phase.name}</h3>
                            <p className="text-gray-600 text-sm">{phase.description}</p>
                          </div>
                          <Badge className={getStatusColor(phase.status)}>
                            {phase.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center space-x-2 text-sm">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span>Start: {new Date(phase.startDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span>End: {new Date(phase.endDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <Target className="h-4 w-4 text-gray-500" />
                            <span>{phase.milestones.length} milestones</span>
                          </div>
                        </div>
                        
                        <div className="mb-4">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span>Progress</span>
                            <span>{phase.progress}%</span>
                          </div>
                          <Progress value={phase.progress} className="h-2" />
                        </div>
                        
                        {/* Milestones */}
                        {phase.milestones.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Milestones:</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {phase.milestones.map((milestone) => (
                                <div key={milestone.id} className="border rounded-lg p-3">
                                  <div className="flex items-start justify-between mb-2">
                                    <div className="flex-1">
                                      <h5 className="font-medium text-sm">{milestone.name}</h5>
                                      <p className="text-xs text-gray-600 mt-1">{milestone.description}</p>
                                    </div>
                                    <Badge className={getMilestoneStatusColor(milestone.status)} size="sm">
                                      {milestone.status}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center justify-between text-xs">
                                    <div className="flex items-center space-x-2">
                                      <Badge className={getPriorityColor(milestone.priority)} size="sm">
                                        {milestone.priority}
                                      </Badge>
                                      <span>{milestone.assignedTasks} tasks</span>
                                    </div>
                                    <span className="text-gray-500">
                                      Due: {new Date(milestone.dueDate).toLocaleDateString()}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Gantt Chart Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle>Gantt Chart View</CardTitle>
          <CardDescription>
            Interactive timeline view of project phases and dependencies
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Gantt Chart visualization will be implemented here</p>
              <p className="text-sm text-gray-500 mt-2">Interactive timeline with dependencies and resource allocation</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
