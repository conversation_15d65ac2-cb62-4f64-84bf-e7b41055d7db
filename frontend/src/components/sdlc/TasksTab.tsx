import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckSquare, Plus, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface TasksTabProps {
  projectId: string;
}

export const TasksTab: React.FC<TasksTabProps> = ({ projectId }) => {
  return (
    <div className="space-y-6">
      {/* Tasks Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">24</div>
            <p className="text-sm text-gray-600">Total Tasks</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">8</div>
            <p className="text-sm text-gray-600">In Progress</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">12</div>
            <p className="text-sm text-gray-600">Completed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">4</div>
            <p className="text-sm text-gray-600">Pending</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Enhanced Task Management</CardTitle>
              <CardDescription>
                Advanced task management with dependencies, time tracking, and Kanban boards
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Task
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <CheckSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Enhanced Task Management</h3>
              <p className="text-gray-600 mb-4">Advanced task management features coming soon:</p>
              <div className="text-left max-w-md mx-auto space-y-2 text-sm text-gray-600">
                <div>• Kanban board with drag-and-drop</div>
                <div>• Task dependencies and Gantt view</div>
                <div>• Time tracking and effort estimation</div>
                <div>• Subtasks and checklists</div>
                <div>• Assignment and notifications</div>
                <div>• Integration with requirements and tests</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
