import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Rocket, Server, GitBranch, CheckCircle, XCircle, Clock, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface DeploymentTabProps {
  projectId: string;
}

export const DeploymentTab: React.FC<DeploymentTabProps> = ({ projectId }) => {
  return (
    <div className="space-y-6">
      {/* Deployment Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Rocket className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">12</div>
                <p className="text-sm text-gray-600">Total Deployments</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">10</div>
                <p className="text-sm text-gray-600">Successful</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <div>
                <div className="text-2xl font-bold">1</div>
                <p className="text-sm text-gray-600">Failed</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">1</div>
                <p className="text-sm text-gray-600">In Progress</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Environment Status */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Status</CardTitle>
          <CardDescription>
            Current status of all deployment environments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Server className="h-5 w-5 text-blue-500" />
                  <h3 className="font-semibold">Development</h3>
                </div>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  Healthy
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span className="font-mono">v2.1.3-dev</span>
                </div>
                <div className="flex justify-between">
                  <span>Last Deploy:</span>
                  <span>2 hours ago</span>
                </div>
                <div className="flex justify-between">
                  <span>Uptime:</span>
                  <span>99.8%</span>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Server className="h-5 w-5 text-yellow-500" />
                  <h3 className="font-semibold">Staging</h3>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                  Deploying
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span className="font-mono">v2.1.2</span>
                </div>
                <div className="flex justify-between">
                  <span>Last Deploy:</span>
                  <span>In progress</span>
                </div>
                <div className="flex justify-between">
                  <span>Uptime:</span>
                  <span>99.5%</span>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Server className="h-5 w-5 text-green-500" />
                  <h3 className="font-semibold">Production</h3>
                </div>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  Healthy
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span className="font-mono">v2.1.1</span>
                </div>
                <div className="flex justify-between">
                  <span>Last Deploy:</span>
                  <span>3 days ago</span>
                </div>
                <div className="flex justify-between">
                  <span>Uptime:</span>
                  <span>99.9%</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Deployment Pipeline</CardTitle>
              <CardDescription>
                CI/CD pipeline management and deployment automation
              </CardDescription>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Deployment
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Rocket className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Deployment Management</h3>
              <p className="text-gray-600 mb-4">Advanced deployment features coming soon:</p>
              <div className="text-left max-w-md mx-auto space-y-2 text-sm text-gray-600">
                <div>• Automated CI/CD pipeline integration</div>
                <div>• Multi-environment deployment management</div>
                <div>• Blue-green and canary deployments</div>
                <div>• Rollback and version management</div>
                <div>• Infrastructure as Code (IaC)</div>
                <div>• Deployment approval workflows</div>
                <div>• Performance monitoring and alerts</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
