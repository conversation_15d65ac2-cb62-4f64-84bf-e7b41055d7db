import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BookOpen, FileText, File, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DocumentationTabProps {
  projectId: string;
}

export const DocumentationTab: React.FC<DocumentationTabProps> = ({ projectId }) => {
  return (
    <div className="space-y-6">
      {/* Documentation Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">18</div>
                <p className="text-sm text-gray-600">Total Documents</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">6</div>
                <p className="text-sm text-gray-600">API Docs</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <File className="h-5 w-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">8</div>
                <p className="text-sm text-gray-600">User Guides</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">4</div>
                <p className="text-sm text-gray-600">Technical Specs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Documentation Management</CardTitle>
              <CardDescription>
                Centralized documentation with version control and collaboration
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Document
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">Documentation Hub</h3>
              <p className="text-gray-600 mb-4">Comprehensive documentation features coming soon:</p>
              <div className="text-left max-w-md mx-auto space-y-2 text-sm text-gray-600">
                <div>• Rich text editor with markdown support</div>
                <div>• Document templates and categories</div>
                <div>• Version control and change tracking</div>
                <div>• Collaborative editing and comments</div>
                <div>• Auto-generated API documentation</div>
                <div>• File attachments and media support</div>
                <div>• Search and tagging system</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
