import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TaskStatus } from "@/types/project";
import { TASK_STATUSES } from "@/lib/constants";
import { cn } from "@/lib/utils";

interface TaskStatusSelectProps {
  value: TaskStatus;
  onValueChange?: (value: TaskStatus) => void;
  disabled?: boolean;
  className?: string;
}

export function TaskStatusSelect({ value, onValueChange, disabled = false, className }: TaskStatusSelectProps) {
  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'to-do':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'in-progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'done':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const currentStatus = TASK_STATUSES.find(status => status.value === value);

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger 
        className={cn(
          "w-full text-sm border rounded-md transition-colors",
          getStatusColor(value),
          className
        )}
      >
        <SelectValue>
          {currentStatus?.label || 'Select status'}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {TASK_STATUSES.map((status) => (
          <SelectItem 
            key={status.value} 
            value={status.value}
            className={cn(
              "text-sm cursor-pointer",
              getStatusColor(status.value)
            )}
          >
            {status.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}