import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Task, SubTask } from "@/types/project";
import { Calendar, User, ChevronDown, ChevronRight, Plus, Edit, CheckCircle2, Circle, AlertTriangle, Trophy, Sparkles, Trash2 } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { SubTaskItem } from "./SubTaskItem";
import { TaskStatusSelect } from "./TaskStatusSelect";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { ConvertToProjectButton } from "@/components/projects/ConvertToProjectButton";
import { format } from "date-fns";

interface TaskItemProps {
  task: Task;
  subTasks: SubTask[];
  onEditTask: (task: Task) => void;
  onAddSubTask: (taskId: string) => void;
  onEditSubTask: (subTask: SubTask) => void;
  onCompleteTask: (task: Task) => void;
  onCompleteSubTask: (subTask: SubTask) => void;
  onStatusChange: (task: Task, status: string) => void;
  onDeleteTask: (task: Task) => void;
  disabled?: boolean;
}

export function TaskItem({ task, subTasks, onEditTask, onAddSubTask, onEditSubTask, onCompleteTask, onCompleteSubTask, onStatusChange, onDeleteTask, disabled = false }: TaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCompletionDialog, setShowCompletionDialog] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'to-do':
        return 'bg-gray-50 border-gray-200';
      case 'in-progress':
        return 'bg-blue-50 border-blue-200';
      case 'done':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const hasSubTasks = subTasks.length > 0;
  const incompleteSubTasks = subTasks.filter(st => st.status !== 'done');
  
  const handleCompleteTask = () => {
    if (task.status !== 'done' && incompleteSubTasks.length > 0) {
      setShowCompletionDialog(true);
    } else {
      onCompleteTask(task);
    }
  };
  
  const handleConfirmComplete = () => {
    onCompleteTask(task);
    setShowCompletionDialog(false);
  };

  return (
    <div className={cn("border rounded-lg transition-colors duration-200", getStatusColor(task.status))}>
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {hasSubTasks && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0 mt-1"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-sm">{task.name}</h4>
                <div className="min-w-0 flex-1">
                  <TaskStatusSelect
                    value={task.status}
                    onValueChange={disabled ? undefined : (status) => onStatusChange(task, status)}
                    className="w-auto min-w-[160px]"
                    disabled={disabled}
                  />
                </div>
              </div>
              
              {task.description && (
                <p className="text-sm text-muted-foreground mb-3">
                  {task.description}
                </p>
              )}
              
              {/* Completion Date Display */}
              {task.status === 'done' && task.completed_at && (
                <div className="mb-3 p-2 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-md">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="flex items-center justify-center w-4 h-4 bg-green-500 rounded-full">
                      <Trophy className="w-2 h-2 text-white" />
                    </div>
                    <span className="text-green-700 font-medium">Completed:</span>
                    <span className="text-green-800 font-semibold">
                      {format(new Date(task.completed_at), "MMM d, yyyy 'at' h:mm a")}
                    </span>
                    <Sparkles className="w-3 h-3 text-green-600 animate-pulse" />
                  </div>
                </div>
              )}
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  <span>{(task as any).assignee_member?.name || task.assignee || "Unassigned"}</span>
                </div>
                
                {task.due_date && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>{new Date(task.due_date).toLocaleDateString()}</span>
                  </div>
                )}
                
                {hasSubTasks && (
                  <div className="flex items-center">
                    <span className="text-xs font-medium">
                      Subtasks: {subTasks.filter(st => st.status === 'done').length}/{subTasks.length}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {!disabled && (
              <ConvertToProjectButton 
                task={task}
                variant="ghost"
                size="sm"
                className="h-8"
              />
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={disabled ? undefined : handleCompleteTask}
              className="h-8 w-8 p-0"
              title={disabled ? 'Cannot edit archived project' : (task.status === 'done' ? 'Mark as incomplete' : 'Mark as complete')}
              disabled={disabled}
            >
              {task.status === 'done' ? (
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              ) : (
                <Circle className="h-4 w-4" />
              )}
            </Button>
            {!disabled && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onAddSubTask(task.id)}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEditTask(task)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDeleteTask(task)}
                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  title="Delete task"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {hasSubTasks && isExpanded && (
        <div className="border-t bg-muted/30">
          <div className="p-4 space-y-3">
            {subTasks.map((subTask) => (
              <SubTaskItem
                key={subTask.id}
                subTask={subTask}
                onEdit={onEditSubTask}
                onComplete={onCompleteSubTask}
                onStatusChange={(subTask, status) => onStatusChange(subTask as any, status)}
                disabled={disabled}
              />
            ))}
          </div>
        </div>
      )}
      
      <AlertDialog open={showCompletionDialog} onOpenChange={setShowCompletionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Incomplete Subtasks
            </AlertDialogTitle>
            <AlertDialogDescription>
              This task has {incompleteSubTasks.length} incomplete subtask{incompleteSubTasks.length > 1 ? 's' : ''}. 
              Are you sure you want to mark this task as complete? The subtasks will remain incomplete.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmComplete}>
              Complete Task
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}