import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface GoogleLoginButtonProps {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  className?: string;
}

export const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  className = "",
}) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const { isLoading } = useAuth();
  const [googleReady, setGoogleReady] = useState(false);

  useEffect(() => {
    const checkGoogleReady = () => {
      return window.google?.accounts?.id && import.meta.env.VITE_GOOGLE_CLIENT_ID;
    };

    if (checkGoogleReady()) {
      setGoogleReady(true);
      renderGoogleButton();
    } else {
      // Wait for Google to load
      const interval = setInterval(() => {
        if (checkGoogleReady()) {
          setGoogleReady(true);
          renderGoogleButton();
          clearInterval(interval);
        }
      }, 100);

      setTimeout(() => clearInterval(interval), 5000);
    }
  }, [disabled]);

  const renderGoogleButton = () => {
    if (!buttonRef.current || !window.google?.accounts?.id || disabled) return;

    try {
      buttonRef.current.innerHTML = '';
      window.google.accounts.id.renderButton(buttonRef.current, {
        theme: 'outline',
        size: 'large',
        width: 300,
        text: 'signin_with',
        shape: 'rectangular',
        logo_alignment: 'left',
      });
    } catch (error) {
      console.error('Failed to render Google button:', error);
      setGoogleReady(false);
    }
  };

  const handleManualLogin = () => {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.prompt();
    } else {
      toast.error('Google authentication is not available');
    }
  };

  // Show fallback button if Google isn't ready
  if (!googleReady) {
    return (
      <Button
        onClick={handleManualLogin}
        disabled={disabled || isLoading}
        className={`w-full ${className}`}
        variant="outline"
      >
        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="currentColor"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="currentColor"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="currentColor"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
        {isLoading ? 'Signing in...' : 'Sign in with Google'}
      </Button>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div ref={buttonRef} className="w-full" />
    </div>
  );
};
