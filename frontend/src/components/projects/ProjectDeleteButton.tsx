import { useState } from "react";
import { Trash2, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Project } from "@/types/project";
import { ProjectDeleteDialog } from "./ProjectDeleteDialog";
import { useDeleteProject } from "@/hooks/useDeleteProject";

interface ProjectDeleteButtonProps {
  project: Project;
  variant?: "icon" | "menu" | "button";
  tasks?: Array<{ status: string; sub_tasks?: Array<{ status: string }> }>;
}

export function ProjectDeleteButton({ 
  project, 
  variant = "icon",
  tasks = []
}: ProjectDeleteButtonProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const deleteProjectMutation = useDeleteProject();

  const taskCount = tasks.length;
  const subTaskCount = tasks.flatMap(task => task.sub_tasks || []).length;

  const handleDelete = () => {
    deleteProjectMutation.mutate(project.id);
    setShowDeleteDialog(false);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowDeleteDialog(true);
  };

  if (variant === "menu") {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={handleDeleteClick}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Project
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <ProjectDeleteDialog
          project={project}
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDelete}
          isDeleting={deleteProjectMutation.isPending}
          taskCount={taskCount}
          subTaskCount={subTaskCount}
        />
      </>
    );
  }

  if (variant === "button") {
    return (
      <>
        <Button
          variant="destructive"
          onClick={handleDeleteClick}
          disabled={deleteProjectMutation.isPending}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Project
        </Button>

        <ProjectDeleteDialog
          project={project}
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDelete}
          isDeleting={deleteProjectMutation.isPending}
          taskCount={taskCount}
          subTaskCount={subTaskCount}
        />
      </>
    );
  }

  // Default icon variant
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={handleDeleteClick}
      >
        <Trash2 className="h-4 w-4 text-destructive" />
      </Button>

      <ProjectDeleteDialog
        project={project}
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDelete}
        isDeleting={deleteProjectMutation.isPending}
        taskCount={taskCount}
        subTaskCount={subTaskCount}
      />
    </>
  );
}