import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Archive, Loader2 } from "lucide-react";
import { Project } from "@/types/project";

interface BulkArchiveButtonProps {
  completedProjects: Project[];
  className?: string;
}

export function BulkArchiveButton({ completedProjects, className }: BulkArchiveButtonProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);

  const bulkArchiveMutation = useMutation({
    mutationFn: async () => {
      const projectIds = completedProjects.map(p => p.id);
      
      const { error } = await supabase
        .from('projects')
        .update({ status: 'archived' })
        .in('id', projectIds);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Success",
        description: `${completedProjects.length} completed projects have been archived`,
      });
      setIsOpen(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to archive projects. Please try again.",
        variant: "destructive",
      });
      console.error('Error archiving projects:', error);
    },
  });

  if (completedProjects.length === 0) {
    return null;
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className={className}
          disabled={bulkArchiveMutation.isPending}
        >
          {bulkArchiveMutation.isPending ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Archive className="w-4 h-4 mr-2" />
          )}
          Archive {completedProjects.length} Completed Project{completedProjects.length !== 1 ? 's' : ''}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Archive Completed Projects</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to archive all {completedProjects.length} completed projects? This will move them to archived status and hide them from the main project list.
            <br /><br />
            <strong>Projects to be archived:</strong>
            <ul className="mt-2 space-y-1">
              {completedProjects.slice(0, 5).map(project => (
                <li key={project.id} className="text-sm text-muted-foreground">
                  • {project.name}
                </li>
              ))}
              {completedProjects.length > 5 && (
                <li className="text-sm text-muted-foreground">
                  • ...and {completedProjects.length - 5} more
                </li>
              )}
            </ul>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={() => bulkArchiveMutation.mutate()}
            disabled={bulkArchiveMutation.isPending}
          >
            {bulkArchiveMutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Archiving...
              </>
            ) : (
              <>
                <Archive className="w-4 h-4 mr-2" />
                Archive All
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}