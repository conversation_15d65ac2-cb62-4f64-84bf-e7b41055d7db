import { useState } from "react";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ConvertToTaskDialog } from "./ConvertToTaskDialog";
import { Project } from "@/types/project";

interface ConvertToTaskButtonProps {
  project: Project;
  variant?: "default" | "outline" | "ghost" | "destructive" | "secondary" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function ConvertToTaskButton({ 
  project, 
  variant = "outline", 
  size = "sm",
  className 
}: ConvertToTaskButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={(e) => {
          e.stopPropagation();
          setIsDialogOpen(true);
        }}
      >
        <ArrowRight className="h-4 w-4" />
        Convert to Task
      </Button>

      <ConvertToTaskDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        sourceProject={project}
      />
    </>
  );
}