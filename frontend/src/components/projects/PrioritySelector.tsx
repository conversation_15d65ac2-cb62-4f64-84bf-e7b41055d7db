import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Edit2 } from 'lucide-react';
import { PriorityLevel, EffortEstimate, ImpactType } from '@/types/project';
import { 
  getPriorityConfig, 
  getEffortConfig, 
  getImpactConfig
} from '@/lib/priority-utils';
import { useImpactTypes, getImpactTypeById, type ImpactType as DbImpactType } from '@/hooks/useImpactTypes';
import { cn } from '@/lib/utils';

interface PrioritySelectorProps {
  priority: PriorityLevel;
  effort: EffortEstimate;
  impact?: ImpactType;
  impactTypeId?: string;
  onPriorityChange?: (priority: PriorityLevel) => void;
  onEffortChange?: (effort: EffortEstimate) => void;
  onImpactChange?: (impact: ImpactType) => void;
  onImpactTypeIdChange?: (impactTypeId: string) => void;
  className?: string;
  disabled?: boolean;
}

export function PrioritySelector({
  priority,
  effort,
  impact,
  impactTypeId,
  onPriorityChange,
  onEffortChange,
  onImpactChange,
  onImpactTypeIdChange,
  className,
  disabled = false
}: PrioritySelectorProps) {
  const priorities: PriorityLevel[] = ['P0', 'P1', 'P2', 'P3', 'P4'];
  const efforts: EffortEstimate[] = ['S', 'M', 'L', 'XL'];
  
  // Use dynamic impact types from database
  const { data: impactTypes = [], isLoading: isLoadingImpactTypes } = useImpactTypes();
  
  // Get current impact type from database or fallback to legacy
  const currentImpactType = impactTypeId 
    ? getImpactTypeById(impactTypes, impactTypeId) 
    : null;

  const renderPriorityBadge = (p: PriorityLevel, isSelected: boolean = false) => {
    const config = getPriorityConfig(p);
    return (
      <Badge
        className={cn(
          "text-xs font-medium border cursor-pointer hover:opacity-80 transition-opacity",
          isSelected && "ring-2 ring-primary"
        )}
        style={{
          backgroundColor: config.bgColor,
          color: config.textColor,
          borderColor: config.borderColor,
        }}
      >
        {p}
      </Badge>
    );
  };

  const renderEffortBadge = (e: EffortEstimate, isSelected: boolean = false) => {
    const config = getEffortConfig(e);
    return (
      <Badge 
        className={cn(
          "text-xs cursor-pointer hover:opacity-80 transition-opacity border",
          isSelected && "ring-2 ring-primary"
        )}
        style={{
          backgroundColor: config.bgColor,
          color: config.color,
          borderColor: config.borderColor,
        }}
      >
        {e}
      </Badge>
    );
  };

  const renderImpactBadge = (impactType: DbImpactType, isSelected: boolean = false) => {
    return (
      <Badge 
        className={cn(
          "text-xs cursor-pointer hover:opacity-80 transition-opacity border",
          isSelected && "ring-2 ring-primary"
        )}
        style={{
          backgroundColor: impactType.bg_color,
          color: impactType.color,
          borderColor: impactType.border_color,
        }}
      >
        {impactType.label}
      </Badge>
    );
  };

  const renderLegacyImpactBadge = (i: ImpactType, isSelected: boolean = false) => {
    const config = getImpactConfig(i);
    return (
      <Badge 
        className={cn(
          "text-xs cursor-pointer hover:opacity-80 transition-opacity border",
          isSelected && "ring-2 ring-primary"
        )}
        style={{
          backgroundColor: config.bgColor,
          color: config.color,
          borderColor: config.borderColor,
        }}
      >
        {config.label}
      </Badge>
    );
  };


  return (
    <div className={cn("space-y-4", className)}>
      {/* Priority Level */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-muted-foreground">PRIORITY LEVEL</h4>
          {onPriorityChange && !disabled && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <Edit2 className="h-3 w-3 mr-1" />
                  Edit
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-80">
                {priorities.map((p) => (
                  <DropdownMenuItem
                    key={p}
                    onClick={() => onPriorityChange(p)}
                    className="flex items-start gap-3 py-3 px-4 min-h-[60px]"
                  >
                    {p === priority && <span className="text-xs text-primary mt-1">✓</span>}
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center gap-2">
                        {renderPriorityBadge(p)}
                        <span className="text-sm font-medium">{getPriorityConfig(p).label}</span>
                      </div>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {getPriorityConfig(p).description}
                      </p>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <div className="flex items-center gap-2">
          {renderPriorityBadge(priority, true)}
          <span className="text-sm text-muted-foreground">
            {getPriorityConfig(priority).label} - {getPriorityConfig(priority).description}
          </span>
        </div>
      </div>

      {/* Effort Estimate */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-muted-foreground">EFFORT ESTIMATE</h4>
          {onEffortChange && !disabled && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <Edit2 className="h-3 w-3 mr-1" />
                  Edit
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-80">
                {efforts.map((e) => (
                  <DropdownMenuItem
                    key={e}
                    onClick={() => onEffortChange(e)}
                    className="flex items-start gap-3 py-3 px-4 min-h-[60px]"
                  >
                    {e === effort && <span className="text-xs text-primary mt-1">✓</span>}
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center gap-2">
                        {renderEffortBadge(e)}
                        <span className="text-sm font-medium">{getEffortConfig(e).label}</span>
                      </div>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {getEffortConfig(e).description}
                      </p>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <div className="flex items-center gap-2">
          {renderEffortBadge(effort, true)}
          <span className="text-sm text-muted-foreground">
            {getEffortConfig(effort).label} - {getEffortConfig(effort).description}
          </span>
        </div>
      </div>

      {/* Impact Type */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-muted-foreground">IMPACT TYPE</h4>
          {onImpactTypeIdChange && !disabled && !isLoadingImpactTypes && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <Edit2 className="h-3 w-3 mr-1" />
                  Edit
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-80">
                {impactTypes.map((impactType) => (
                  <DropdownMenuItem
                    key={impactType.id}
                    onClick={() => onImpactTypeIdChange(impactType.id)}
                    className="flex items-start gap-3 py-3 px-4 min-h-[60px]"
                  >
                    {impactType.id === impactTypeId && <span className="text-xs text-primary mt-1">✓</span>}
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center gap-2">
                        {renderImpactBadge(impactType)}
                        <span className="text-sm font-medium">{impactType.label}</span>
                      </div>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {impactType.description}
                      </p>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isLoadingImpactTypes ? (
            <Badge className="text-xs">Loading...</Badge>
          ) : currentImpactType ? (
            <>
              {renderImpactBadge(currentImpactType, true)}
              <span className="text-sm text-muted-foreground">
                {currentImpactType.label} - {currentImpactType.description}
              </span>
            </>
          ) : impact ? (
            <>
              {renderLegacyImpactBadge(impact, true)}
              <span className="text-sm text-muted-foreground">
                {getImpactConfig(impact).label} - {getImpactConfig(impact).description}
              </span>
            </>
          ) : (
            <span className="text-sm text-muted-foreground">Select an impact type</span>
          )}
        </div>
      </div>

    </div>
  );
}