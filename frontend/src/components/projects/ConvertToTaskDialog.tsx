import { useState } from "react";
import { Search } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Project } from "@/types/project";
import { useProjects } from "@/hooks/useProjects";
import { useConvertProjectToTask } from "@/hooks/useConvertProjectToTask";
import { useNavigate } from "react-router-dom";

interface ConvertToTaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sourceProject: Project;
}

export function ConvertToTaskDialog({ open, onOpenChange, sourceProject }: ConvertToTaskDialogProps) {
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { data: projects = [] } = useProjects();
  const convertMutation = useConvertProjectToTask();
  const navigate = useNavigate();

  // Filter projects excluding the source project and filter by search
  const availableProjects = projects.filter(project => 
    project.id !== sourceProject.id &&
    project.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedProject = availableProjects.find(p => p.id === selectedProjectId);

  const handleConvert = () => {
    if (!selectedProjectId) return;

    convertMutation.mutate(
      { 
        sourceProjectId: sourceProject.id, 
        targetProjectId: selectedProjectId 
      },
      {
        onSuccess: () => {
          onOpenChange(false);
          navigate(`/projects/${selectedProjectId}`);
        }
      }
    );
  };

  const resetDialog = () => {
    setSelectedProjectId("");
    setSearchQuery("");
    setShowConfirmation(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetDialog();
    }
    onOpenChange(newOpen);
  };

  if (showConfirmation) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Project Conversion</DialogTitle>
          </DialogHeader>
          
          <Alert>
            <AlertDescription>
              <strong>Warning:</strong> This action cannot be undone. Are you sure you want to convert 
              "<strong>{sourceProject.name}</strong>" project to be a task under 
              "<strong>{selectedProject?.name}</strong>"?
            </AlertDescription>
          </Alert>

          <div className="space-y-3 text-sm text-muted-foreground">
            <p><strong>What will happen:</strong></p>
            <ul className="list-disc pl-5 space-y-1">
              <li>The project "{sourceProject.name}" will be deleted</li>
              <li>A new task will be created under "{selectedProject?.name}"</li>
              <li>All existing tasks will become subtasks of the new task</li>
              <li>All existing subtasks will be preserved</li>
              <li>Project integrations will be removed</li>
            </ul>
          </div>

          <div className="flex gap-2 justify-end">
            <Button 
              variant="outline" 
              onClick={() => setShowConfirmation(false)}
              disabled={convertMutation.isPending}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConvert}
              disabled={convertMutation.isPending}
            >
              {convertMutation.isPending ? "Converting..." : "Confirm Conversion"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Convert Project to Task</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <p className="text-sm text-muted-foreground mb-4">
              Convert "<strong>{sourceProject.name}</strong>" to a task under another project.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="search">Search Projects</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search for a project..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Select Target Project</Label>
            <div className="max-h-60 overflow-y-auto border rounded-md">
              {availableProjects.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  {searchQuery ? "No projects found matching your search." : "No other projects available."}
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {availableProjects.map((project) => (
                    <div
                      key={project.id}
                      className={`p-3 rounded-md cursor-pointer transition-colors ${
                        selectedProjectId === project.id
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted"
                      }`}
                      onClick={() => setSelectedProjectId(project.id)}
                    >
                      <div className="font-medium">{project.name}</div>
                      <div className="text-sm opacity-70">
                        {project.company_name} • {project.type}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={() => handleOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={() => setShowConfirmation(true)}
              disabled={!selectedProjectId}
            >
              Continue
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}