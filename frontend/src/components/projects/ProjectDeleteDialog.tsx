import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Project } from "@/types/project";
import { Loader2 } from "lucide-react";

interface ProjectDeleteDialogProps {
  project: Project;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
  taskCount?: number;
  subTaskCount?: number;
}

export function ProjectDeleteDialog({
  project,
  open,
  onOpenChange,
  onConfirm,
  isDeleting,
  taskCount = 0,
  subTaskCount = 0,
}: ProjectDeleteDialogProps) {
  const [confirmText, setConfirmText] = useState("");
  const totalItems = taskCount + subTaskCount;
  const requiresNameConfirmation = project.status === 'in-progress' && totalItems > 5;

  const canConfirm = requiresNameConfirmation 
    ? confirmText === project.name 
    : true;

  const handleConfirm = () => {
    if (canConfirm) {
      onConfirm();
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Project</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                Are you sure you want to delete <strong>"{project.name}"</strong>?
              </p>
              
              {totalItems > 0 && (
                <div className="bg-muted p-3 rounded-md">
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    This will permanently delete:
                  </p>
                  <ul className="text-sm space-y-1">
                    {taskCount > 0 && <li>• {taskCount} task{taskCount > 1 ? 's' : ''}</li>}
                    {subTaskCount > 0 && <li>• {subTaskCount} sub-task{subTaskCount > 1 ? 's' : ''}</li>}
                  </ul>
                </div>
              )}

              {requiresNameConfirmation && (
                <div className="space-y-2">
                  <Label htmlFor="confirm-name">
                    Type the project name to confirm:
                  </Label>
                  <Input
                    id="confirm-name"
                    value={confirmText}
                    onChange={(e) => setConfirmText(e.target.value)}
                    placeholder={project.name}
                  />
                </div>
              )}

              <p className="text-sm text-destructive">
                This action cannot be undone.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!canConfirm || isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Project'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}