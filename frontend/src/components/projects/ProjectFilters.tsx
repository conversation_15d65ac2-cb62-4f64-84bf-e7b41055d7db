import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Search, X, Archive } from "lucide-react";
import { PROJECT_TYPES, PROJECT_STATUSES } from "@/lib/constants";
import { useTeamMembers } from "@/hooks/useTeamMembers";
import { PRIORITY_CONFIG } from "@/lib/priority-utils";

interface ProjectFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  typeFilter: string;
  onTypeFilterChange: (value: string) => void;
  statusFilter: string;
  onStatusFilterChange: (value: string) => void;
  leadFilter: string;
  onLeadFilterChange: (value: string) => void;
  priorityFilter: string;
  onPriorityFilterChange: (value: string) => void;
  groupBy: "none" | "start_date" | "end_date" | "status" | "lead";
  onGroupByChange: (value: "none" | "start_date" | "end_date" | "status" | "lead") => void;
  showArchivedProjects: boolean;
  onShowArchivedProjectsChange: (value: boolean) => void;
  onClearFilters: () => void;
  archivedProjectsCount?: number;
}

export function ProjectFilters({
  searchTerm,
  onSearchChange,
  typeFilter,
  onTypeFilterChange,
  statusFilter,
  onStatusFilterChange,
  leadFilter,
  onLeadFilterChange,
  priorityFilter,
  onPriorityFilterChange,
  groupBy,
  onGroupByChange,
  showArchivedProjects,
  onShowArchivedProjectsChange,
  onClearFilters,
  archivedProjectsCount = 0
}: ProjectFiltersProps) {
  const { data: teamMembers, isLoading: isLoadingTeamMembers } = useTeamMembers(true);
  const hasActiveFilters = typeFilter !== 'all' || statusFilter !== 'all' || leadFilter !== 'all' || priorityFilter !== 'all' || searchTerm || groupBy !== 'none';

  return (
    <div className="space-y-4">
      {/* Archive Mode Banner */}
      {showArchivedProjects && (
        <div className="bg-muted/50 border border-border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Archive className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Archive Mode</span>
            <Badge variant="secondary" className="ml-auto">
              {archivedProjectsCount} archived project{archivedProjectsCount !== 1 ? 's' : ''}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Viewing only archived projects. Toggle off to see active projects.
          </p>
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects by name or customer..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-8"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            id="show-archived"
            checked={showArchivedProjects}
            onCheckedChange={onShowArchivedProjectsChange}
          />
          <Label htmlFor="show-archived" className="text-sm whitespace-nowrap flex items-center gap-2">
            <Archive className="h-3 w-3" />
            View archived projects
            {archivedProjectsCount > 0 && (
              <Badge variant="outline" className="ml-1 text-xs">
                {archivedProjectsCount}
              </Badge>
            )}
          </Label>
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={typeFilter} onValueChange={onTypeFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Project Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {PROJECT_TYPES.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {PROJECT_STATUSES.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={leadFilter} onValueChange={onLeadFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Project Lead" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Leads</SelectItem>
            {teamMembers?.map((member) => (
              <SelectItem key={member.id} value={member.name}>
                {member.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={onPriorityFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priorities</SelectItem>
            {Object.entries(PRIORITY_CONFIG).map(([priority, config]) => (
              <SelectItem key={priority} value={priority}>
                {priority} - {config.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={groupBy} onValueChange={onGroupByChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Group By" />
          </SelectTrigger>
          <SelectContent>
             <SelectItem value="none">No Grouping</SelectItem>
             <SelectItem value="status">Status</SelectItem>
             <SelectItem value="start_date">Start Date</SelectItem>
             <SelectItem value="end_date">End Date</SelectItem>
             <SelectItem value="lead">Lead</SelectItem>
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <Button variant="outline" onClick={onClearFilters} className="flex items-center gap-2">
            <X className="h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}