import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from "lucide-react";
import { PROJECT_STATUSES } from "@/lib/constants";
import { cn } from "@/lib/utils";

interface ProjectStatusDropdownProps {
  projectId: string;
  currentStatus: string;
  className?: string;
  disabled?: boolean;
}

export function ProjectStatusDropdown({ 
  projectId, 
  currentStatus, 
  className,
  disabled = false
}: ProjectStatusDropdownProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUpdating, setIsUpdating] = useState(false);

  const updateStatusMutation = useMutation({
    mutationFn: async (newStatus: string) => {
      setIsUpdating(true);
      const { error } = await supabase
        .from('projects')
        .update({ status: newStatus })
        .eq('id', projectId);

      if (error) throw error;
      return newStatus;
    },
    onSuccess: (newStatus) => {
      queryClient.invalidateQueries({ queryKey: ['project-detail', projectId] });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Success",
        description: `Project status updated to ${PROJECT_STATUSES.find(s => s.value === newStatus)?.label}`,
      });
      setIsUpdating(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update project status. Please try again.",
        variant: "destructive",
      });
      console.error('Error updating project status:', error);
      setIsUpdating(false);
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'backlog':
        return 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800';
      case 'not-started':
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-800 dark:border-gray-700';
      case 'in-progress':
        return 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800';
      case 'archived':
        return 'text-gray-500 bg-gray-100 border-gray-300 dark:text-gray-500 dark:bg-gray-900 dark:border-gray-600';
      default:
        return 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800';
    }
  };

  const handleStatusChange = (newStatus: string) => {
    if (newStatus !== currentStatus) {
      updateStatusMutation.mutate(newStatus);
    }
  };

  const currentStatusLabel = PROJECT_STATUSES.find(s => s.value === currentStatus)?.label || currentStatus;

  if (isUpdating) {
    return (
      <Badge variant="outline" className={cn("text-sm animate-pulse", getStatusColor(currentStatus), className)}>
        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
        Updating...
      </Badge>
    );
  }

  if (disabled) {
    return (
      <Badge variant="outline" className={cn("text-sm", getStatusColor(currentStatus), className)}>
        <div className="flex items-center gap-2">
          <div 
            className={cn(
              "w-3 h-3 rounded-full",
              currentStatus === 'backlog' && "bg-purple-600",
              currentStatus === 'not-started' && "bg-gray-600",
              currentStatus === 'in-progress' && "bg-blue-600",
              currentStatus === 'completed' && "bg-green-600",
              currentStatus === 'archived' && "bg-gray-500"
            )} 
          />
          {currentStatusLabel}
        </div>
      </Badge>
    );
  }

  return (
    <Select value={currentStatus} onValueChange={handleStatusChange}>
      <SelectTrigger 
        className={cn(
          "text-sm cursor-pointer hover:opacity-80 transition-opacity border h-auto px-3 py-1 rounded-md w-auto",
          getStatusColor(currentStatus),
          className
        )}
      >
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {PROJECT_STATUSES.map((status) => (
          <SelectItem 
            key={status.value} 
            value={status.value}
            className="cursor-pointer py-3 px-4"
          >
            <div className="flex items-center gap-2">
              <div 
                className={cn(
                  "w-3 h-3 rounded-full",
                  status.value === 'backlog' && "bg-purple-600",
                  status.value === 'not-started' && "bg-gray-600",
                  status.value === 'in-progress' && "bg-blue-600",
                  status.value === 'completed' && "bg-green-600",
                  status.value === 'archived' && "bg-gray-500"
                )} 
              />
              {status.label}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}