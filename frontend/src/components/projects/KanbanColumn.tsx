import { useDroppable } from '@dnd-kit/core';
import { DraggableProjectCard } from './DraggableProjectCard';
import { ProjectWithDetails } from '@/types/project';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { memo } from 'react';

interface KanbanColumnProps {
  id: string;
  title: string;
  projects: ProjectWithDetails[];
  isDraggedOver?: boolean;
  draggedItemId?: string | null;
  isUpdating?: boolean;
}

const KanbanColumnComponent = ({ 
  id, 
  title, 
  projects, 
  isDraggedOver = false, 
  draggedItemId = null,
  isUpdating = false 
}: KanbanColumnProps) => {
  const { setNodeRef, isOver } = useDroppable({
    id: id,
  });

  const getColumnColor = (status: string) => {
    const baseColors = {
      'not-started': 'border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/20',
      'in-progress': 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20',
      'completed': 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20',
      'backlog': 'border-gray-200 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800/20',
    };
    
    return baseColors[status as keyof typeof baseColors] || 'border-border bg-background';
  };

  const getDropZoneStyles = () => {
    if (isOver || isDraggedOver) {
      return 'border-primary bg-primary/10 ring-2 ring-primary/20 ring-offset-2 shadow-lg transition-all duration-200 ease-out';
    }
    return 'transition-all duration-200 ease-out';
  };

  return (
    <div 
      ref={setNodeRef}
      className={`
        flex flex-col rounded-lg border-2 h-full will-change-transform
        ${getColumnColor(id)}
        ${getDropZoneStyles()}
        ${isUpdating ? 'opacity-75' : ''}
        ${isDraggedOver ? 'z-10' : ''}
      `}
      role="region"
      aria-label={`${title} column with ${projects.length} projects`}
    >
      {/* Column Header - Also acts as drop zone */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-foreground">{title}</h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground bg-background rounded-full px-2 py-1 min-w-[24px] text-center">
              {projects.length}
            </span>
            {isUpdating && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
            )}
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 p-3 space-y-3 overflow-y-auto min-h-[200px]">
        {projects.map(project => (
          <DraggableProjectCard
            key={project.id}
            project={project}
            tasks={project.tasks || []}
          />
        ))}

        {/* Drop Zone Indicator */}
        {(isOver || isDraggedOver) && projects.length === 0 && (
          <div className="border-2 border-dashed border-primary/50 rounded-lg p-8 text-center text-primary/70 bg-primary/5 animate-pulse">
            <div className="text-sm font-medium">Drop here to move project</div>
          </div>
        )}

        {/* Insertion Indicator for non-empty columns */}
        {(isOver || isDraggedOver) && projects.length > 0 && (
          <div className="border-2 border-dashed border-primary/50 rounded-lg p-4 text-center text-primary/70 bg-primary/5 animate-pulse">
            <div className="text-xs">Drop here to add to this column</div>
          </div>
        )}

        {/* Empty State */}
        {projects.length === 0 && !isOver && !isDraggedOver && (
          <div className="text-center py-8 text-muted-foreground">
            <div className="mb-2">No projects</div>
            <Button variant="ghost" size="sm" className="text-xs">
              <Plus className="w-3 h-3 mr-1" />
              Add Item
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export const KanbanColumn = memo(KanbanColumnComponent);