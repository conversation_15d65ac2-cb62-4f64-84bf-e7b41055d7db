import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { User, Mail, Building, Edit2 } from "lucide-react";
import { ProjectWithDetails } from "@/types/project";
import { EditTeamDialog } from "./EditTeamDialog";

interface ProjectTeamCardProps {
  project: ProjectWithDetails;
}

export function ProjectTeamCard({ project }: ProjectTeamCardProps) {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Team
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setEditDialogOpen(true)}
            className="h-8 w-8 p-0"
          >
            <Edit2 className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Project Lead */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">Project Lead</Badge>
          </div>
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs bg-primary/10 text-primary">
                {getInitials(project.project_lead)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">{project.project_lead}</p>
              {(project as any).project_lead_member?.email && (
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  {(project as any).project_lead_member.email}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Customer Lead */}
        {project.customer_lead && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">Customer Lead</Badge>
            </div>
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs bg-orange-100 text-orange-700">
                  {getInitials(project.customer_lead)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-sm">{project.customer_lead}</p>
                {(project as any).customer_lead_member?.email && (
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Mail className="h-3 w-3" />
                    {(project as any).customer_lead_member.email}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Customer Information */}
        {(project.customer_name || project.customer_contact) && (
          <div className="pt-2 border-t space-y-2">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Customer</span>
            </div>
            {project.customer_name && (
              <p className="text-sm font-medium">{project.customer_name}</p>
            )}
            {project.customer_contact && (
              <p className="text-xs text-muted-foreground">{project.customer_contact}</p>
            )}
          </div>
        )}
      </CardContent>

      <EditTeamDialog
        project={project}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
      />
    </Card>
  );
}