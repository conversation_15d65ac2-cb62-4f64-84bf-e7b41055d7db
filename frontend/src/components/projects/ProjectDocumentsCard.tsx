import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, FileText, Globe } from "lucide-react";
import { ProjectWithDetails } from "@/types/project";

interface ProjectDocumentsCardProps {
  project: ProjectWithDetails;
}

export function ProjectDocumentsCard({ project }: ProjectDocumentsCardProps) {
  const handleOpenPRDDocument = () => {
    if (project.prd_document_link) {
      window.open(project.prd_document_link, '_blank', 'noopener,noreferrer');
    }
  };

  const handleOpenPOCUrl = () => {
    if (project.poc_url) {
      window.open(project.poc_url, '_blank', 'noopener,noreferrer');
    }
  };

  const hasDocuments = project.prd_document_link || project.poc_url;

  if (!hasDocuments) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">No documents available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Documents
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {project.prd_document_link && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenPRDDocument}
            className="w-full justify-start"
          >
            <FileText className="h-4 w-4 mr-2" />
            PRD Document
            <ExternalLink className="h-3 w-3 ml-auto" />
          </Button>
        )}

        {project.poc_url && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenPOCUrl}
            className="w-full justify-start"
          >
            <Globe className="h-4 w-4 mr-2" />
            Proof of Concept
            <ExternalLink className="h-3 w-3 ml-auto" />
          </Button>
        )}
      </CardContent>
    </Card>
  );
}