import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLef<PERSON>, Brain } from 'lucide-react';

interface PRDQualityAnalyzerProps {
  projectId: string;
  sections: any[];
  onBack: () => void;
}

export const PRDQualityAnalyzer: React.FC<PRDQualityAnalyzerProps> = ({
  projectId,
  sections,
  onBack
}) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Brain className="h-5 w-5 text-purple-600" />
            PRD Quality Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <p className="text-gray-600">
              AI-Powered PRD Quality Analysis - Coming Soon
            </p>
            <p className="text-sm text-gray-500 mt-2">
              This will analyze PRD quality, completeness, and provide improvement suggestions
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
