import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  CheckCircle2, 
  AlertTriangle, 
  Clock, 
  Edit3,
  Eye,
  Brain,
  Sparkles,
  Target,
  Shield,
  Code,
  Settings,
  Calendar,
  Server,
  Users,
  BookOpen,
  Wrench,
  Scale,
  TrendingUp,
  Award
} from 'lucide-react';

// Import PRD section components (to be created)
import { PRDSectionEditor } from './PRDSectionEditor';
import { PRDQualityAnalyzer } from './PRDQualityAnalyzer';

interface PRDDocumentSectionProps {
  projectId: string;
}

interface PRDSectionInfo {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'empty' | 'partial' | 'complete' | 'needs-review';
  completeness: number;
  itemCount: number;
  lastUpdated?: string;
  aiSuggestions: number;
}

export const PRDDocumentSection: React.FC<PRDDocumentSectionProps> = ({ projectId }) => {
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'editor' | 'quality'>('overview');

  // PRD Sections Configuration
  const prdSections: PRDSectionInfo[] = [
    {
      id: 'project_overview',
      title: '1. Project Overview',
      description: 'Vision, goals, scope, stakeholders, and success criteria',
      icon: <Target className="h-4 w-4" />,
      status: 'complete',
      completeness: 100,
      itemCount: 8,
      lastUpdated: '2 hours ago',
      aiSuggestions: 0
    },
    {
      id: 'functional_requirements',
      title: '2. Functional Requirements',
      description: 'User stories, features, workflows, and business logic',
      icon: <Users className="h-4 w-4" />,
      status: 'complete',
      completeness: 95,
      itemCount: 24,
      lastUpdated: '1 hour ago',
      aiSuggestions: 2
    },
    {
      id: 'technical_requirements',
      title: '3. Technical Requirements',
      description: 'Architecture, APIs, data models, and integration points',
      icon: <Code className="h-4 w-4" />,
      status: 'partial',
      completeness: 70,
      itemCount: 15,
      lastUpdated: '3 hours ago',
      aiSuggestions: 5
    },
    {
      id: 'non_functional_requirements',
      title: '4. Non-Functional Requirements',
      description: 'Performance, scalability, reliability, and usability',
      icon: <TrendingUp className="h-4 w-4" />,
      status: 'partial',
      completeness: 60,
      itemCount: 12,
      lastUpdated: '4 hours ago',
      aiSuggestions: 3
    },
    {
      id: 'integration_requirements',
      title: '5. Integration Requirements',
      description: 'Third-party APIs, internal systems, and data exchange',
      icon: <Settings className="h-4 w-4" />,
      status: 'needs-review',
      completeness: 80,
      itemCount: 8,
      lastUpdated: '1 day ago',
      aiSuggestions: 4
    },
    {
      id: 'security_rbac',
      title: '6. Security & RBAC',
      description: 'Authentication, authorization, data protection, and access control',
      icon: <Shield className="h-4 w-4" />,
      status: 'partial',
      completeness: 45,
      itemCount: 10,
      lastUpdated: '2 days ago',
      aiSuggestions: 7
    },
    {
      id: 'technology_stack',
      title: '7. Technology Stack',
      description: 'Languages, frameworks, databases, and infrastructure choices',
      icon: <Server className="h-4 w-4" />,
      status: 'complete',
      completeness: 90,
      itemCount: 18,
      lastUpdated: '6 hours ago',
      aiSuggestions: 1
    },
    {
      id: 'timeline_milestones',
      title: '8. Timeline & Milestones',
      description: 'Project phases, dependencies, and critical path',
      icon: <Calendar className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 8
    },
    {
      id: 'deployment_infrastructure',
      title: '9. Deployment & Infrastructure',
      description: 'Environments, CI/CD, monitoring, and scaling strategy',
      icon: <Server className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 6
    },
    {
      id: 'existing_systems_impact',
      title: '10. Existing Systems Impact',
      description: 'Dependencies, migration plans, and rollback strategies',
      icon: <Settings className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 4
    },
    {
      id: 'training_documentation',
      title: '11. Training & Documentation',
      description: 'User training, technical docs, and knowledge transfer',
      icon: <BookOpen className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 5
    },
    {
      id: 'maintenance_support',
      title: '12. Maintenance & Support',
      description: 'Support model, SLAs, and long-term roadmap',
      icon: <Wrench className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 3
    },
    {
      id: 'compliance_licensing',
      title: '13. Compliance & Licensing',
      description: 'Regulations, standards, and software licenses',
      icon: <Scale className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 2
    },
    {
      id: 'risk_assessment',
      title: '14. Risk Assessment',
      description: 'Technical, business, and operational risks with mitigation',
      icon: <AlertTriangle className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 6
    },
    {
      id: 'success_metrics',
      title: '15. Success Metrics',
      description: 'KPIs, business metrics, and ROI calculations',
      icon: <Award className="h-4 w-4" />,
      status: 'empty',
      completeness: 0,
      itemCount: 0,
      aiSuggestions: 4
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'bg-green-100 text-green-800 border-green-200';
      case 'partial': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs-review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'empty': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'partial': return <Clock className="h-4 w-4 text-blue-600" />;
      case 'needs-review': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'empty': return <FileText className="h-4 w-4 text-gray-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const overallCompleteness = Math.round(
    prdSections.reduce((sum, section) => sum + section.completeness, 0) / prdSections.length
  );

  const completedSections = prdSections.filter(s => s.status === 'complete').length;
  const totalSuggestions = prdSections.reduce((sum, section) => sum + section.aiSuggestions, 0);

  if (viewMode === 'editor' && selectedSection) {
    return (
      <PRDSectionEditor 
        projectId={projectId}
        sectionId={selectedSection}
        onBack={() => setViewMode('overview')}
      />
    );
  }

  if (viewMode === 'quality') {
    return (
      <PRDQualityAnalyzer 
        projectId={projectId}
        sections={prdSections}
        onBack={() => setViewMode('overview')}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* PRD Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{overallCompleteness}%</div>
            <p className="text-sm text-gray-600">Overall Completeness</p>
            <Progress value={overallCompleteness} className="mt-2 h-1" />
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{completedSections}</div>
            <p className="text-sm text-gray-600">Sections Complete</p>
            <p className="text-xs text-gray-500">of {prdSections.length} total</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{totalSuggestions}</div>
            <p className="text-sm text-gray-600">AI Suggestions</p>
            <p className="text-xs text-gray-500">Ready to apply</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Button 
                onClick={() => setViewMode('quality')}
                className="w-full"
                variant="outline"
              >
                <Brain className="h-4 w-4 mr-2" />
                Quality Analysis
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* PRD Sections Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            PRD Document Sections
          </CardTitle>
          <CardDescription>
            Comprehensive Product Requirements Document with AI-powered insights and suggestions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {prdSections.map((section) => (
              <Card 
                key={section.id}
                className="cursor-pointer transition-all hover:shadow-md border-l-4 border-l-blue-500"
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {section.icon}
                      <h3 className="font-semibold text-sm">{section.title}</h3>
                    </div>
                    {getStatusIcon(section.status)}
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                    {section.description}
                  </p>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-xs">
                      <span>Completeness</span>
                      <span className="font-medium">{section.completeness}%</span>
                    </div>
                    <Progress value={section.completeness} className="h-1" />
                    
                    <div className="flex items-center justify-between text-xs">
                      <Badge className={getStatusColor(section.status)}>
                        {section.status.replace('_', ' ')}
                      </Badge>
                      {section.aiSuggestions > 0 && (
                        <Badge variant="outline" className="text-purple-600">
                          <Sparkles className="h-3 w-3 mr-1" />
                          {section.aiSuggestions} AI tips
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between pt-2">
                      <span className="text-xs text-gray-500">
                        {section.itemCount} items
                      </span>
                      <div className="flex items-center gap-1">
                        <Button 
                          size="sm" 
                          variant="ghost"
                          onClick={() => {
                            setSelectedSection(section.id);
                            setViewMode('editor');
                          }}
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
