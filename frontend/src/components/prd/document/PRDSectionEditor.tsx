import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Save, Eye } from 'lucide-react';

interface PRDSectionEditorProps {
  projectId: string;
  sectionId: string;
  onBack: () => void;
}

export const PRDSectionEditor: React.FC<PRDSectionEditorProps> = ({
  projectId,
  sectionId,
  onBack
}) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Button variant="ghost" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
              PRD Section Editor
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button size="sm">
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <p className="text-gray-600">
              PRD Section Editor for {sectionId} - Coming Soon
            </p>
            <p className="text-sm text-gray-500 mt-2">
              This will be a rich editor for editing PRD sections with AI assistance
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
