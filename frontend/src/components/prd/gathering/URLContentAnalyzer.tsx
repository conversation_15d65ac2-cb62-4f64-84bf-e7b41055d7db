import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Link, 
  Plus, 
  X, 
  Loader2,
  CheckCircle2,
  ExternalLink
} from 'lucide-react';

interface URLContentAnalyzerProps {
  projectId: string;
  onContentCaptured: (content: any) => void;
  onProcessingChange: (isProcessing: boolean) => void;
}

export const URLContentAnalyzer: React.FC<URLContentAnalyzerProps> = ({
  projectId,
  onContentCaptured,
  onProcessingChange
}) => {
  const [urls, setUrls] = useState<string[]>(['']);
  const [isProcessing, setIsProcessing] = useState(false);

  const addUrlField = () => {
    setUrls([...urls, '']);
  };

  const removeUrlField = (index: number) => {
    setUrls(urls.filter((_, i) => i !== index));
  };

  const updateUrl = (index: number, value: string) => {
    const newUrls = [...urls];
    newUrls[index] = value;
    setUrls(newUrls);
  };

  const analyzeUrls = async () => {
    const validUrls = urls.filter(url => url.trim());
    if (validUrls.length === 0) return;

    setIsProcessing(true);
    onProcessingChange(true);

    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      onProcessingChange(false);
      
      validUrls.forEach(url => {
        onContentCaptured({
          type: 'url',
          source: url,
          content: `Analyzed content from ${url}`,
          analysis: { confidence: 0.8, extracted_requirements: [] }
        });
      });
    }, 2000);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="text-center mb-4">
            <Link className="h-12 w-12 mx-auto mb-2 text-orange-600" />
            <h3 className="text-lg font-semibold">Web Content Analysis</h3>
            <p className="text-sm text-gray-600">
              Analyze websites, documentation, repositories, and online resources
            </p>
          </div>

          <div className="space-y-3">
            {urls.map((url, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={url}
                  onChange={(e) => updateUrl(index, e.target.value)}
                  placeholder="https://example.com/documentation"
                  className="flex-1"
                />
                {urls.length > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeUrlField(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-between">
            <Button variant="outline" onClick={addUrlField}>
              <Plus className="h-4 w-4 mr-2" />
              Add URL
            </Button>
            
            <Button 
              onClick={analyzeUrls}
              disabled={isProcessing || !urls.some(url => url.trim())}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Link className="h-4 w-4 mr-2" />
                  Analyze URLs
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-orange-50 border-orange-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-orange-800 mb-2">Supported Content Types</h4>
          <div className="grid grid-cols-2 gap-2 text-sm text-orange-700">
            <div>• Documentation sites</div>
            <div>• GitHub repositories</div>
            <div>• Figma designs</div>
            <div>• Confluence pages</div>
            <div>• API documentation</div>
            <div>• Competitor websites</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
