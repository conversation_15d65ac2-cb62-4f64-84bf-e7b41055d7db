import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageSquare, 
  Send, 
  Bot, 
  User, 
  Sparkles, 
  Brain,
  Loader2,
  CheckCircle2,
  Lightbulb
} from 'lucide-react';
import { prdApiClient } from '@/services/api/prd-api';
import { useToast } from '@/hooks/use-toast';

interface AIConversationInterfaceProps {
  projectId: string;
  onContentCaptured: (content: any) => void;
  onProcessingChange: (isProcessing: boolean) => void;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  extractedRequirements?: any[];
  suggestions?: string[];
}

export const AIConversationInterface: React.FC<AIConversationInterfaceProps> = ({
  projectId,
  onContentCaptured,
  onProcessingChange
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [chatMode, setChatMode] = useState('structured_interview');
  const [chatId, setChatId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const chatModes = [
    {
      id: 'structured_interview',
      name: 'Structured Interview',
      description: 'AI asks systematic questions to gather comprehensive requirements'
    },
    {
      id: 'free_form_discussion',
      name: 'Free Discussion',
      description: 'Natural conversation about your project needs'
    },
    {
      id: 'technical_deep_dive',
      name: 'Technical Deep Dive',
      description: 'Focus on architecture, APIs, and technical requirements'
    },
    {
      id: 'business_requirements',
      name: 'Business Focus',
      description: 'Concentrate on business goals, metrics, and success criteria'
    },
    {
      id: 'user_story_workshop',
      name: 'User Story Workshop',
      description: 'Create detailed user stories and acceptance criteria'
    }
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const startChatSession = async () => {
    try {
      onProcessingChange(true);
      const result = await prdApiClient.startChatSession(
        `session-${projectId}`, // TODO: Use real session ID
        chatMode
      );
      
      setChatId(result.chat_id);
      
      // Add initial AI message
      const initialMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        role: 'assistant',
        content: result.initial_message,
        timestamp: new Date().toISOString()
      };
      
      setMessages([initialMessage]);
      
    } catch (error: any) {
      console.error('Failed to start chat session:', error);
      toast({
        title: "Chat Session Failed",
        description: error.message || "Could not start AI conversation. Please try again.",
        variant: "destructive",
      });
    } finally {
      onProcessingChange(false);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !chatId) return;

    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);
    onProcessingChange(true);

    try {
      const response = await prdApiClient.sendChatMessage(chatId, inputMessage);
      
      const aiMessage: ChatMessage = {
        id: `msg-${Date.now()}-ai`,
        role: 'assistant',
        content: response.response,
        timestamp: new Date().toISOString(),
        extractedRequirements: response.extracted_requirements,
        suggestions: []
      };

      setMessages(prev => [...prev, aiMessage]);

      // If requirements were extracted, notify parent
      if (response.extracted_requirements && response.extracted_requirements.length > 0) {
        onContentCaptured({
          type: 'chat',
          source: 'AI Conversation',
          content: response.extracted_requirements,
          analysis: {
            confidence: 0.9,
            extracted_requirements: response.extracted_requirements,
            suggestions: []
          }
        });

        toast({
          title: "Requirements Extracted",
          description: `Found ${response.extracted_requirements.length} new requirements from conversation`,
        });
      }

    } catch (error: any) {
      console.error('Failed to send message:', error);
      
      const errorMessage: ChatMessage = {
        id: `msg-${Date.now()}-error`,
        role: 'assistant',
        content: "I apologize, but I'm having trouble processing your message. Please try again.",
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Message Failed",
        description: error.message || "Could not send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsTyping(false);
      onProcessingChange(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="space-y-4">
      {/* Chat Mode Selection */}
      {!chatId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-600" />
              Choose Conversation Mode
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Select value={chatMode} onValueChange={setChatMode}>
              <SelectTrigger>
                <SelectValue placeholder="Select conversation mode" />
              </SelectTrigger>
              <SelectContent>
                {chatModes.map((mode) => (
                  <SelectItem key={mode.id} value={mode.id}>
                    <div>
                      <div className="font-medium">{mode.name}</div>
                      <div className="text-xs text-gray-500">{mode.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button onClick={startChatSession} className="w-full">
              <MessageSquare className="h-4 w-4 mr-2" />
              Start AI Conversation
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Chat Interface */}
      {chatId && (
        <Card className="h-96">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5 text-blue-600" />
                AI Requirements Assistant
              </CardTitle>
              <Badge variant="outline">
                {chatModes.find(m => m.id === chatMode)?.name}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="flex flex-col h-full p-0">
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                      <span className="text-xs opacity-75">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    
                    {/* Extracted Requirements */}
                    {message.extractedRequirements && message.extractedRequirements.length > 0 && (
                      <div className="mt-2 p-2 bg-green-50 rounded border">
                        <div className="flex items-center gap-1 mb-1">
                          <CheckCircle2 className="h-3 w-3 text-green-600" />
                          <span className="text-xs font-medium text-green-800">
                            Extracted {message.extractedRequirements.length} requirements
                          </span>
                        </div>
                        {message.extractedRequirements.slice(0, 2).map((req, idx) => (
                          <div key={idx} className="text-xs text-green-700">
                            • {req.title}
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* AI Suggestions */}
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="mt-2 p-2 bg-yellow-50 rounded border">
                        <div className="flex items-center gap-1 mb-1">
                          <Lightbulb className="h-3 w-3 text-yellow-600" />
                          <span className="text-xs font-medium text-yellow-800">
                            Suggestions
                          </span>
                        </div>
                        {message.suggestions.slice(0, 2).map((suggestion, idx) => (
                          <div key={idx} className="text-xs text-yellow-700">
                            • {suggestion}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex gap-3 justify-start">
                  <div className="bg-gray-100 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <Bot className="h-4 w-4" />
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Describe your project requirements..."
                  disabled={isTyping}
                  className="flex-1"
                />
                <Button 
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Press Enter to send, Shift+Enter for new line
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chat Summary */}
      {messages.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-green-800">Conversation Progress</h3>
                <p className="text-sm text-green-600">
                  {messages.filter(m => m.role === 'user').length} messages exchanged, 
                  {messages.reduce((sum, m) => sum + (m.extractedRequirements?.length || 0), 0)} requirements captured
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Get Summary
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
