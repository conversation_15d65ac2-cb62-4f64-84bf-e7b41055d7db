import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { prdApiClient } from '@/services/api/prd-api';
import {
  Mic,
  Square,
  Play,
  Pause,
  Upload,
  Loader2,
  CheckCircle2
} from 'lucide-react';

interface AudioRecordingInterfaceProps {
  projectId: string;
  onContentCaptured: (content: any) => void;
  onProcessingChange: (isProcessing: boolean) => void;
}

export const AudioRecordingInterface: React.FC<AudioRecordingInterfaceProps> = ({
  projectId,
  onContentCaptured,
  onProcessingChange
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const startRecording = () => {
    setIsRecording(true);
    // TODO: Implement real audio recording
  };

  const stopRecording = async () => {
    setIsRecording(false);
    setIsProcessing(true);
    onProcessingChange(true);

    try {
      // TODO: Implement real audio recording and processing
      // This would capture audio from microphone and send to backend
      const audioBlob = new Blob([], { type: 'audio/webm' });

      // Call real API for audio processing
      const result = await prdApiClient.uploadAndProcessAudio(
        `session-${projectId}`,
        audioBlob
      );

      onContentCaptured({
        type: 'audio',
        source: 'Voice Recording',
        content: result.transcription,
        analysis: result.analysis
      });
    } catch (error: any) {
      console.error('Audio processing failed:', error);
      // Handle error appropriately
    } finally {
      setIsProcessing(false);
      onProcessingChange(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-8 text-center">
          <div className="space-y-4">
            <div className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center ${
              isRecording ? 'bg-red-100 animate-pulse' : 'bg-gray-100'
            }`}>
              <Mic className={`h-8 w-8 ${isRecording ? 'text-red-600' : 'text-gray-600'}`} />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold">
                {isRecording ? 'Recording...' : 'Ready to Record'}
              </h3>
              <p className="text-sm text-gray-600">
                {isRecording 
                  ? 'Speak clearly about your project requirements'
                  : 'Click to start recording your requirements discussion'
                }
              </p>
            </div>

            <div className="flex justify-center gap-2">
              {!isRecording ? (
                <Button onClick={startRecording} className="bg-red-600 hover:bg-red-700">
                  <Mic className="h-4 w-4 mr-2" />
                  Start Recording
                </Button>
              ) : (
                <Button onClick={stopRecording} variant="outline">
                  <Square className="h-4 w-4 mr-2" />
                  Stop Recording
                </Button>
              )}
            </div>

            {isProcessing && (
              <div className="flex items-center justify-center gap-2 text-blue-600">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processing audio and extracting requirements...</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-800 mb-2">Audio Recording Features</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Real-time transcription with speaker identification</li>
            <li>• Automatic requirement extraction from conversations</li>
            <li>• Support for meeting recordings and interviews</li>
            <li>• AI-powered content analysis and categorization</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
