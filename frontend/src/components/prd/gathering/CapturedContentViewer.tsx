import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Mic, 
  Link, 
  MessageSquare, 
  Image,
  CheckCircle2,
  Eye,
  Edit3,
  Trash2
} from 'lucide-react';

interface CapturedContentViewerProps {
  items: any[];
  projectId: string;
}

export const CapturedContentViewer: React.FC<CapturedContentViewerProps> = ({
  items,
  projectId
}) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'file': return <FileText className="h-4 w-4 text-blue-600" />;
      case 'audio': return <Mic className="h-4 w-4 text-green-600" />;
      case 'url': return <Link className="h-4 w-4 text-orange-600" />;
      case 'chat': return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'text': return <FileText className="h-4 w-4 text-indigo-600" />;
      case 'image': return <Image className="h-4 w-4 text-pink-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'file': return 'bg-blue-100 text-blue-800';
      case 'audio': return 'bg-green-100 text-green-800';
      case 'url': return 'bg-orange-100 text-orange-800';
      case 'chat': return 'bg-purple-100 text-purple-800';
      case 'text': return 'bg-indigo-100 text-indigo-800';
      case 'image': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <Card key={index} className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-base">
                {getTypeIcon(item.type)}
                {item.source}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={getTypeColor(item.type)}>
                  {item.type}
                </Badge>
                {item.analysis?.confidence && (
                  <Badge variant="outline">
                    {Math.round(item.analysis.confidence * 100)}% confidence
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            {/* Content Preview */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700 line-clamp-3">
                {typeof item.content === 'string' 
                  ? item.content 
                  : JSON.stringify(item.content).substring(0, 200) + '...'
                }
              </p>
            </div>

            {/* Extracted Requirements */}
            {item.analysis?.extracted_requirements && item.analysis.extracted_requirements.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">
                    {item.analysis.extracted_requirements.length} Requirements Extracted
                  </span>
                </div>
                <div className="space-y-1">
                  {item.analysis.extracted_requirements.slice(0, 3).map((req: any, reqIndex: number) => (
                    <div key={reqIndex} className="text-xs bg-green-50 p-2 rounded border-l-2 border-green-200">
                      <div className="font-medium text-green-800">{req.title}</div>
                      <div className="text-green-600">{req.description}</div>
                    </div>
                  ))}
                  {item.analysis.extracted_requirements.length > 3 && (
                    <div className="text-xs text-gray-500">
                      +{item.analysis.extracted_requirements.length - 3} more requirements
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="text-xs text-gray-500">
                Captured {new Date().toLocaleTimeString()}
              </div>
              <div className="flex items-center gap-1">
                <Button size="sm" variant="ghost">
                  <Eye className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Edit3 className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost">
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
