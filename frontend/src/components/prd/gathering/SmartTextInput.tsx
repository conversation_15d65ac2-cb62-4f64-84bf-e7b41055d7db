import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { prdApiClient } from '@/services/api/prd-api';
import {
  FileText,
  Sparkles,
  Loader2,
  CheckCircle2,
  Lightbulb
} from 'lucide-react';

interface SmartTextInputProps {
  projectId: string;
  onContentCaptured: (content: any) => void;
  onProcessingChange: (isProcessing: boolean) => void;
}

export const SmartTextInput: React.FC<SmartTextInputProps> = ({
  projectId,
  onContentCaptured,
  onProcessingChange
}) => {
  const [text, setText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const analyzeText = async () => {
    if (!text.trim()) return;

    setIsProcessing(true);
    onProcessingChange(true);

    try {
      // Use real API for text analysis
      const result = await prdApiClient.processTextInput(
        `session-${projectId}`,
        text,
        'Smart text input analysis'
      );

      onContentCaptured({
        type: 'text',
        source: 'Smart Text Input',
        content: text,
        analysis: result
      });

      setSuggestions(result.suggestions || [
        'Consider adding acceptance criteria',
        'Define user personas',
        'Specify technical constraints'
      ]);
    } catch (error: any) {
      console.error('Text analysis failed:', error);
      // Handle error appropriately
    } finally {
      setIsProcessing(false);
      onProcessingChange(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="text-center mb-4">
            <FileText className="h-12 w-12 mx-auto mb-2 text-indigo-600" />
            <h3 className="text-lg font-semibold">Smart Text Input</h3>
            <p className="text-sm text-gray-600">
              AI-assisted text input with real-time suggestions and structure detection
            </p>
          </div>

          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Describe your project requirements in detail. For example:

'We need a user management system where users can register with email, login securely, and manage their profiles. The system should support role-based access control with admin, manager, and regular user roles. Performance should be fast with response times under 200ms..'"
            className="min-h-[200px]"
          />

          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {text.length} characters • AI will analyze and extract structured requirements
            </div>
            
            <Button 
              onClick={analyzeText}
              disabled={isProcessing || !text.trim()}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Analyze Text
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {suggestions.length > 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="h-4 w-4 text-yellow-600" />
              <h4 className="font-medium text-yellow-800">AI Suggestions</h4>
            </div>
            <ul className="text-sm text-yellow-700 space-y-1">
              {suggestions.map((suggestion, index) => (
                <li key={index}>• {suggestion}</li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      <Card className="bg-indigo-50 border-indigo-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-indigo-800 mb-2">Smart Features</h4>
          <div className="grid grid-cols-2 gap-2 text-sm text-indigo-700">
            <div>• Real-time AI suggestions</div>
            <div>• Structure detection</div>
            <div>• Grammar & clarity checks</div>
            <div>• Context awareness</div>
            <div>• Multi-language support</div>
            <div>• Auto-categorization</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
