import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  File, 
  Image, 
  FileText, 
  Database,
  Code,
  Archive,
  X,
  CheckCircle2,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { prdApiClient } from '@/services/api/prd-api';
import { useToast } from '@/hooks/use-toast';

interface MultiModalFileUploadProps {
  projectId: string;
  onContentCaptured: (content: any) => void;
  onProcessingChange: (isProcessing: boolean) => void;
  acceptedTypes?: string;
  mode?: 'document' | 'visual' | 'code' | 'data';
}

interface UploadedFile {
  file: File;
  id: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  analysis?: any;
  error?: string;
}

export const MultiModalFileUpload: React.FC<MultiModalFileUploadProps> = ({
  projectId,
  onContentCaptured,
  onProcessingChange,
  acceptedTypes = '*',
  mode = 'document'
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // File type configurations
  const fileTypeConfig = {
    document: {
      accept: {
        'application/pdf': ['.pdf'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'text/plain': ['.txt'],
        'text/markdown': ['.md']
      },
      maxSize: 10 * 1024 * 1024, // 10MB
      description: 'Upload documents, PDFs, Word files, spreadsheets'
    },
    visual: {
      accept: {
        'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
        'application/json': ['.figma', '.sketch']
      },
      maxSize: 5 * 1024 * 1024, // 5MB
      description: 'Upload images, wireframes, mockups, diagrams'
    },
    code: {
      accept: {
        'application/json': ['.json'],
        'text/yaml': ['.yaml', '.yml'],
        'application/sql': ['.sql'],
        'text/plain': ['.txt', '.md', '.js', '.ts', '.py']
      },
      maxSize: 2 * 1024 * 1024, // 2MB
      description: 'Upload code files, configs, API specs'
    },
    data: {
      accept: {
        'text/csv': ['.csv'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'application/json': ['.json'],
        'application/xml': ['.xml']
      },
      maxSize: 20 * 1024 * 1024, // 20MB
      description: 'Upload data files, spreadsheets, exports'
    }
  };

  const config = fileTypeConfig[mode];

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setIsProcessing(true);
    onProcessingChange(true);

    // Add files to upload queue
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      file,
      id: `${Date.now()}-${Math.random()}`,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    try {
      // Upload and analyze files using real API
      const results = await prdApiClient.uploadAndAnalyzeFiles(
        `session-${projectId}`, // TODO: Use real session ID
        acceptedFiles
      );

      // Update file statuses based on results
      setUploadedFiles(prev => prev.map(uploadFile => {
        const result = results.find(r => r.file.name === uploadFile.file.name);
        if (result) {
          return {
            ...uploadFile,
            status: 'completed',
            progress: 100,
            analysis: result.analysis
          };
        }
        return uploadFile;
      }));

      // Notify parent component
      results.forEach(result => {
        onContentCaptured({
          type: 'file',
          source: result.file.name,
          content: result.extracted_data,
          analysis: result.analysis
        });
      });

      toast({
        title: "Files Processed Successfully",
        description: `Analyzed ${results.length} files and extracted requirements`,
      });

    } catch (error: any) {
      console.error('File upload error:', error);
      
      // Update files with error status
      setUploadedFiles(prev => prev.map(uploadFile => ({
        ...uploadFile,
        status: 'error',
        error: error.message || 'Failed to process file'
      })));

      toast({
        title: "Upload Failed",
        description: error.message || "Failed to process files. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      onProcessingChange(false);
    }
  }, [projectId, onContentCaptured, onProcessingChange, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes === '*' ? config.accept : { [acceptedTypes]: [] },
    maxSize: config.maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf': return <FileText className="h-4 w-4 text-red-600" />;
      case 'docx': case 'doc': return <FileText className="h-4 w-4 text-blue-600" />;
      case 'xlsx': case 'xls': case 'csv': return <Database className="h-4 w-4 text-green-600" />;
      case 'png': case 'jpg': case 'jpeg': case 'gif': case 'svg': return <Image className="h-4 w-4 text-purple-600" />;
      case 'json': case 'yaml': case 'yml': case 'js': case 'ts': return <Code className="h-4 w-4 text-orange-600" />;
      case 'zip': case 'tar': case 'gz': return <Archive className="h-4 w-4 text-gray-600" />;
      default: return <File className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      case 'uploading': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <Card 
        {...getRootProps()} 
        className={`cursor-pointer transition-all border-2 border-dashed ${
          isDragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <CardContent className="p-8 text-center">
          <input {...getInputProps()} />
          <Upload className={`h-12 w-12 mx-auto mb-4 ${
            isDragActive ? 'text-blue-600' : 'text-gray-400'
          }`} />
          
          {isDragActive ? (
            <div>
              <p className="text-lg font-medium text-blue-600">Drop files here...</p>
              <p className="text-sm text-blue-500">AI will analyze and extract requirements</p>
            </div>
          ) : (
            <div>
              <p className="text-lg font-medium text-gray-700">
                Drag & drop files here, or click to browse
              </p>
              <p className="text-sm text-gray-500 mt-2">{config.description}</p>
              <p className="text-xs text-gray-400 mt-1">
                Max file size: {(config.maxSize / (1024 * 1024)).toFixed(0)}MB
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h3 className="font-medium mb-4">Uploaded Files ({uploadedFiles.length})</h3>
            <div className="space-y-3">
              {uploadedFiles.map((uploadFile) => (
                <div key={uploadFile.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0">
                    {getFileIcon(uploadFile.file.name)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium truncate">{uploadFile.file.name}</p>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(uploadFile.status)}>
                          {uploadFile.status}
                        </Badge>
                        {uploadFile.status !== 'error' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(uploadFile.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <p className="text-xs text-gray-500">
                        {(uploadFile.file.size / 1024).toFixed(1)} KB
                      </p>
                      
                      {uploadFile.status === 'uploading' || uploadFile.status === 'processing' ? (
                        <div className="flex items-center gap-2 flex-1">
                          <Progress value={uploadFile.progress} className="flex-1 h-1" />
                          <Loader2 className="h-3 w-3 animate-spin" />
                        </div>
                      ) : uploadFile.status === 'completed' ? (
                        <div className="flex items-center gap-2 text-green-600">
                          <CheckCircle2 className="h-3 w-3" />
                          <span className="text-xs">
                            {uploadFile.analysis?.extracted_requirements?.length || 0} requirements extracted
                          </span>
                        </div>
                      ) : uploadFile.status === 'error' ? (
                        <div className="flex items-center gap-2 text-red-600">
                          <AlertCircle className="h-3 w-3" />
                          <span className="text-xs">{uploadFile.error}</span>
                        </div>
                      ) : null}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processing Status */}
      {isProcessing && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div>
                <p className="font-medium text-blue-800">AI Processing Files...</p>
                <p className="text-sm text-blue-600">
                  Analyzing content and extracting structured requirements
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
