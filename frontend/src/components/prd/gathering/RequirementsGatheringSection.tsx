import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  Mic, 
  MessageSquare, 
  Link, 
  FileText, 
  Image,
  Plus,
  Sparkles,
  Brain,
  Loader2,
  CheckCircle2
} from 'lucide-react';

// Import multi-modal input components (to be created)
import { MultiModalFileUpload } from './MultiModalFileUpload';
import { AudioRecordingInterface } from './AudioRecordingInterface';
import { AIConversationInterface } from './AIConversationInterface';
import { URLContentAnalyzer } from './URLContentAnalyzer';
import { SmartTextInput } from './SmartTextInput';
import { CapturedContentViewer } from './CapturedContentViewer';

interface RequirementsGatheringSectionProps {
  projectId: string;
}

export const RequirementsGatheringSection: React.FC<RequirementsGatheringSectionProps> = ({ 
  projectId 
}) => {
  const [activeInputMethod, setActiveInputMethod] = useState<string>('files');
  const [isProcessing, setIsProcessing] = useState(false);
  const [capturedItems, setCapturedItems] = useState<any[]>([]);

  // Input method configurations
  const inputMethods = [
    {
      id: 'files',
      name: 'Files & Documents',
      icon: <Upload className="h-4 w-4" />,
      description: 'Upload PDFs, Word docs, images, diagrams',
      color: 'blue'
    },
    {
      id: 'audio',
      name: 'Audio Recording',
      icon: <Mic className="h-4 w-4" />,
      description: 'Record meetings, interviews, voice notes',
      color: 'green'
    },
    {
      id: 'chat',
      name: 'AI Conversation',
      icon: <MessageSquare className="h-4 w-4" />,
      description: 'Chat with AI to extract requirements',
      color: 'purple'
    },
    {
      id: 'urls',
      name: 'Web Content',
      icon: <Link className="h-4 w-4" />,
      description: 'Analyze websites, documentation, repos',
      color: 'orange'
    },
    {
      id: 'text',
      name: 'Smart Text Input',
      icon: <FileText className="h-4 w-4" />,
      description: 'AI-assisted text input with suggestions',
      color: 'indigo'
    },
    {
      id: 'images',
      name: 'Visual Content',
      icon: <Image className="h-4 w-4" />,
      description: 'Wireframes, mockups, diagrams, screenshots',
      color: 'pink'
    }
  ];

  const getMethodColor = (color: string) => {
    const colors = {
      blue: 'border-blue-200 bg-blue-50 text-blue-700',
      green: 'border-green-200 bg-green-50 text-green-700',
      purple: 'border-purple-200 bg-purple-50 text-purple-700',
      orange: 'border-orange-200 bg-orange-50 text-orange-700',
      indigo: 'border-indigo-200 bg-indigo-50 text-indigo-700',
      pink: 'border-pink-200 bg-pink-50 text-pink-700'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const handleContentCaptured = (content: any) => {
    setCapturedItems(prev => [...prev, content]);
  };

  return (
    <div className="space-y-6">
      {/* Input Method Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            Multi-Modal Requirements Capture
          </CardTitle>
          <CardDescription>
            Choose your preferred method to capture requirements. AI will analyze and extract structured information from any input.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {inputMethods.map((method) => (
              <Card 
                key={method.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  activeInputMethod === method.id 
                    ? `ring-2 ring-${method.color}-500 ${getMethodColor(method.color)}` 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setActiveInputMethod(method.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${getMethodColor(method.color)}`}>
                        {method.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">{method.name}</h3>
                      </div>
                    </div>
                    {activeInputMethod === method.id && (
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                  <p className="text-xs text-gray-600">{method.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Active Input Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {inputMethods.find(m => m.id === activeInputMethod)?.icon}
              {inputMethods.find(m => m.id === activeInputMethod)?.name}
            </span>
            {isProcessing && (
              <div className="flex items-center gap-2 text-blue-600">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">AI Processing...</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Dynamic Input Interface */}
          {activeInputMethod === 'files' && (
            <MultiModalFileUpload 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
            />
          )}
          
          {activeInputMethod === 'audio' && (
            <AudioRecordingInterface 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
            />
          )}
          
          {activeInputMethod === 'chat' && (
            <AIConversationInterface 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
            />
          )}
          
          {activeInputMethod === 'urls' && (
            <URLContentAnalyzer 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
            />
          )}
          
          {activeInputMethod === 'text' && (
            <SmartTextInput 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
            />
          )}
          
          {activeInputMethod === 'images' && (
            <MultiModalFileUpload 
              projectId={projectId}
              onContentCaptured={handleContentCaptured}
              onProcessingChange={setIsProcessing}
              acceptedTypes="image/*"
              mode="visual"
            />
          )}
        </CardContent>
      </Card>

      {/* Captured Content Viewer */}
      {capturedItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-green-600" />
              Captured Content & AI Analysis
            </CardTitle>
            <CardDescription>
              Review captured content and AI-extracted requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CapturedContentViewer 
              items={capturedItems}
              projectId={projectId}
            />
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-green-800">Ready to Generate PRD?</h3>
              <p className="text-sm text-green-600">
                {capturedItems.length} items captured. AI can now generate your comprehensive PRD document.
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add More Content
              </Button>
              <Button 
                className="bg-green-600 hover:bg-green-700"
                disabled={capturedItems.length === 0}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Generate PRD
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
