import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Sparkles, 
  Brain, 
  Download, 
  Share2, 
  Users, 
  CheckCircle2,
  AlertCircle,
  Clock,
  TrendingUp
} from 'lucide-react';

// Import the new PRD components (to be created)
import { RequirementsGatheringSection } from './gathering/RequirementsGatheringSection';
import { PRDDocumentSection } from './document/PRDDocumentSection';

interface PRDRequirementsTabProps {
  projectId: string;
}

export const PRDRequirementsTab: React.FC<PRDRequirementsTabProps> = ({ projectId }) => {
  const [activeTab, setActiveTab] = useState<'gathering' | 'prd'>('gathering');
  
  // Mock data for demonstration - will be replaced with real API calls
  const gatheringStats = {
    totalSessions: 3,
    completedSessions: 2,
    capturedItems: 47,
    processingItems: 3,
    extractedRequirements: 28,
    overallProgress: 75
  };

  const prdStats = {
    totalSections: 15,
    completedSections: 8,
    inProgressSections: 4,
    emptySections: 3,
    overallCompleteness: 67,
    qualityScore: 82,
    lastUpdated: '2 hours ago'
  };

  return (
    <div className="space-y-6">
      {/* Header with Overview Stats */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            AI-Powered Requirements & PRD System
          </CardTitle>
          <CardDescription>
            Comprehensive requirements gathering and Product Requirements Document generation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{gatheringStats.capturedItems}</div>
              <p className="text-sm text-gray-600">Items Captured</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{gatheringStats.extractedRequirements}</div>
              <p className="text-sm text-gray-600">Requirements Extracted</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{prdStats.completedSections}</div>
              <p className="text-sm text-gray-600">PRD Sections Complete</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{prdStats.qualityScore}%</div>
              <p className="text-sm text-gray-600">Quality Score</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'gathering' | 'prd')}>
        <div className="flex items-center justify-between">
          <TabsList className="grid w-auto grid-cols-2">
            <TabsTrigger value="gathering" className="flex items-center gap-2 px-6 py-3">
              <Sparkles className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">Requirements Gathering</div>
                <div className="text-xs text-gray-500">{gatheringStats.overallProgress}% Complete</div>
              </div>
            </TabsTrigger>
            <TabsTrigger value="prd" className="flex items-center gap-2 px-6 py-3">
              <FileText className="h-4 w-4" />
              <div className="text-left">
                <div className="font-medium">PRD Document</div>
                <div className="text-xs text-gray-500">{prdStats.overallCompleteness}% Complete</div>
              </div>
            </TabsTrigger>
          </TabsList>

          {/* Quick Actions */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Collaborate
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          <TabsContent value="gathering" className="space-y-6">
            {/* Gathering Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-600" />
                    Requirements Gathering Progress
                  </span>
                  <Badge variant="secondary">
                    {gatheringStats.completedSessions}/{gatheringStats.totalSessions} Sessions Complete
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-gray-600">{gatheringStats.overallProgress}%</span>
                    </div>
                    <Progress value={gatheringStats.overallProgress} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{gatheringStats.capturedItems} items captured</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm">{gatheringStats.processingItems} items processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">{gatheringStats.extractedRequirements} requirements extracted</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Requirements Gathering Interface */}
            <RequirementsGatheringSection projectId={projectId} />
          </TabsContent>

          <TabsContent value="prd" className="space-y-6">
            {/* PRD Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    PRD Document Status
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      Quality Score: {prdStats.qualityScore}%
                    </Badge>
                    <Badge variant="outline">
                      Last updated {prdStats.lastUpdated}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Document Completeness</span>
                      <span className="text-sm text-gray-600">{prdStats.overallCompleteness}%</span>
                    </div>
                    <Progress value={prdStats.overallCompleteness} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{prdStats.completedSections} completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm">{prdStats.inProgressSections} in progress</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm">{prdStats.emptySections} empty</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Brain className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">AI-powered insights</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* PRD Document Interface */}
            <PRDDocumentSection projectId={projectId} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};
