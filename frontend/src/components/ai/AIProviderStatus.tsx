import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { apiClient } from '@/lib/api-client';
import { 
  Brain, 
  CheckCircle2, 
  XCircle, 
  AlertTriangle,
  Zap,
  Settings,
  RefreshCw
} from 'lucide-react';

interface AIProviderStatusProps {
  showDetails?: boolean;
}

export const AIProviderStatus: React.FC<AIProviderStatusProps> = ({ 
  showDetails = false 
}) => {
  const { data: providerStatus, isLoading, refetch } = useQuery({
    queryKey: ['ai-provider-status'],
    queryFn: () => apiClient.request('/api/ai-providers/status'),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'openai': return '🤖';
      case 'gemini': return '💎';
      case 'claude': return '🧠';
      case 'kimi': return '🌙';
      case 'openrouter': return '🔀';
      default: return '🤖';
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'openai': return 'OpenAI';
      case 'gemini': return 'Google Gemini';
      case 'claude': return 'Anthropic Claude';
      case 'kimi': return 'Kimi K2';
      case 'openrouter': return 'OpenRouter';
      default: return provider;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm">Checking AI providers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!providerStatus) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-600">
            <XCircle className="h-4 w-4" />
            <span className="text-sm">Failed to check AI provider status</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { available_providers, provider_status, default_provider, total_available, recommendations } = providerStatus;

  if (!showDetails) {
    // Compact view
    return (
      <div className="flex items-center gap-2">
        <Brain className="h-4 w-4 text-purple-600" />
        <span className="text-sm font-medium">
          {total_available} AI Provider{total_available !== 1 ? 's' : ''} Active
        </span>
        {total_available === 0 && (
          <Badge variant="destructive" className="text-xs">
            No AI
          </Badge>
        )}
        {total_available > 0 && (
          <Badge variant="secondary" className="text-xs">
            {getProviderIcon(default_provider)} {getProviderName(default_provider)}
          </Badge>
        )}
      </div>
    );
  }

  // Detailed view
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI Provider Status
        </CardTitle>
        <CardDescription>
          {total_available > 0 
            ? `${total_available} AI provider${total_available !== 1 ? 's' : ''} configured and ready`
            : 'No AI providers configured'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Provider Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(provider_status).map(([provider, status]) => (
            <div 
              key={provider}
              className={`p-3 rounded-lg border ${
                status.available 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getProviderIcon(provider)}</span>
                  <span className="font-medium text-sm">{getProviderName(provider)}</span>
                </div>
                {status.available ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-gray-400" />
                )}
              </div>
              
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-600">Status</span>
                  <Badge 
                    variant={status.available ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {status.available ? 'Ready' : 'Not configured'}
                  </Badge>
                </div>
                
                {status.model && (
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Model</span>
                    <span className="font-mono text-gray-800">{status.model}</span>
                  </div>
                )}
                
                {provider === default_provider && (
                  <div className="flex items-center gap-1 text-xs text-purple-600">
                    <Zap className="h-3 w-3" />
                    <span>Default Provider</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Configuration Summary */}
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800 text-sm">Configuration</span>
          </div>
          <div className="space-y-1 text-xs text-blue-700">
            <div>Default: {getProviderName(default_provider)}</div>
            {providerStatus.fallback_providers?.length > 0 && (
              <div>
                Fallbacks: {providerStatus.fallback_providers.map(p => getProviderName(p)).join(', ')}
              </div>
            )}
          </div>
        </div>

        {/* Recommendations */}
        {recommendations && recommendations.length > 0 && (
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="font-medium text-yellow-800 text-sm">Recommendations</span>
            </div>
            <ul className="space-y-1 text-xs text-yellow-700">
              {recommendations.map((rec, index) => (
                <li key={index}>• {rec}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center gap-2 pt-2 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => refetch()}
          >
            <RefreshCw className="h-3 w-3 mr-2" />
            Refresh Status
          </Button>
          
          {total_available === 0 && (
            <Button variant="outline" size="sm">
              <Settings className="h-3 w-3 mr-2" />
              Configure AI
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
