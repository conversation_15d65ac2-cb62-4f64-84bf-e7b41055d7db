import { Plus, LogIn, Settings } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { UserProfile } from "@/components/auth/UserProfile";
import { GoogleLoginButton } from "@/components/auth/GoogleLoginButton";
export function Header() {
  const { isAuthenticated, user, hasPermission, hasRole } = useAuth();

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link to="/" className="mr-6 flex items-center space-x-2">
            <img src="/lovable-uploads/d0fa832a-0c8f-413b-a7f0-c088cca3f404.png" alt="Orbit Project Pulse" className="h-8 w-auto" />
            <span className="font-bold text-lg">Orbit Project Pulse</span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            <Link to="/" className="transition-colors hover:text-foreground/80 text-foreground">
              Dashboard
            </Link>
            <Link to="/team" className="transition-colors hover:text-foreground/80 text-foreground/60">
              Team
            </Link>
          </nav>
        </div>

        <div className="flex flex-1 items-center justify-end space-x-2">
          {/* Show Admin Panel button for admin users */}
          {(isAuthenticated && (hasRole('admin') || hasRole('super_admin'))) && (
            <Button asChild size="sm" variant="outline">
              <Link to="/admin">
                <Settings className="h-4 w-4 mr-2" />
                Admin
              </Link>
            </Button>
          )}

          {/* Show New Project button only if user has permission */}
          {(isAuthenticated && hasPermission('project:create')) && (
            <Button asChild size="sm">
              <Link to="/projects/new">
                <Plus className="h-4 w-4 mr-2" />
                New Project
              </Link>
            </Button>
          )}

          {/* Authentication UI */}
          {isAuthenticated ? (
            <UserProfile />
          ) : (
            <div className="flex items-center space-x-2">
              <GoogleLoginButton
                className="h-9"
                onSuccess={() => {
                  // Refresh the page to update auth state
                  window.location.reload();
                }}
                onError={(error) => {
                  console.error('Header login error:', error);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </header>
  );
}