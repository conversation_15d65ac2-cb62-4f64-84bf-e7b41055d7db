import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/lib/api-client';
import { 
  Wand2, 
  Globe, 
  Smartphone, 
  Server, 
  Database, 
  ShoppingCart, 
  Users, 
  BarChart3,
  Sparkles,
  Loader2,
  CheckCircle2,
  ArrowRight
} from 'lucide-react';

interface SmartTemplatesProps {
  projectId: string;
  onTemplateGenerated: (requirements: any[]) => void;
}

interface TemplateOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  estimatedRequirements: number;
  complexity: 'Simple' | 'Moderate' | 'Complex';
  industries: string[];
}

const templateOptions: TemplateOption[] = [
  {
    id: 'web-application',
    name: 'Web Application',
    description: 'Modern web application with user authentication, responsive design, and core functionality',
    icon: <Globe className="h-6 w-6 text-blue-600" />,
    category: 'Frontend',
    estimatedRequirements: 12,
    complexity: 'Moderate',
    industries: ['Technology', 'E-commerce', 'Healthcare', 'Education']
  },
  {
    id: 'mobile-app',
    name: 'Mobile Application',
    description: 'Cross-platform mobile app with native features and offline capabilities',
    icon: <Smartphone className="h-6 w-6 text-green-600" />,
    category: 'Mobile',
    estimatedRequirements: 15,
    complexity: 'Complex',
    industries: ['Technology', 'Retail', 'Healthcare', 'Finance']
  },
  {
    id: 'api-service',
    name: 'API Service',
    description: 'RESTful API with authentication, rate limiting, and comprehensive documentation',
    icon: <Server className="h-6 w-6 text-purple-600" />,
    category: 'Backend',
    estimatedRequirements: 10,
    complexity: 'Moderate',
    industries: ['Technology', 'Finance', 'Healthcare', 'Government']
  },
  {
    id: 'data-platform',
    name: 'Data Platform',
    description: 'Data processing and analytics platform with ETL pipelines and visualization',
    icon: <Database className="h-6 w-6 text-orange-600" />,
    category: 'Data',
    estimatedRequirements: 18,
    complexity: 'Complex',
    industries: ['Technology', 'Finance', 'Healthcare', 'Manufacturing']
  },
  {
    id: 'ecommerce-platform',
    name: 'E-commerce Platform',
    description: 'Complete online store with payment processing, inventory management, and analytics',
    icon: <ShoppingCart className="h-6 w-6 text-red-600" />,
    category: 'E-commerce',
    estimatedRequirements: 20,
    complexity: 'Complex',
    industries: ['Retail', 'Fashion', 'Electronics', 'Food & Beverage']
  },
  {
    id: 'crm-system',
    name: 'CRM System',
    description: 'Customer relationship management with lead tracking, sales pipeline, and reporting',
    icon: <Users className="h-6 w-6 text-indigo-600" />,
    category: 'Business',
    estimatedRequirements: 16,
    complexity: 'Moderate',
    industries: ['Sales', 'Real Estate', 'Insurance', 'Consulting']
  },
  {
    id: 'analytics-dashboard',
    name: 'Analytics Dashboard',
    description: 'Business intelligence dashboard with real-time metrics and interactive visualizations',
    icon: <BarChart3 className="h-6 w-6 text-yellow-600" />,
    category: 'Analytics',
    estimatedRequirements: 14,
    complexity: 'Moderate',
    industries: ['Technology', 'Marketing', 'Finance', 'Operations']
  }
];

export const SmartTemplates: React.FC<SmartTemplatesProps> = ({
  projectId,
  onTemplateGenerated
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleTemplateGeneration = async () => {
    if (!selectedTemplate) {
      toast({
        title: "Template Required",
        description: "Please select a template to generate requirements",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Call the AI template generation API
      const templateResult = await apiClient.generateTemplate(
        selectedTemplate, 
        projectId, 
        selectedIndustry || undefined
      );

      // Convert template result to requirements format
      const requirements = templateResult.requirements.map((req: any) => ({
        title: req.title,
        description: req.description,
        type: req.type,
        priority: req.priority,
        status: 'draft',
        acceptance_criteria: req.acceptance_criteria,
        source: req.source
      }));

      onTemplateGenerated(requirements);

      toast({
        title: "Template Generated Successfully",
        description: `Generated ${requirements.length} requirements from ${getSelectedTemplate()?.name} template`,
      });

      // Reset selections
      setSelectedTemplate('');
      setSelectedIndustry('');
    } catch (error: any) {
      console.error('Template generation error:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Could not generate template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getSelectedTemplate = () => {
    return templateOptions.find(t => t.id === selectedTemplate);
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Simple': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Complex': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAvailableIndustries = () => {
    const template = getSelectedTemplate();
    return template ? template.industries : [];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-6 w-6 text-purple-600" />
            Smart Requirements Templates
          </CardTitle>
          <CardDescription>
            AI-generated requirements templates based on industry best practices and project patterns
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Template Selection Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templateOptions.map((template) => (
          <Card 
            key={template.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedTemplate === template.id 
                ? 'ring-2 ring-purple-500 bg-purple-50' 
                : 'hover:bg-gray-50'
            }`}
            onClick={() => setSelectedTemplate(template.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {template.icon}
                  <div>
                    <h3 className="font-semibold text-sm">{template.name}</h3>
                    <p className="text-xs text-gray-500">{template.category}</p>
                  </div>
                </div>
                {selectedTemplate === template.id && (
                  <CheckCircle2 className="h-5 w-5 text-purple-600" />
                )}
              </div>
              
              <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                {template.description}
              </p>
              
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {template.estimatedRequirements} reqs
                  </Badge>
                  <Badge className={getComplexityColor(template.complexity)}>
                    {template.complexity}
                  </Badge>
                </div>
              </div>
              
              <div className="mt-2 flex flex-wrap gap-1">
                {template.industries.slice(0, 2).map((industry) => (
                  <Badge key={industry} variant="secondary" className="text-xs">
                    {industry}
                  </Badge>
                ))}
                {template.industries.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{template.industries.length - 2}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Industry Selection */}
      {selectedTemplate && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Customize Template</CardTitle>
            <CardDescription>
              Fine-tune the template for your specific industry and requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Industry (Optional)</label>
              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger>
                  <SelectValue placeholder="Select your industry for tailored requirements" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableIndustries().map((industry) => (
                    <SelectItem key={industry} value={industry}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Template Preview */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                {getSelectedTemplate()?.icon}
                {getSelectedTemplate()?.name} Template
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {getSelectedTemplate()?.description}
              </p>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Sparkles className="h-4 w-4 text-purple-500" />
                  <span>{getSelectedTemplate()?.estimatedRequirements} Requirements</span>
                </div>
                <Badge className={getComplexityColor(getSelectedTemplate()?.complexity || 'Simple')}>
                  {getSelectedTemplate()?.complexity}
                </Badge>
                {selectedIndustry && (
                  <Badge variant="outline">
                    {selectedIndustry} Focus
                  </Badge>
                )}
              </div>
            </div>

            <Button 
              onClick={handleTemplateGeneration}
              disabled={isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating Requirements...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Generate {getSelectedTemplate()?.estimatedRequirements} Requirements
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
