import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle2, 
  AlertTriangle, 
  XCircle, 
  Lightbulb, 
  Target, 
  FileText,
  TrendingUp,
  Brain,
  Sparkles
} from 'lucide-react';

interface RequirementsQualityAnalyzerProps {
  requirements: any[];
  onSuggestionApplied?: (requirementId: string, suggestion: string) => void;
}

interface QualityMetric {
  name: string;
  score: number;
  maxScore: number;
  status: 'excellent' | 'good' | 'needs-improvement' | 'poor';
  description: string;
  suggestions: string[];
}

interface QualityAnalysis {
  overallScore: number;
  metrics: QualityMetric[];
  requirementAnalysis: Array<{
    id: string;
    title: string;
    score: number;
    issues: string[];
    suggestions: string[];
  }>;
}

export const RequirementsQualityAnalyzer: React.FC<RequirementsQualityAnalyzerProps> = ({
  requirements,
  onSuggestionApplied
}) => {
  const [analysis, setAnalysis] = useState<QualityAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    if (requirements.length > 0) {
      analyzeRequirements();
    }
  }, [requirements]);

  const analyzeRequirements = async () => {
    setIsAnalyzing(true);
    
    // Simulate AI analysis delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const analysisResult = performQualityAnalysis(requirements);
    setAnalysis(analysisResult);
    setIsAnalyzing(false);
  };

  const performQualityAnalysis = (reqs: any[]): QualityAnalysis => {
    const metrics: QualityMetric[] = [
      {
        name: 'Clarity & Completeness',
        score: calculateClarityScore(reqs),
        maxScore: 100,
        status: 'good',
        description: 'How clear and complete are your requirements?',
        suggestions: [
          'Add more specific acceptance criteria',
          'Define technical constraints clearly',
          'Include user personas and scenarios'
        ]
      },
      {
        name: 'Testability',
        score: calculateTestabilityScore(reqs),
        maxScore: 100,
        status: 'needs-improvement',
        description: 'How easily can these requirements be tested?',
        suggestions: [
          'Add measurable success criteria',
          'Define specific input/output expectations',
          'Include performance benchmarks'
        ]
      },
      {
        name: 'Consistency',
        score: calculateConsistencyScore(reqs),
        maxScore: 100,
        status: 'excellent',
        description: 'Are requirements consistent with each other?',
        suggestions: [
          'Review terminology usage',
          'Align priority levels',
          'Check for conflicting requirements'
        ]
      },
      {
        name: 'Traceability',
        score: calculateTraceabilityScore(reqs),
        maxScore: 100,
        status: 'needs-improvement',
        description: 'Can requirements be traced to business objectives?',
        suggestions: [
          'Link requirements to business goals',
          'Add requirement dependencies',
          'Create requirement hierarchy'
        ]
      }
    ];

    // Update status based on scores
    metrics.forEach(metric => {
      if (metric.score >= 90) metric.status = 'excellent';
      else if (metric.score >= 75) metric.status = 'good';
      else if (metric.score >= 60) metric.status = 'needs-improvement';
      else metric.status = 'poor';
    });

    const overallScore = Math.round(
      metrics.reduce((sum, metric) => sum + metric.score, 0) / metrics.length
    );

    const requirementAnalysis = reqs.map(req => ({
      id: req.id,
      title: req.title,
      score: Math.floor(Math.random() * 40) + 60, // Simulate individual scores
      issues: generateIssues(req),
      suggestions: generateSuggestions(req)
    }));

    return {
      overallScore,
      metrics,
      requirementAnalysis
    };
  };

  const calculateClarityScore = (reqs: any[]): number => {
    let score = 0;
    reqs.forEach(req => {
      if (req.title && req.title.length > 10) score += 20;
      if (req.description && req.description.length > 50) score += 20;
      if (req.acceptance_criteria && req.acceptance_criteria.length > 30) score += 15;
      if (req.type && req.priority) score += 10;
    });
    return Math.min(100, Math.round(score / reqs.length));
  };

  const calculateTestabilityScore = (reqs: any[]): number => {
    let score = 0;
    reqs.forEach(req => {
      if (req.acceptance_criteria) score += 30;
      if (req.type === 'functional') score += 20;
      if (req.description && req.description.includes('shall')) score += 15;
    });
    return Math.min(100, Math.round(score / reqs.length));
  };

  const calculateConsistencyScore = (reqs: any[]): number => {
    // Simulate consistency analysis
    const priorities = reqs.map(r => r.priority);
    const types = reqs.map(r => r.type);
    const uniquePriorities = new Set(priorities).size;
    const uniqueTypes = new Set(types).size;
    
    return Math.min(100, 85 + Math.random() * 15);
  };

  const calculateTraceabilityScore = (reqs: any[]): number => {
    let score = 0;
    reqs.forEach(req => {
      if (req.source) score += 25;
      if (req.rationale) score += 20;
      if (req.tasks && req.tasks.length > 0) score += 15;
    });
    return Math.min(100, Math.round(score / reqs.length));
  };

  const generateIssues = (req: any): string[] => {
    const issues = [];
    if (!req.acceptance_criteria) issues.push('Missing acceptance criteria');
    if (!req.description || req.description.length < 20) issues.push('Description too brief');
    if (req.priority === 'medium' && req.type === 'constraint') issues.push('Priority may be too low for constraint');
    return issues;
  };

  const generateSuggestions = (req: any): string[] => {
    const suggestions = [];
    if (!req.acceptance_criteria) {
      suggestions.push('Add specific, measurable acceptance criteria');
    }
    if (req.type === 'functional') {
      suggestions.push('Consider adding user story format: "As a... I want... So that..."');
    }
    if (!req.rationale) {
      suggestions.push('Add rationale explaining why this requirement is needed');
    }
    return suggestions;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case 'good': return <CheckCircle2 className="h-5 w-5 text-blue-600" />;
      case 'needs-improvement': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'poor': return <XCircle className="h-5 w-5 text-red-600" />;
      default: return <Target className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (requirements.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Brain className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold mb-2">No Requirements to Analyze</h3>
          <p className="text-gray-600">Add some requirements to see AI-powered quality analysis</p>
        </CardContent>
      </Card>
    );
  }

  if (isAnalyzing) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Brain className="h-12 w-12 mx-auto mb-4 text-purple-600 animate-pulse" />
          <h3 className="text-lg font-semibold mb-2">Analyzing Requirements Quality...</h3>
          <p className="text-gray-600">AI is evaluating clarity, testability, and consistency</p>
        </CardContent>
      </Card>
    );
  }

  if (!analysis) return null;

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            Requirements Quality Analysis
          </CardTitle>
          <CardDescription>
            AI-powered analysis of your requirements quality and suggestions for improvement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-3xl font-bold flex items-center gap-2">
                <span className={getScoreColor(analysis.overallScore)}>
                  {analysis.overallScore}%
                </span>
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <p className="text-gray-600">Overall Quality Score</p>
            </div>
            <div className="text-right">
              <Badge className={getStatusColor(
                analysis.overallScore >= 90 ? 'excellent' :
                analysis.overallScore >= 75 ? 'good' :
                analysis.overallScore >= 60 ? 'needs-improvement' : 'poor'
              )}>
                {analysis.overallScore >= 90 ? 'Excellent' :
                 analysis.overallScore >= 75 ? 'Good' :
                 analysis.overallScore >= 60 ? 'Needs Improvement' : 'Poor'}
              </Badge>
            </div>
          </div>
          <Progress value={analysis.overallScore} className="h-2" />
        </CardContent>
      </Card>

      {/* Quality Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Quality Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {analysis.metrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(metric.status)}
                  <span className="font-medium">{metric.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`font-semibold ${getScoreColor(metric.score)}`}>
                    {metric.score}%
                  </span>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.status.replace('-', ' ')}
                  </Badge>
                </div>
              </div>
              <Progress value={metric.score} className="h-1" />
              <p className="text-sm text-gray-600">{metric.description}</p>
              
              {metric.suggestions.length > 0 && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Lightbulb className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium">AI Suggestions</span>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {metric.suggestions.map((suggestion, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <span className="text-yellow-600">•</span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {index < analysis.metrics.length - 1 && <Separator />}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Individual Requirement Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            Individual Requirement Analysis
          </CardTitle>
          <CardDescription>
            Detailed analysis of each requirement with specific improvement suggestions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {analysis.requirementAnalysis.map((reqAnalysis) => (
            <Card key={reqAnalysis.id} className="border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{reqAnalysis.title}</h4>
                  <Badge className={getStatusColor(
                    reqAnalysis.score >= 80 ? 'excellent' :
                    reqAnalysis.score >= 70 ? 'good' :
                    reqAnalysis.score >= 60 ? 'needs-improvement' : 'poor'
                  )}>
                    {reqAnalysis.score}%
                  </Badge>
                </div>
                
                {reqAnalysis.issues.length > 0 && (
                  <div className="mb-3">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium text-red-600">Issues Found</span>
                    </div>
                    <ul className="text-sm text-red-600 space-y-1">
                      {reqAnalysis.issues.map((issue, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span>•</span>
                          {issue}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {reqAnalysis.suggestions.length > 0 && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Sparkles className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-600">AI Suggestions</span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                      {reqAnalysis.suggestions.map((suggestion, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span>•</span>
                          {suggestion}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};
