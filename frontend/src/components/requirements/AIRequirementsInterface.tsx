import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/lib/api-client';
import { FileUploadZone } from './FileUploadZone';
import { SmartTemplates } from './SmartTemplates';
import { 
  Brain, 
  MessageSquare, 
  Upload, 
  Sparkles, 
  FileText, 
  Plus,
  Wand2,
  Lightbulb,
  Target,
  CheckCircle2,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface AIRequirementsInterfaceProps {
  projectId: string;
  onRequirementsGenerated: (requirements: any[]) => void;
}

interface GeneratedRequirement {
  id: string;
  title: string;
  description: string;
  type: 'functional' | 'non-functional' | 'constraint' | 'assumption';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'approved';
  acceptance_criteria?: string;
  source: string;
  confidence: number;
  suggestions?: string[];
}

export const AIRequirementsInterface: React.FC<AIRequirementsInterfaceProps> = ({
  projectId,
  onRequirementsGenerated
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [conversationInput, setConversationInput] = useState('');
  const [generatedRequirements, setGeneratedRequirements] = useState<GeneratedRequirement[]>([]);
  const [selectedRequirements, setSelectedRequirements] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const handleFileAnalysis = async (files: any[]) => {
    const extractedRequirements: GeneratedRequirement[] = [];

    files.forEach((file, fileIndex) => {
      if (file.extractedData?.requirements) {
        file.extractedData.requirements.forEach((req: any, reqIndex: number) => {
          extractedRequirements.push({
            id: `file-${fileIndex}-req-${reqIndex}`,
            title: req.title,
            description: req.description,
            type: req.type as any,
            priority: req.priority as any,
            status: 'draft',
            acceptance_criteria: req.acceptance_criteria,
            source: `AI Analysis: ${file.file.name}`,
            confidence: 0.85 + Math.random() * 0.1, // Simulate confidence score
            suggestions: [
              "Consider adding more specific acceptance criteria",
              "Link to related user stories",
              "Define measurable success metrics"
            ]
          });
        });
      }
    });

    setGeneratedRequirements(prev => [...prev, ...extractedRequirements]);

    toast({
      title: "Requirements Extracted",
      description: `Generated ${extractedRequirements.length} requirements from uploaded files`,
    });
  };

  const handleTemplateGeneration = (templateRequirements: any[]) => {
    const generatedRequirements: GeneratedRequirement[] = templateRequirements.map((req, index) => ({
      id: `template-${Date.now()}-${index}`,
      title: req.title,
      description: req.description,
      type: req.type,
      priority: req.priority,
      status: 'draft',
      acceptance_criteria: req.acceptance_criteria,
      source: req.source,
      confidence: 0.95, // High confidence for templates
      suggestions: [
        "Review and customize for your specific needs",
        "Consider additional edge cases",
        "Validate with stakeholders"
      ]
    }));

    setGeneratedRequirements(prev => [...prev, ...generatedRequirements]);

    toast({
      title: "Template Generated",
      description: `Generated ${generatedRequirements.length} requirements from template`,
    });
  };

  const handleConversationalGeneration = async () => {
    if (!conversationInput.trim()) return;

    setIsGenerating(true);

    try {
      // Call real AI conversation analysis API
      const analysisResult = await apiClient.analyzeConversation(conversationInput, projectId);

      // Convert API response to GeneratedRequirement format
      const aiRequirements: GeneratedRequirement[] = analysisResult.requirements.map((req: any, index: number) => ({
        id: `conv-${Date.now()}-${index}`,
        title: req.title,
        description: req.description,
        type: req.type,
        priority: req.priority,
        status: 'draft' as const,
        acceptance_criteria: req.acceptance_criteria,
        source: "AI Conversation Analysis",
        confidence: analysisResult.confidence || 0.85,
        suggestions: analysisResult.suggestions || [
          "Consider adding more specific acceptance criteria",
          "Think about edge cases and error handling",
          "Define measurable success metrics"
        ]
      }));

      setGeneratedRequirements(prev => [...prev, ...aiRequirements]);
      setConversationInput('');

      toast({
        title: "Requirements Generated",
        description: `Created ${aiRequirements.length} requirements from your description`,
      });
    } catch (error: any) {
      console.error('Conversation analysis error:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Could not generate requirements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const toggleRequirementSelection = (id: string) => {
    const newSelection = new Set(selectedRequirements);
    if (newSelection.has(id)) {
      newSelection.delete(id);
    } else {
      newSelection.add(id);
    }
    setSelectedRequirements(newSelection);
  };

  const addSelectedRequirements = () => {
    const requirementsToAdd = generatedRequirements
      .filter(req => selectedRequirements.has(req.id))
      .map(req => ({
        title: req.title,
        description: req.description,
        type: req.type,
        priority: req.priority,
        status: req.status,
        acceptance_criteria: req.acceptance_criteria,
        source: req.source
      }));
    
    onRequirementsGenerated(requirementsToAdd);
    
    // Remove added requirements from generated list
    setGeneratedRequirements(prev => 
      prev.filter(req => !selectedRequirements.has(req.id))
    );
    setSelectedRequirements(new Set());
    
    toast({
      title: "Requirements Added",
      description: `Added ${requirementsToAdd.length} requirements to your project`,
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return "text-green-600 bg-green-100";
    if (confidence >= 0.7) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'high': return <Target className="h-4 w-4 text-orange-500" />;
      case 'medium': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'low': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Interface Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            AI-Powered Requirements Intelligence
          </CardTitle>
          <CardDescription>
            Transform documents and conversations into structured requirements automatically
          </CardDescription>
        </CardHeader>
      </Card>

      {/* AI Input Methods */}
      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload Documents
          </TabsTrigger>
          <TabsTrigger value="conversation" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Describe Needs
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Smart Templates
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <FileUploadZone
            projectId={projectId}
            onFilesAnalyzed={handleFileAnalysis}
            maxFiles={5}
            maxSize={50}
          />
        </TabsContent>

        <TabsContent value="conversation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                Conversational Requirements Generation
              </CardTitle>
              <CardDescription>
                Describe your project needs in natural language, and AI will generate structured requirements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="conversation-input">Describe your project requirements:</Label>
                <Textarea
                  id="conversation-input"
                  placeholder="Example: I need a user management system where users can register, login, and manage their profiles. The system should be secure and handle user authentication..."
                  value={conversationInput}
                  onChange={(e) => setConversationInput(e.target.value)}
                  rows={4}
                />
              </div>
              <Button 
                onClick={handleConversationalGeneration}
                disabled={!conversationInput.trim() || isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Brain className="h-4 w-4 mr-2 animate-pulse" />
                    AI is analyzing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Requirements
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <SmartTemplates
            projectId={projectId}
            onTemplateGenerated={handleTemplateGeneration}
          />
        </TabsContent>
      </Tabs>

      {/* Generated Requirements Review */}
      {generatedRequirements.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-600" />
                AI-Generated Requirements ({generatedRequirements.length})
              </div>
              {selectedRequirements.size > 0 && (
                <Button onClick={addSelectedRequirements}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Selected ({selectedRequirements.size})
                </Button>
              )}
            </CardTitle>
            <CardDescription>
              Review and select requirements to add to your project
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {generatedRequirements.map((req) => (
              <Card 
                key={req.id} 
                className={`cursor-pointer transition-all ${
                  selectedRequirements.has(req.id) 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => toggleRequirementSelection(req.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold flex items-center gap-2">
                      {getPriorityIcon(req.priority)}
                      {req.title}
                    </h4>
                    <div className="flex items-center gap-2">
                      <Badge className={getConfidenceColor(req.confidence)}>
                        {Math.round(req.confidence * 100)}% confidence
                      </Badge>
                      <Badge variant="outline" className="capitalize">
                        {req.type.replace('-', ' ')}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{req.description}</p>
                  
                  {req.acceptance_criteria && (
                    <div className="text-xs text-gray-500 mb-2">
                      <strong>Acceptance Criteria:</strong> {req.acceptance_criteria}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Source: {req.source}</span>
                    {req.suggestions && (
                      <span className="flex items-center gap-1">
                        <Sparkles className="h-3 w-3" />
                        {req.suggestions.length} AI suggestions
                      </span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
