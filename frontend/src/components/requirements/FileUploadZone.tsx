import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/lib/api-client';
import { 
  Upload, 
  File, 
  FileText, 
  Image, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Brain,
  Sparkles,
  FileImage,
  FileSpreadsheet
} from 'lucide-react';

interface UploadedFile {
  id: string;
  file: File;
  preview?: string;
  status: 'uploading' | 'analyzing' | 'completed' | 'error';
  progress: number;
  extractedData?: {
    requirements: Array<{
      title: string;
      description: string;
      type: string;
      priority: string;
      acceptance_criteria?: string;
    }>;
    metadata: {
      documentType: string;
      pageCount?: number;
      wordCount?: number;
      language: string;
    };
  };
  error?: string;
}

interface FileUploadZoneProps {
  projectId: string;
  onFilesAnalyzed: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxSize?: number; // in MB
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  projectId,
  onFilesAnalyzed,
  maxFiles = 10,
  maxSize = 50
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { toast } = useToast();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
      status: 'uploading',
      progress: 0
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    setIsAnalyzing(true);

    // Process each file
    for (const uploadedFile of newFiles) {
      try {
        await processFile(uploadedFile);
      } catch (error) {
        console.error('Error processing file:', error);
        updateFileStatus(uploadedFile.id, 'error', 100, undefined, 'Failed to process file');
      }
    }

    setIsAnalyzing(false);
    
    // Notify parent component
    const completedFiles = uploadedFiles.filter(f => f.status === 'completed');
    if (completedFiles.length > 0) {
      onFilesAnalyzed(completedFiles);
    }
  }, [uploadedFiles, onFilesAnalyzed]);

  const processFile = async (uploadedFile: UploadedFile) => {
    try {
      // Start upload
      updateFileStatus(uploadedFile.id, 'uploading', 30);

      // Start AI analysis
      updateFileStatus(uploadedFile.id, 'analyzing', 60);

      // Call real AI analysis API
      const response = await apiClient.analyzeDocument(uploadedFile.file, projectId);

      if (response.success && response.data.analysis) {
        const analysisResult = {
          requirements: response.data.analysis.requirements,
          metadata: response.data.analysis.metadata
        };

        updateFileStatus(uploadedFile.id, 'completed', 100, analysisResult);

        toast({
          title: "File Analyzed Successfully",
          description: `Extracted ${analysisResult.requirements.length} requirements from ${uploadedFile.file.name}`,
        });
      } else {
        throw new Error('Invalid response from AI analysis service');
      }
    } catch (error: any) {
      console.error('File processing error:', error);
      updateFileStatus(uploadedFile.id, 'error', 100, undefined, error.message || 'AI analysis failed');
      toast({
        title: "Analysis Failed",
        description: `Could not analyze ${uploadedFile.file.name}: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const updateFileStatus = (
    id: string, 
    status: UploadedFile['status'], 
    progress: number, 
    extractedData?: UploadedFile['extractedData'],
    error?: string
  ) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === id 
        ? { ...file, status, progress, extractedData, error }
        : file
    ));
  };

  const simulateAIAnalysis = async (file: File): Promise<UploadedFile['extractedData']> => {
    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock AI analysis results based on file type
    const fileName = file.name.toLowerCase();
    
    if (fileName.includes('requirement') || fileName.includes('spec')) {
      return {
        requirements: [
          {
            title: "User Authentication System",
            description: "The system shall provide secure user authentication with multi-factor support",
            type: "functional",
            priority: "high",
            acceptance_criteria: "Users can login with email/password and optional 2FA"
          },
          {
            title: "System Performance Requirements",
            description: "The system shall respond to user requests within 200ms under normal load",
            type: "non-functional",
            priority: "medium",
            acceptance_criteria: "95% of requests complete within 200ms during peak usage"
          }
        ],
        metadata: {
          documentType: "Requirements Document",
          pageCount: 15,
          wordCount: 2500,
          language: "English"
        }
      };
    }
    
    return {
      requirements: [
        {
          title: "Extracted Requirement",
          description: `Requirement extracted from ${file.name}`,
          type: "functional",
          priority: "medium"
        }
      ],
      metadata: {
        documentType: "General Document",
        language: "English"
      }
    };
  };

  const removeFile = (id: string) => {
    setUploadedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  };

  const getFileIcon = (file: File) => {
    const type = file.type;
    if (type.startsWith('image/')) return <FileImage className="h-8 w-8 text-blue-500" />;
    if (type.includes('pdf')) return <FileText className="h-8 w-8 text-red-500" />;
    if (type.includes('word') || type.includes('document')) return <FileText className="h-8 w-8 text-blue-600" />;
    if (type.includes('sheet') || type.includes('excel')) return <FileSpreadsheet className="h-8 w-8 text-green-600" />;
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'analyzing':
        return <Brain className="h-4 w-4 animate-pulse text-purple-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxFiles,
    maxSize: maxSize * 1024 * 1024,
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(rejection => {
        const errors = rejection.errors.map(e => e.message).join(', ');
        toast({
          title: "File Rejected",
          description: `${rejection.file.name}: ${errors}`,
          variant: "destructive",
        });
      });
    }
  });

  return (
    <div className="space-y-6">
      {/* Upload Zone */}
      <Card className={`transition-all duration-200 ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-dashed border-gray-300'}`}>
        <CardContent className="p-8">
          <div {...getRootProps()} className="cursor-pointer">
            <input {...getInputProps()} />
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                {isDragActive ? (
                  <div className="p-4 bg-blue-100 rounded-full">
                    <Upload className="h-12 w-12 text-blue-600 animate-bounce" />
                  </div>
                ) : (
                  <div className="p-4 bg-gray-100 rounded-full">
                    <Brain className="h-12 w-12 text-purple-600" />
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  AI-Powered Requirements Extraction
                </h3>
                <p className="text-gray-600">
                  {isDragActive 
                    ? "Drop your files here to analyze..." 
                    : "Drag & drop documents or click to upload"
                  }
                </p>
                <p className="text-sm text-gray-500">
                  Supports PDF, Word, Excel, images, and text files (max {maxSize}MB each)
                </p>
              </div>
              
              <Button variant="outline" className="mt-4">
                <Upload className="h-4 w-4 mr-2" />
                Choose Files
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-semibold flex items-center gap-2">
            <File className="h-4 w-4" />
            Processing Files ({uploadedFiles.length})
          </h4>
          
          {uploadedFiles.map((uploadedFile) => (
            <Card key={uploadedFile.id} className="p-4">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {getFileIcon(uploadedFile.file)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium truncate">
                      {uploadedFile.file.name}
                    </p>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(uploadedFile.status)}
                      <Badge variant={
                        uploadedFile.status === 'completed' ? 'default' :
                        uploadedFile.status === 'error' ? 'destructive' : 'secondary'
                      }>
                        {uploadedFile.status === 'analyzing' ? 'AI Analyzing...' : uploadedFile.status}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(uploadedFile.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <Progress value={uploadedFile.progress} className="mb-2" />
                  
                  {uploadedFile.extractedData && (
                    <div className="text-xs text-green-600 flex items-center gap-1">
                      <Sparkles className="h-3 w-3" />
                      Extracted {uploadedFile.extractedData.requirements.length} requirements
                    </div>
                  )}
                  
                  {uploadedFile.error && (
                    <p className="text-xs text-red-600">{uploadedFile.error}</p>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
