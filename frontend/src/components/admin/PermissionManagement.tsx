import React, { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api-client';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Key, Shield, Database, Loader2 } from 'lucide-react';

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

interface GroupedPermissions {
  [resource: string]: Permission[];
}

export const PermissionManagement: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [groupedPermissions, setGroupedPermissions] = useState<GroupedPermissions>({});
  const [loading, setLoading] = useState(true);
  const [activeResource, setActiveResource] = useState<string>('all');

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getPermissions();

      if (response.success) {
        setPermissions(response.data.permissions);
        setGroupedPermissions(response.data.grouped);
        
        // Set first resource as active if available
        const resources = Object.keys(response.data.grouped);
        if (resources.length > 0 && activeResource === 'all') {
          setActiveResource(resources[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching permissions:', error);
      toast.error('Failed to load permissions');
    } finally {
      setLoading(false);
    }
  };

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'read':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'update':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'delete':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'manage':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getResourceIcon = (resource: string) => {
    switch (resource.toLowerCase()) {
      case 'user':
        return <Shield className="h-4 w-4" />;
      case 'project':
        return <Database className="h-4 w-4" />;
      case 'task':
        return <Key className="h-4 w-4" />;
      default:
        return <Key className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const resources = Object.keys(groupedPermissions);

  return (
    <div className="space-y-6">
      {/* Permission Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{permissions.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resources</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{resources.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Actions</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(permissions.map(p => p.action)).size}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Permissions by Resource */}
      <Card>
        <CardHeader>
          <CardTitle>System Permissions</CardTitle>
          <CardDescription>
            View all permissions organized by resource type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeResource} onValueChange={setActiveResource}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              {resources.slice(0, 3).map((resource) => (
                <TabsTrigger key={resource} value={resource} className="capitalize">
                  {resource}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="all" className="mt-6">
              <div className="space-y-6">
                {resources.map((resource) => (
                  <Card key={resource}>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2 capitalize">
                        {getResourceIcon(resource)}
                        <span>{resource} Permissions</span>
                        <Badge variant="outline">
                          {groupedPermissions[resource].length}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {groupedPermissions[resource].map((permission) => (
                          <div
                            key={permission.id}
                            className="p-3 border rounded-lg space-y-2"
                          >
                            <div className="flex items-center justify-between">
                              <Badge className={getActionColor(permission.action)}>
                                {permission.action}
                              </Badge>
                            </div>
                            <div>
                              <p className="font-medium text-sm">{permission.name}</p>
                              {permission.description && (
                                <p className="text-xs text-gray-500 mt-1">
                                  {permission.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {resources.map((resource) => (
              <TabsContent key={resource} value={resource} className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 capitalize">
                      {getResourceIcon(resource)}
                      <span>{resource} Permissions</span>
                      <Badge variant="outline">
                        {groupedPermissions[resource].length}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Permission</TableHead>
                          <TableHead>Action</TableHead>
                          <TableHead>Description</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {groupedPermissions[resource].map((permission) => (
                          <TableRow key={permission.id}>
                            <TableCell>
                              <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                {permission.name}
                              </code>
                            </TableCell>
                            <TableCell>
                              <Badge className={getActionColor(permission.action)}>
                                {permission.action}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-600">
                                {permission.description || 'No description available'}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Matrix</CardTitle>
          <CardDescription>
            Overview of all actions available for each resource
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="text-left p-2 border-b font-medium">Resource</th>
                  {Array.from(new Set(permissions.map(p => p.action))).map(action => (
                    <th key={action} className="text-center p-2 border-b font-medium capitalize">
                      {action}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {resources.map(resource => (
                  <tr key={resource}>
                    <td className="p-2 border-b font-medium capitalize">
                      <div className="flex items-center space-x-2">
                        {getResourceIcon(resource)}
                        <span>{resource}</span>
                      </div>
                    </td>
                    {Array.from(new Set(permissions.map(p => p.action))).map(action => {
                      const hasPermission = groupedPermissions[resource]?.some(p => p.action === action);
                      return (
                        <td key={action} className="text-center p-2 border-b">
                          {hasPermission ? (
                            <div className="w-4 h-4 bg-green-500 rounded-full mx-auto"></div>
                          ) : (
                            <div className="w-4 h-4 bg-gray-200 rounded-full mx-auto"></div>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
