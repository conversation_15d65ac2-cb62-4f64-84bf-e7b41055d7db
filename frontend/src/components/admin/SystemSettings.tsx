import React, { useState } from 'react';
import { apiClient } from '@/lib/api-client';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { 
  Settings, 
  Database, 
  Shield, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  Loader2
} from 'lucide-react';

export const SystemSettings: React.FC = () => {
  const [initializing, setInitializing] = useState(false);

  const handleInitializeRBAC = async () => {
    try {
      setInitializing(true);
      const response = await apiClient.initializeRBAC();
      
      if (response.success) {
        toast.success('RBAC system initialized successfully');
      } else {
        toast.error('Failed to initialize RBAC system');
      }
    } catch (error) {
      console.error('Error initializing RBAC:', error);
      toast.error('Failed to initialize RBAC system');
    } finally {
      setInitializing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Connected</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              PostgreSQL
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Authentication</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Active</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Google OAuth + JWT
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">RBAC System</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm">Initialized</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Roles & Permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* RBAC Management */}
      <Card>
        <CardHeader>
          <CardTitle>RBAC System Management</CardTitle>
          <CardDescription>
            Manage the Role-Based Access Control system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              The RBAC system manages user roles and permissions. Initializing will create or update default roles and permissions.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-1">
                <h4 className="font-medium">Initialize Default Roles & Permissions</h4>
                <p className="text-sm text-gray-600">
                  Creates or updates system roles (Guest, User, Project Manager, Admin, Super Admin) and their permissions
                </p>
              </div>
              <Button 
                onClick={handleInitializeRBAC}
                disabled={initializing}
                variant="outline"
              >
                {initializing ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Initializing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Initialize
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Default Roles Information */}
      <Card>
        <CardHeader>
          <CardTitle>Default System Roles</CardTitle>
          <CardDescription>
            Overview of the default roles and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4">
              <div className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge className="bg-gray-100 text-gray-800 border-gray-200">Guest</Badge>
                <div className="flex-1">
                  <p className="text-sm font-medium">Guest User</p>
                  <p className="text-xs text-gray-600">Read-only access to projects and tasks</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" className="text-xs">project:read</Badge>
                    <Badge variant="outline" className="text-xs">task:read</Badge>
                    <Badge variant="outline" className="text-xs">team_member:read</Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge className="bg-green-100 text-green-800 border-green-200">User</Badge>
                <div className="flex-1">
                  <p className="text-sm font-medium">Regular User</p>
                  <p className="text-xs text-gray-600">Basic project access with create and update permissions</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" className="text-xs">project:read</Badge>
                    <Badge variant="outline" className="text-xs">project:create</Badge>
                    <Badge variant="outline" className="text-xs">task:read</Badge>
                    <Badge variant="outline" className="text-xs">task:create</Badge>
                    <Badge variant="outline" className="text-xs">task:update</Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge className="bg-blue-100 text-blue-800 border-blue-200">Project Manager</Badge>
                <div className="flex-1">
                  <p className="text-sm font-medium">Project Manager</p>
                  <p className="text-xs text-gray-600">Full project and task management capabilities</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" className="text-xs">project:manage</Badge>
                    <Badge variant="outline" className="text-xs">task:manage</Badge>
                    <Badge variant="outline" className="text-xs">team_member:create</Badge>
                    <Badge variant="outline" className="text-xs">team_member:update</Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge className="bg-orange-100 text-orange-800 border-orange-200">Admin</Badge>
                <div className="flex-1">
                  <p className="text-sm font-medium">Administrator</p>
                  <p className="text-xs text-gray-600">Full system access including user management</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" className="text-xs">project:manage</Badge>
                    <Badge variant="outline" className="text-xs">task:manage</Badge>
                    <Badge variant="outline" className="text-xs">user:manage</Badge>
                    <Badge variant="outline" className="text-xs">team_member:manage</Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-3 border rounded-lg">
                <Badge className="bg-red-100 text-red-800 border-red-200">Super Admin</Badge>
                <div className="flex-1">
                  <p className="text-sm font-medium">Super Administrator</p>
                  <p className="text-xs text-gray-600">Complete system control including admin management</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" className="text-xs">admin:manage</Badge>
                    <Badge variant="outline" className="text-xs">All permissions</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
          <CardDescription>
            Current system configuration and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Authentication</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Provider:</span>
                  <span>Google OAuth 2.0</span>
                </div>
                <div className="flex justify-between">
                  <span>Token Type:</span>
                  <span>JWT (Access + Refresh)</span>
                </div>
                <div className="flex justify-between">
                  <span>Access Token TTL:</span>
                  <span>15 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span>Refresh Token TTL:</span>
                  <span>7 days</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Database</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Type:</span>
                  <span>PostgreSQL</span>
                </div>
                <div className="flex justify-between">
                  <span>ORM:</span>
                  <span>Prisma</span>
                </div>
                <div className="flex justify-between">
                  <span>Database:</span>
                  <span>orbit</span>
                </div>
                <div className="flex justify-between">
                  <span>Host:</span>
                  <span>localhost:5432</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
