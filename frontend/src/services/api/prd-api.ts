import { apiClient } from '@/lib/api-client';
import type {
  PRDProject,
  GatheringSession,
  CapturedContent,
  PRDDocument,
  AIAnalysisResult,
  FileUploadResult,
  AudioProcessingResult,
  URLAnalysisResult,
  ChatInteractionResult,
  PRDExportOptions,
  PRDExportResult,
  ExtractedRequirement
} from '@/types/prd.types';

/**
 * PRD API Client - Real API calls only, no mocks
 */
export class PRDApiClient {
  
  // ==================== PROJECT MANAGEMENT ====================
  
  /**
   * Get all PRD projects for the current user
   */
  async getPRDProjects(): Promise<PRDProject[]> {
    return apiClient.request('/prd/projects');
  }

  /**
   * Get a specific PRD project
   */
  async getPRDProject(projectId: string): Promise<PRDProject> {
    return apiClient.request(`/prd/projects/${projectId}`);
  }

  /**
   * Create a new PRD project
   */
  async createPRDProject(data: Partial<PRDProject>): Promise<PRDProject> {
    return apiClient.request('/prd/projects', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * Update PRD project
   */
  async updatePRDProject(projectId: string, data: Partial<PRDProject>): Promise<PRDProject> {
    return apiClient.request(`/prd/projects/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // ==================== REQUIREMENTS GATHERING ====================

  /**
   * Create a new gathering session
   */
  async createGatheringSession(projectId: string, name: string): Promise<GatheringSession> {
    return apiClient.request('/prd/gathering/sessions', {
      method: 'POST',
      body: JSON.stringify({ project_id: projectId, name })
    });
  }

  /**
   * Get gathering sessions for a project
   */
  async getGatheringSessions(projectId: string): Promise<GatheringSession[]> {
    return apiClient.request(`/prd/gathering/sessions?project_id=${projectId}`);
  }

  /**
   * Get captured content for a session
   */
  async getCapturedContent(sessionId: string): Promise<CapturedContent[]> {
    return apiClient.request(`/prd/gathering/content?session_id=${sessionId}`);
  }

  // ==================== FILE PROCESSING ====================

  /**
   * Upload and analyze files
   */
  async uploadAndAnalyzeFiles(
    sessionId: string, 
    files: File[]
  ): Promise<FileUploadResult[]> {
    const formData = new FormData();
    formData.append('session_id', sessionId);
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    return apiClient.request('/prd/content/files/analyze', {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    });
  }

  /**
   * Get file analysis status
   */
  async getFileAnalysisStatus(fileId: string): Promise<{ status: string; progress: number; result?: AIAnalysisResult }> {
    return apiClient.request(`/prd/content/files/${fileId}/status`);
  }

  // ==================== AUDIO PROCESSING ====================

  /**
   * Upload and process audio
   */
  async uploadAndProcessAudio(
    sessionId: string,
    audioBlob: Blob,
    metadata?: any
  ): Promise<AudioProcessingResult> {
    const formData = new FormData();
    formData.append('session_id', sessionId);
    formData.append('audio', audioBlob, 'recording.webm');
    
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    return apiClient.request('/prd/content/audio/process', {
      method: 'POST',
      body: formData,
      headers: {}
    });
  }

  /**
   * Get audio processing status
   */
  async getAudioProcessingStatus(audioId: string): Promise<{ status: string; progress: number; result?: AudioProcessingResult }> {
    return apiClient.request(`/prd/content/audio/${audioId}/status`);
  }

  // ==================== URL ANALYSIS ====================

  /**
   * Analyze URL content
   */
  async analyzeURL(sessionId: string, url: string): Promise<URLAnalysisResult> {
    return apiClient.request('/prd/content/url/analyze', {
      method: 'POST',
      body: JSON.stringify({ session_id: sessionId, url })
    });
  }

  /**
   * Batch analyze multiple URLs
   */
  async analyzeURLs(sessionId: string, urls: string[]): Promise<URLAnalysisResult[]> {
    return apiClient.request('/prd/content/url/batch-analyze', {
      method: 'POST',
      body: JSON.stringify({ session_id: sessionId, urls })
    });
  }

  // ==================== AI CHAT INTERFACE ====================

  /**
   * Start AI chat session
   */
  async startChatSession(sessionId: string, mode: string = 'structured_interview'): Promise<{ chat_id: string; initial_message: string }> {
    return apiClient.request('/prd/content/chat/start', {
      method: 'POST',
      body: JSON.stringify({ session_id: sessionId, mode })
    });
  }

  /**
   * Send message to AI chat
   */
  async sendChatMessage(chatId: string, message: string): Promise<{ response: string; extracted_requirements?: ExtractedRequirement[] }> {
    return apiClient.request('/prd/content/chat/message', {
      method: 'POST',
      body: JSON.stringify({ chat_id: chatId, message })
    });
  }

  /**
   * Get chat interaction results
   */
  async getChatResults(chatId: string): Promise<ChatInteractionResult> {
    return apiClient.request(`/prd/content/chat/${chatId}/results`);
  }

  // ==================== TEXT PROCESSING ====================

  /**
   * Process text input with AI analysis
   */
  async processTextInput(
    sessionId: string,
    text: string,
    context?: string
  ): Promise<AIAnalysisResult> {
    return apiClient.request('/prd/content/text/analyze', {
      method: 'POST',
      body: JSON.stringify({ session_id: sessionId, text, context })
    });
  }

  // ==================== PRD DOCUMENT MANAGEMENT ====================

  /**
   * Generate PRD document from gathered content
   */
  async generatePRDDocument(projectId: string): Promise<PRDDocument> {
    return apiClient.request('/prd/document/generate', {
      method: 'POST',
      body: JSON.stringify({ project_id: projectId })
    });
  }

  /**
   * Get PRD document
   */
  async getPRDDocument(documentId: string): Promise<PRDDocument> {
    return apiClient.request(`/prd/document/${documentId}`);
  }

  /**
   * Update PRD document section
   */
  async updatePRDSection(
    documentId: string,
    sectionId: string,
    content: any
  ): Promise<{ success: boolean }> {
    return apiClient.request(`/prd/document/${documentId}/sections/${sectionId}`, {
      method: 'PUT',
      body: JSON.stringify({ content })
    });
  }

  /**
   * Get PRD document versions
   */
  async getPRDVersions(projectId: string): Promise<PRDDocument[]> {
    return apiClient.request(`/prd/document/versions?project_id=${projectId}`);
  }

  // ==================== AI ANALYSIS & SUGGESTIONS ====================

  /**
   * Get AI quality analysis for PRD
   */
  async getPRDQualityAnalysis(documentId: string): Promise<{
    overall_score: number;
    section_scores: { [key: string]: number };
    suggestions: string[];
    missing_sections: string[];
    conflicts: string[];
  }> {
    return apiClient.request(`/api/prd/document/${documentId}/quality-analysis`);
  }

  /**
   * Get AI suggestions for improving PRD section
   */
  async getSectionSuggestions(
    documentId: string, 
    sectionId: string
  ): Promise<{ suggestions: string[]; improvements: string[] }> {
    return apiClient.request(`/api/prd/document/${documentId}/sections/${sectionId}/suggestions`);
  }

  /**
   * Auto-categorize captured content into PRD sections
   */
  async categorizeCapturedContent(sessionId: string): Promise<{
    categorization: { [sectionType: string]: ExtractedRequirement[] };
    confidence_scores: { [sectionType: string]: number };
    suggestions: string[];
  }> {
    return apiClient.request(`/api/prd/gathering/sessions/${sessionId}/categorize`);
  }

  // ==================== TEMPLATES ====================

  /**
   * Get available PRD templates
   */
  async getPRDTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    industry: string;
    project_type: string;
    sections: string[];
  }>> {
    return apiClient.request('/api/prd/templates');
  }

  /**
   * Apply template to PRD document
   */
  async applyTemplate(documentId: string, templateId: string): Promise<PRDDocument> {
    return apiClient.request(`/api/prd/document/${documentId}/apply-template`, {
      method: 'POST',
      body: JSON.stringify({ template_id: templateId })
    });
  }

  // ==================== EXPORT & SHARING ====================

  /**
   * Export PRD document
   */
  async exportPRDDocument(
    documentId: string, 
    options: PRDExportOptions
  ): Promise<PRDExportResult> {
    return apiClient.request(`/api/prd/document/${documentId}/export`, {
      method: 'POST',
      body: JSON.stringify(options)
    });
  }

  /**
   * Share PRD document
   */
  async sharePRDDocument(
    documentId: string, 
    shareWith: string[], 
    permissions: string[]
  ): Promise<{ share_url: string; expires_at: string }> {
    return apiClient.request(`/api/prd/document/${documentId}/share`, {
      method: 'POST',
      body: JSON.stringify({ share_with: shareWith, permissions })
    });
  }

  // ==================== COLLABORATION ====================

  /**
   * Get document collaborators
   */
  async getDocumentCollaborators(documentId: string): Promise<Array<{
    user_id: string;
    name: string;
    role: string;
    permissions: string[];
    last_active: string;
  }>> {
    return apiClient.request(`/api/prd/document/${documentId}/collaborators`);
  }

  /**
   * Add collaborator to document
   */
  async addCollaborator(
    documentId: string, 
    userId: string, 
    role: string, 
    permissions: string[]
  ): Promise<{ success: boolean }> {
    return apiClient.request(`/api/prd/document/${documentId}/collaborators`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId, role, permissions })
    });
  }

  // ==================== REAL-TIME UPDATES ====================

  /**
   * Subscribe to real-time updates for a document
   */
  subscribeToDocumentUpdates(documentId: string, callback: (update: any) => void): () => void {
    // WebSocket connection for real-time updates
    const ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/prd/document/${documentId}/updates`);
    
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      callback(update);
    };

    // Return cleanup function
    return () => {
      ws.close();
    };
  }
}

// Export singleton instance
export const prdApiClient = new PRDApiClient();
