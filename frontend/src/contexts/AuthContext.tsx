import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient } from '@/lib/api-client';

// Types
interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  roles: string[];
  permissions: string[];
  is_active: boolean;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credential: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshToken: () => Promise<void>;
}

// Google OAuth types
interface GoogleCredentialResponse {
  credential: string;
  select_by: string;
}

declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, config: any) => void;
          prompt: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize Google OAuth
  useEffect(() => {
    const initializeGoogleAuth = () => {
      if (window.google) {
        console.log('Initializing Google OAuth with client ID:', import.meta.env.VITE_GOOGLE_CLIENT_ID);
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleGoogleLogin,
          auto_select: false,
          cancel_on_tap_outside: false,
          use_fedcm_for_prompt: false,
          scope: 'openid email profile',
          include_granted_scopes: true,
        });
        console.log('Google OAuth initialized successfully');
      }
    };

    // Wait for Google script to load
    if (window.google) {
      initializeGoogleAuth();
    } else {
      console.log('Waiting for Google script to load...');
      const checkGoogle = setInterval(() => {
        if (window.google) {
          console.log('Google script loaded, initializing...');
          initializeGoogleAuth();
          clearInterval(checkGoogle);
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkGoogle);
        console.log('Google script load timeout');
      }, 10000);
    }
  }, []);

  // Check for existing token on app load
  useEffect(() => {
    const checkAuthStatus = async () => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        try {
          const response = await apiClient.get('/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (response.data.success) {
            setUser(response.data.data.user);
          } else {
            localStorage.removeItem('auth_token');
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          localStorage.removeItem('auth_token');
        }
      }
      setIsLoading(false);
    };

    checkAuthStatus();
  }, []);

  const handleGoogleLogin = async (response: GoogleCredentialResponse) => {
    try {
      console.log('Google login response received:', response);
      setIsLoading(true);

      // Decode the JWT credential to get user info
      const payload = JSON.parse(atob(response.credential.split('.')[1]));
      console.log('Decoded Google payload:', payload);

      const authResponse = await apiClient.post('/auth/google', {
        token: response.credential,
        profile: {
          id: payload.sub,
          email: payload.email,
          name: payload.name,
          picture: payload.picture,
          verified_email: payload.email_verified
        }
      });

      console.log('Auth response:', authResponse);

      if (authResponse.data.success) {
        const { token, user: userData } = authResponse.data.data;
        localStorage.setItem('auth_token', token);
        setUser(userData);
        console.log('User authenticated successfully:', userData);

        // Show success message
        if (window.location.pathname === '/login') {
          window.location.href = '/';
        }
      } else {
        console.error('Authentication failed:', authResponse.data.error);
        throw new Error(authResponse.data.error || 'Authentication failed');
      }
    } catch (error) {
      console.error('Google login failed:', error);
      // Don't throw error to prevent unhandled promise rejection
      // Instead, show user-friendly error message
      alert('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credential: string) => {
    await handleGoogleLogin({ credential, select_by: 'btn' });
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
    
    // Sign out from Google
    if (window.google) {
      window.google.accounts.id.disableAutoSelect();
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.roles.includes(role);
  };

  const refreshToken = async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) return;

    try {
      const response = await apiClient.post('/auth/refresh', {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        localStorage.setItem('auth_token', response.data.data.token);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
