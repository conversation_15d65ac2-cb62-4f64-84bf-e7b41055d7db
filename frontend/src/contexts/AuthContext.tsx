import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient } from '@/lib/api-client';

// Types
interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  roles: string[];
  permissions: string[];
  is_active: boolean;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credential: string) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshToken: () => Promise<void>;
}

// Google OAuth types
interface GoogleCredentialResponse {
  credential: string;
  select_by: string;
}

declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: GoogleInitConfig) => void;
          renderButton: (element: HTMLElement, config: GoogleButtonConfig) => void;
          prompt: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

interface GoogleInitConfig {
  client_id: string;
  callback: (response: GoogleCredentialResponse) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleButtonConfig {
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  size?: 'large' | 'medium' | 'small';
  width?: number;
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  logo_alignment?: 'left' | 'center';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize Google OAuth
  useEffect(() => {
    const initializeGoogleAuth = () => {
      if (!window.google?.accounts?.id) {
        console.warn('Google Identity Services not available');
        return;
      }

      const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
      if (!clientId) {
        console.error('VITE_GOOGLE_CLIENT_ID not configured');
        return;
      }

      console.log('Initializing Google OAuth with client ID:', clientId);
      try {
        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: handleGoogleLogin,
          auto_select: false,
          cancel_on_tap_outside: false,
        });
        console.log('Google OAuth initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Google OAuth:', error);
      }
    };

    // Wait for Google script to load
    if (window.google?.accounts?.id) {
      initializeGoogleAuth();
    } else {
      console.log('Waiting for Google Identity Services to load...');
      const checkGoogle = setInterval(() => {
        if (window.google?.accounts?.id) {
          console.log('Google Identity Services loaded');
          initializeGoogleAuth();
          clearInterval(checkGoogle);
        }
      }, 100);

      // Cleanup after 10 seconds
      setTimeout(() => {
        clearInterval(checkGoogle);
        console.warn('Google Identity Services failed to load within 10 seconds');
      }, 10000);
    }
  }, []);

  // Check for existing token on app load
  useEffect(() => {
    const checkAuthStatus = async () => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        try {
          const response = await apiClient.get('/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (response.data.success) {
            setUser(response.data.data.user);
          } else {
            localStorage.removeItem('auth_token');
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          localStorage.removeItem('auth_token');
        }
      }
      setIsLoading(false);
    };

    checkAuthStatus();
  }, []);

  const handleGoogleLogin = async (response: GoogleCredentialResponse) => {
    try {
      console.log('Google login callback triggered with response:', response);
      setIsLoading(true);

      if (!response.credential) {
        throw new Error('No credential received from Google');
      }

      // Send the credential token to backend for verification
      console.log('Sending credential to backend...');
      const authResponse = await apiClient.post('/auth/google', {
        token: response.credential
      });

      console.log('Backend auth response:', authResponse);

      if (authResponse.success) {
        const { token, user: userData } = authResponse.data;
        localStorage.setItem('auth_token', token);
        setUser(userData);
        console.log('User authenticated successfully:', userData);

        // Show success message and redirect
        if (window.location.pathname === '/login') {
          window.location.href = '/';
        } else {
          // Refresh the current page to update auth state
          window.location.reload();
        }
      } else {
        console.error('Authentication failed:', authResponse.error);
        throw new Error(authResponse.error || 'Authentication failed');
      }
    } catch (error) {
      console.error('Google login failed:', error);
      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Login failed. Please try again.';
      alert(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credential: string) => {
    await handleGoogleLogin({ credential, select_by: 'btn' });
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
    
    // Sign out from Google
    if (window.google) {
      window.google.accounts.id.disableAutoSelect();
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.roles.includes(role);
  };

  const refreshToken = async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) return;

    try {
      const response = await apiClient.post('/auth/refresh', {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        localStorage.setItem('auth_token', response.data.data.token);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
