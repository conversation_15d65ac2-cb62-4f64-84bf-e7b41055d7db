import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient } from '@/lib/api-client';

// Types
interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  roles: string[];
  permissions: string[];
  is_active: boolean;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshToken: () => Promise<void>;
}

// Google OAuth types
interface GoogleCredentialResponse {
  credential: string;
  select_by: string;
}

declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: GoogleInitConfig) => void;
          renderButton: (element: HTMLElement, config: GoogleButtonConfig) => void;
          prompt: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

interface GoogleInitConfig {
  client_id: string;
  callback: (response: GoogleCredentialResponse) => void;
  auto_select?: boolean;
  cancel_on_tap_outside?: boolean;
}

interface GoogleButtonConfig {
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  size?: 'large' | 'medium' | 'small';
  width?: number;
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  logo_alignment?: 'left' | 'center';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Get stored tokens
  const getAccessToken = () => localStorage.getItem('auth_token');
  const getRefreshToken = () => localStorage.getItem('refresh_token');
  const setTokens = (accessToken: string, refreshToken: string) => {
    localStorage.setItem('auth_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  };
  const clearTokens = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  };

  // Legacy methods for backward compatibility
  const setToken = (token: string) => {
    localStorage.setItem('auth_token', token);
  };
  const clearToken = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  };

  // API call helper with token
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const token = getAccessToken();
    const response = await apiClient.get(endpoint, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    });
    return response;
  };

  // Get current user
  const getCurrentUser = async () => {
    try {
      const response = await apiCall('/auth/me');
      if (response.success) {
        setUser(response.data.user);
      } else {
        throw new Error('Failed to get user');
      }
    } catch (error) {
      console.error('Get current user failed:', error);
      clearToken();
      setUser(null);
    }
  };

  // Handle OAuth callback
  useEffect(() => {
    const handleOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');
      const refreshTokenParam = urlParams.get('refresh');

      if (token && refreshTokenParam) {
        setTokens(token, refreshTokenParam);
        // Clear URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        // Get user data
        getCurrentUser();
      } else if (token) {
        // Fallback for legacy single token
        setToken(token);
        // Clear URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        // Get user data
        getCurrentUser();
      }
    };

    // Check if we're on the OAuth callback route or have token in URL
    if (window.location.pathname === '/auth/callback' || window.location.search.includes('token=')) {
      handleOAuthCallback();
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      const token = getAccessToken();
      if (token) {
        try {
          await getCurrentUser();
        } catch (error) {
          console.error('Auth initialization failed:', error);
        }
      }

      setIsLoading(false);
    };

    // Don't initialize if we're handling OAuth callback
    if (window.location.pathname !== '/auth/callback' && !window.location.search.includes('token=')) {
      initializeAuth();
    } else {
      setIsLoading(false);
    }
  }, []);

  // Login with Google OAuth - redirect to backend
  const login = () => {
    const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    window.location.href = `${API_BASE_URL}/auth/google`;
  };

  // Logout
  const logout = async () => {
    try {
      if (getAccessToken()) {
        await apiCall('/auth/logout');
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      clearTokens();
      setUser(null);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    return user.roles.includes(role);
  };

  const refreshToken = async () => {
    const refreshTokenValue = getRefreshToken();
    if (!refreshTokenValue) {
      logout();
      return;
    }

    try {
      const response = await apiClient.post('/auth/refresh', {
        refreshToken: refreshTokenValue
      });

      if (response.data.success) {
        setTokens(response.data.data.accessToken, response.data.data.refreshToken);
      } else {
        logout();
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
