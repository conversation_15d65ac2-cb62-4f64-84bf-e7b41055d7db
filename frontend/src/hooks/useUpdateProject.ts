import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Tables } from "@/integrations/supabase/types";
import { FEATURE_FLAGS } from "@/lib/database-config";

// Import Prisma hook
import { useUpdateProject as usePrismaUpdateProject } from "@/hooks/prisma/useProjectMutations";

type ProjectUpdate = Partial<Tables<'projects'>>;

export function useUpdateProject() {
  if (FEATURE_FLAGS.USE_PRISMA_PROJECTS) {
    return usePrismaUpdateProject();
  }

  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: ProjectUpdate }) => {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.id] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });

      toast({
        title: "Success",
        description: "Project updated successfully.",
      });
    },
    onError: (error) => {
      console.error('Error updating project:', error);
      toast({
        title: "Error",
        description: "Failed to update project. Please try again.",
        variant: "destructive",
      });
    },
  });
}