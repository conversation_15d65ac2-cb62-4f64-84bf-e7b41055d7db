import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useConvertProjectToTask = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      sourceProjectId, 
      targetProjectId 
    }: { 
      sourceProjectId: string; 
      targetProjectId: string; 
    }) => {
      const { data, error } = await supabase.rpc('convert_project_to_task', {
        source_project_id: sourceProjectId,
        target_project_id: targetProjectId
      });

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (newTaskId) => {
      // Invalidate all project-related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      
      toast({
        title: "Project converted successfully",
        description: "The project has been converted to a task with all its existing tasks as subtasks.",
      });

      return newTaskId;
    },
    onError: (error: any) => {
      toast({
        title: "Failed to convert project",
        description: error.message || "An error occurred while converting the project to a task.",
        variant: "destructive",
      });
    },
  });
};