import { useUrlState } from './useUrlState';
import { useSearchParams } from 'react-router-dom';

export type GroupByOption = "none" | "start_date" | "end_date" | "status" | "lead";
export type SortField = "name" | "project_lead" | "customer_name" | "start_date" | "end_date" | "status" | "priority_level";
export type SortDirection = "asc" | "desc";
export type ViewMode = "grid" | "list";

export function useProjectFilters() {
  const [, setSearchParams] = useSearchParams();
  // Filter states
  const [searchTerm, setSearchTerm] = useUrlState('search', '');
  const [typeFilter, setTypeFilter] = useUrlState('type', 'all');
  const [statusFilter, setStatusFilter] = useUrlState('status', 'all');
  const [leadFilter, setLeadFilter] = useUrlState('lead', 'all');
  const [priorityFilter, setPriorityFilter] = useUrlState('priority', 'all');
  
  // Grouping and view states
  const [groupBy, setGroupBy] = useUrlState<GroupByOption>('group', 'none');
  const [showArchivedProjects, setShowArchivedProjects] = useUrlState(
    'archived',
    false,
    (value) => value ? 'true' : 'false',
    (value) => value === 'true'
  );
  const [viewMode, setViewMode] = useUrlState<ViewMode>('view', 'list');
  
  // Sorting states
  const [sortBy, setSortBy] = useUrlState<SortField>('sort', 'priority_level');
  const [sortDirection, setSortDirection] = useUrlState<SortDirection>('dir', 'asc');

  const clearFilters = () => {
    // Clear all URL parameters in a single operation to avoid race conditions
    setSearchParams({}, { replace: true });
    
    // Also update local state to defaults
    setSearchTerm('');
    setTypeFilter('all');
    setStatusFilter('all');
    setLeadFilter('all');
    setPriorityFilter('all');
    setGroupBy('none');
    setShowArchivedProjects(false);
    setSortBy('priority_level');
    setSortDirection('asc');
  };

  return {
    // Filter values
    searchTerm,
    typeFilter,
    statusFilter,
    leadFilter,
    priorityFilter,
    groupBy,
    showArchivedProjects,
    viewMode,
    sortBy,
    sortDirection,
    
    // Filter setters
    setSearchTerm,
    setTypeFilter,
    setStatusFilter,
    setLeadFilter,
    setPriorityFilter,
    setGroupBy,
    setShowArchivedProjects,
    setViewMode,
    setSortBy,
    setSortDirection,
    
    // Utility functions
    clearFilters,
  };
}