import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';

export interface ImpactType {
  id: string;
  name: string;
  label: string;
  description: string;
  color: string;
  bg_color: string;
  border_color: string;
  is_active: boolean;
  sort_order: number;
}

export function useImpactTypes() {
  return useQuery({
    queryKey: ['impact-types'],
    queryFn: async (): Promise<ImpactType[]> => {
      const impactTypes = await apiClient.getImpactTypes();
      return impactTypes as ImpactType[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
