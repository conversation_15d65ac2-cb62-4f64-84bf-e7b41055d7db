// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});