@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Monochrome Color Palette */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 0 0% 80%;
    --chart-2: 0 0% 70%;
    --chart-3: 0 0% 60%;
    --chart-4: 0 0% 50%;
    --chart-5: 0 0% 40%;
    --radius: 0.5rem;
    
    /* Project Status Colors */
    --status-not-started: 220 9% 46%;
    --status-in-progress: 217 91% 60%;
    --status-completed: 142 76% 36%;
    --status-archived: 0 0% 60%;
    
    /* Task Status Colors */
    --task-todo: 0 0% 70%;
    --task-in-progress: 217 91% 60%;
    --task-done: 142 76% 36%;
    
    /* Semantic Colors */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --info: 217 91% 60%;
    --info-foreground: 0 0% 98%;
    
    /* Priority system colors */
    --orange: 25 95% 53%;
    --orange-foreground: 0 0% 98%;
    --yellow: 47 96% 53%;
    --yellow-foreground: 0 0% 9%;
    --blue: 221 83% 53%;
    --blue-foreground: 0 0% 98%;
    --green: 142 71% 45%;
    --green-foreground: 0 0% 98%;
    --purple: 262 83% 58%;
    --purple-foreground: 0 0% 98%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 0 0% 20%;
    --chart-2: 0 0% 30%;
    --chart-3: 0 0% 40%;
    --chart-4: 0 0% 50%;
    --chart-5: 0 0% 60%;
    
    /* Project Status Colors (Dark) */
    --status-not-started: 220 14% 96%;
    --status-in-progress: 217 91% 60%;
    --status-completed: 142 76% 36%;
    
    /* Task Status Colors (Dark) */
    --task-todo: 0 0% 40%;
    --task-in-progress: 217 91% 60%;
    --task-done: 142 76% 36%;
    
    /* Priority system colors - dark mode */
    --orange: 25 95% 53%;
    --orange-foreground: 0 0% 98%;
    --yellow: 47 96% 53%;
    --yellow-foreground: 0 0% 9%;
    --blue: 221 83% 53%;
    --blue-foreground: 0 0% 98%;
    --green: 142 71% 45%;
    --green-foreground: 0 0% 98%;
    --purple: 262 83% 58%;
    --purple-foreground: 0 0% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Badge status colors */
  .status-backlog {
    @apply text-purple-600 bg-purple-50 dark:text-purple-400 dark:bg-purple-950;
  }
  
  .status-not-started {
    @apply text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-800;
  }
  
  .status-in-progress {
    @apply text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950;
  }
  
  .status-completed {
    @apply text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950;
  }
  
  .status-archived {
    @apply text-gray-500 bg-gray-100 dark:text-gray-500 dark:bg-gray-900;
  }
  
  /* Full card background colors */
  .card-bg-backlog {
    @apply bg-red-100 border-red-200 dark:bg-red-900/30 dark:border-red-800;
  }
  
  .card-bg-not-started {
    @apply bg-gray-100 border-gray-200 dark:bg-gray-900/30 dark:border-gray-800;
  }
  
  .card-bg-in-progress {
    @apply bg-orange-100 border-orange-200 dark:bg-orange-900/30 dark:border-orange-800;
  }
  
  .card-bg-completed {
    @apply bg-green-100 border-green-200 dark:bg-green-900/30 dark:border-green-800;
  }
  
  .card-bg-archived {
    @apply bg-gray-50 border-gray-300 dark:bg-gray-950/50 dark:border-gray-700 opacity-75;
  }

  /* Due Date Proximity Background Colors */
  .card-bg-no-date {
    @apply bg-gray-100 border-gray-200 dark:bg-gray-900/30 dark:border-gray-800;
  }

  .card-bg-overdue {
    @apply bg-red-100 border-red-200 dark:bg-red-900/30 dark:border-red-800;
  }

  .card-bg-urgent {
    @apply bg-orange-100 border-orange-200 dark:bg-orange-900/30 dark:border-orange-800;
  }

  .card-bg-upcoming {
    @apply bg-yellow-100 border-yellow-200 dark:bg-yellow-900/30 dark:border-yellow-800;
  }

  .card-bg-future {
    @apply bg-green-100 border-green-200 dark:bg-green-900/30 dark:border-green-800;
  }

  /* Text colors for colored backgrounds */
  .card-text-backlog {
    @apply text-red-900 dark:text-red-100;
  }
  
  .card-text-not-started {
    @apply text-gray-900 dark:text-gray-100;
  }
  
  .card-text-in-progress {
    @apply text-orange-900 dark:text-orange-100;
  }
  
  .card-text-completed {
    @apply text-green-900 dark:text-green-100;
  }
  
  .card-text-archived {
    @apply text-gray-600 dark:text-gray-400;
  }

  /* Due Date Proximity Text Colors */
  .card-text-no-date {
    @apply text-gray-900 dark:text-gray-100;
  }

  .card-text-overdue {
    @apply text-red-900 dark:text-red-100;
  }

  .card-text-urgent {
    @apply text-orange-900 dark:text-orange-100;
  }

  .card-text-upcoming {
    @apply text-yellow-900 dark:text-yellow-100;
  }

  .card-text-future {
    @apply text-green-900 dark:text-green-100;
  }

  /* Muted text colors for colored backgrounds */
  .card-muted-backlog {
    @apply text-red-700 dark:text-red-300;
  }
  
  .card-muted-not-started {
    @apply text-gray-700 dark:text-gray-300;
  }
  
  .card-muted-in-progress {
    @apply text-orange-700 dark:text-orange-300;
  }
  
  .card-muted-completed {
    @apply text-green-700 dark:text-green-300;
  }
  
  .card-muted-archived {
    @apply text-gray-500 dark:text-gray-500;
  }

  /* Due Date Proximity Muted Text Colors */
  .card-muted-no-date {
    @apply text-gray-700 dark:text-gray-300;
  }

  .card-muted-overdue {
    @apply text-red-700 dark:text-red-300;
  }

  .card-muted-urgent {
    @apply text-orange-700 dark:text-orange-300;
  }

  .card-muted-upcoming {
    @apply text-yellow-700 dark:text-yellow-300;
  }

  .card-muted-future {
    @apply text-green-700 dark:text-green-300;
  }
  
  .task-todo {
    @apply text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-800;
  }
  
  .task-in-progress {
    @apply text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950;
  }
  
  .task-final-stages {
    @apply text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-950;
  }
  
  .task-done {
    @apply text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950;
  }
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}
