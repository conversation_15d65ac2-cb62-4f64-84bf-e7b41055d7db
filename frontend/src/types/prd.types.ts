// PRD (Product Requirements Document) Type Definitions

export interface PRDProject {
  id: string;
  name: string;
  description?: string;
  status: 'draft' | 'in-progress' | 'review' | 'approved' | 'archived';
  created_at: string;
  updated_at: string;
  created_by: string;
}

// Requirements Gathering Types
export interface GatheringSession {
  id: string;
  project_id: string;
  name: string;
  status: 'active' | 'completed' | 'paused';
  progress_percentage: number;
  created_at: string;
  updated_at: string;
}

export interface CapturedContent {
  id: string;
  session_id: string;
  content_type: 'file' | 'text' | 'audio' | 'url' | 'chat' | 'form' | 'image';
  source_name: string;
  raw_content: string;
  processed_content?: any;
  ai_analysis?: AIAnalysisResult;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
}

export interface AIAnalysisResult {
  confidence: number;
  extracted_requirements: ExtractedRequirement[];
  categorization: PRDCategorization;
  suggestions: string[];
  quality_score: number;
}

export interface ExtractedRequirement {
  title: string;
  description: string;
  type: RequirementType;
  priority: RequirementPriority;
  acceptance_criteria?: string;
  rationale?: string;
  source: string;
  confidence: number;
}

// PRD Document Types
export interface PRDDocument {
  id: string;
  project_id: string;
  version: number;
  status: 'draft' | 'review' | 'approved' | 'published';
  sections: PRDSection[];
  metadata: PRDMetadata;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface PRDSection {
  id: string;
  section_type: PRDSectionType;
  title: string;
  content: any; // Section-specific content structure
  status: 'empty' | 'partial' | 'complete' | 'needs-review';
  completeness_percentage: number;
  last_updated: string;
  updated_by: string;
}

export interface PRDMetadata {
  template_id?: string;
  industry?: string;
  project_type?: string;
  methodology?: string;
  stakeholders: string[];
  reviewers: string[];
  approvers: string[];
  export_formats: string[];
}

// PRD Section Types
export type PRDSectionType = 
  | 'project_overview'
  | 'functional_requirements'
  | 'technical_requirements'
  | 'non_functional_requirements'
  | 'integration_requirements'
  | 'security_rbac'
  | 'technology_stack'
  | 'timeline_milestones'
  | 'deployment_infrastructure'
  | 'existing_systems_impact'
  | 'training_documentation'
  | 'maintenance_support'
  | 'compliance_licensing'
  | 'risk_assessment'
  | 'success_metrics';

// Section Content Interfaces
export interface ProjectOverviewContent {
  vision: string;
  mission: string;
  business_goals: string[];
  project_scope: {
    included: string[];
    excluded: string[];
  };
  stakeholders: Stakeholder[];
  success_criteria: string[];
}

export interface FunctionalRequirementsContent {
  user_stories: UserStory[];
  features: Feature[];
  workflows: Workflow[];
  business_rules: BusinessRule[];
  data_requirements: DataRequirement[];
}

export interface TechnicalRequirementsContent {
  system_architecture: string;
  api_specifications: APISpec[];
  data_models: DataModel[];
  integration_points: IntegrationPoint[];
  technical_constraints: string[];
}

export interface NonFunctionalRequirementsContent {
  performance: PerformanceRequirement[];
  scalability: ScalabilityRequirement[];
  reliability: ReliabilityRequirement[];
  usability: UsabilityRequirement[];
  compatibility: CompatibilityRequirement[];
}

export interface SecurityRBACContent {
  authentication: AuthenticationRequirement[];
  authorization: AuthorizationRequirement[];
  data_protection: DataProtectionRequirement[];
  security_standards: string[];
  access_control: AccessControlRule[];
}

export interface TechnologyStackContent {
  programming_languages: TechChoice[];
  frameworks: TechChoice[];
  databases: TechChoice[];
  infrastructure: TechChoice[];
  tools: TechChoice[];
}

// Supporting Types
export interface Stakeholder {
  name: string;
  role: string;
  responsibility: string;
  contact: string;
}

export interface UserStory {
  id: string;
  title: string;
  description: string;
  persona: string;
  acceptance_criteria: string[];
  priority: RequirementPriority;
  effort_estimate?: string;
}

export interface Feature {
  id: string;
  name: string;
  description: string;
  requirements: string[];
  dependencies: string[];
  priority: RequirementPriority;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  actors: string[];
}

export interface WorkflowStep {
  step_number: number;
  description: string;
  actor: string;
  system_action?: string;
  validation?: string;
}

export interface BusinessRule {
  id: string;
  name: string;
  description: string;
  conditions: string[];
  actions: string[];
}

export interface DataRequirement {
  entity: string;
  description: string;
  attributes: DataAttribute[];
  relationships: string[];
}

export interface DataAttribute {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface APISpec {
  endpoint: string;
  method: string;
  description: string;
  parameters: APIParameter[];
  response: any;
}

export interface APIParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface DataModel {
  name: string;
  description: string;
  fields: DataModelField[];
  relationships: DataModelRelationship[];
}

export interface DataModelField {
  name: string;
  type: string;
  constraints: string[];
  description: string;
}

export interface DataModelRelationship {
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  target: string;
  description: string;
}

export interface IntegrationPoint {
  name: string;
  type: 'api' | 'database' | 'file' | 'message_queue';
  description: string;
  requirements: string[];
}

export interface PerformanceRequirement {
  metric: string;
  target: string;
  measurement: string;
  conditions: string;
}

export interface ScalabilityRequirement {
  dimension: string;
  current_capacity: string;
  target_capacity: string;
  scaling_strategy: string;
}

export interface ReliabilityRequirement {
  metric: string;
  target: string;
  measurement: string;
  recovery_strategy: string;
}

export interface UsabilityRequirement {
  aspect: string;
  requirement: string;
  measurement: string;
  target: string;
}

export interface CompatibilityRequirement {
  category: string;
  requirements: string[];
  testing_strategy: string;
}

export interface AuthenticationRequirement {
  method: string;
  description: string;
  security_level: string;
  implementation: string;
}

export interface AuthorizationRequirement {
  resource: string;
  permissions: string[];
  roles: string[];
  conditions: string[];
}

export interface DataProtectionRequirement {
  data_type: string;
  protection_method: string;
  compliance_standard: string;
  implementation: string;
}

export interface AccessControlRule {
  resource: string;
  role: string;
  permissions: string[];
  conditions: string[];
}

export interface TechChoice {
  name: string;
  version?: string;
  rationale: string;
  alternatives_considered: string[];
  decision_criteria: string[];
}

// Categorization Types
export interface PRDCategorization {
  [key: string]: {
    section: PRDSectionType;
    confidence: number;
    items: ExtractedRequirement[];
  };
}

// Enums
export type RequirementType = 
  | 'functional'
  | 'non-functional'
  | 'technical'
  | 'business'
  | 'constraint'
  | 'assumption';

export type RequirementPriority = 
  | 'critical'
  | 'high'
  | 'medium'
  | 'low';

// Input Processing Types
export interface FileUploadResult {
  file: File;
  analysis: AIAnalysisResult;
  extracted_data: any;
}

export interface AudioProcessingResult {
  transcription: string;
  speaker_identification?: SpeakerInfo[];
  key_points: string[];
  action_items: string[];
  analysis: AIAnalysisResult;
}

export interface SpeakerInfo {
  speaker_id: string;
  name?: string;
  segments: AudioSegment[];
}

export interface AudioSegment {
  start_time: number;
  end_time: number;
  text: string;
}

export interface URLAnalysisResult {
  url: string;
  title: string;
  content: string;
  metadata: any;
  extracted_requirements: ExtractedRequirement[];
  related_resources: string[];
}

export interface ChatInteractionResult {
  conversation_id: string;
  messages: ChatMessage[];
  extracted_requirements: ExtractedRequirement[];
  follow_up_questions: string[];
  completeness_assessment: CompletenessAssessment;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: any;
}

export interface CompletenessAssessment {
  overall_score: number;
  missing_sections: PRDSectionType[];
  recommendations: string[];
  next_questions: string[];
}

// Export Types
export interface PRDExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'markdown' | 'confluence' | 'notion';
  sections: PRDSectionType[];
  include_metadata: boolean;
  include_ai_insights: boolean;
  template_style?: string;
}

export interface PRDExportResult {
  file_url: string;
  file_name: string;
  format: string;
  size: number;
  generated_at: string;
}
