import React, { useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Calendar, 
  CheckSquare, 
  Code, 
  TestTube, 
  BookOpen, 
  Rocket, 
  Activity,
  ArrowLeft,
  Settings
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Tab Components (we'll create these)
import { RequirementsTab } from '@/components/sdlc/RequirementsTab';
import { PlanningTab } from '@/components/sdlc/PlanningTab';
import { TasksTab } from '@/components/sdlc/TasksTab';
import { ImplementationTab } from '@/components/sdlc/ImplementationTab';
import { TestingTab } from '@/components/sdlc/TestingTab';
import { DocumentationTab } from '@/components/sdlc/DocumentationTab';
import { DeploymentTab } from '@/components/sdlc/DeploymentTab';
import { MonitoringTab } from '@/components/sdlc/MonitoringTab';

export const ProjectSDLC: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('requirements');

  // Mock project data - will be replaced with real API call
  const project = {
    id: id,
    name: 'E-commerce Platform Redesign',
    description: 'Complete redesign of the e-commerce platform with modern UI/UX',
    status: 'in-progress',
    currentPhase: 'implementation',
    completionPercentage: 65,
    methodology: 'agile',
    projectHealth: 'green',
    startDate: '2024-01-15',
    endDate: '2024-06-30',
    teamSize: 8,
    budget: { allocated: 150000, spent: 97500 }
  };

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!hasPermission('project:read')) {
    return <Navigate to="/" replace />;
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getPhaseProgress = (phase: string) => {
    const phases = ['requirements', 'planning', 'implementation', 'testing', 'deployment', 'monitoring'];
    const currentIndex = phases.indexOf(project.currentPhase);
    const phaseIndex = phases.indexOf(phase);
    
    if (phaseIndex < currentIndex) return 100;
    if (phaseIndex === currentIndex) return project.completionPercentage;
    return 0;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Projects
                </Link>
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
                <p className="text-gray-600 mt-1">{project.description}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${getHealthColor(project.projectHealth)}`}></div>
                <span className="text-sm font-medium capitalize">{project.projectHealth} Health</span>
              </div>
              <Badge variant="secondary" className="px-3 py-1">
                {project.methodology.toUpperCase()}
              </Badge>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Project Overview */}
          <div className="pb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{project.completionPercentage}%</div>
                  <p className="text-sm text-gray-600">Overall Progress</p>
                  <Progress value={project.completionPercentage} className="mt-2" />
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{project.teamSize}</div>
                  <p className="text-sm text-gray-600">Team Members</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">${(project.budget.spent / 1000).toFixed(0)}k</div>
                  <p className="text-sm text-gray-600">Budget Spent</p>
                  <div className="text-xs text-gray-500 mt-1">
                    of ${(project.budget.allocated / 1000).toFixed(0)}k allocated
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold capitalize">{project.currentPhase}</div>
                  <p className="text-sm text-gray-600">Current Phase</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* SDLC Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="requirements" className="flex flex-col items-center space-y-1 p-3">
              <FileText className="h-4 w-4" />
              <span className="text-xs">Requirements</span>
              <Progress value={getPhaseProgress('requirements')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="planning" className="flex flex-col items-center space-y-1 p-3">
              <Calendar className="h-4 w-4" />
              <span className="text-xs">Planning</span>
              <Progress value={getPhaseProgress('planning')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex flex-col items-center space-y-1 p-3">
              <CheckSquare className="h-4 w-4" />
              <span className="text-xs">Tasks</span>
              <Progress value={getPhaseProgress('implementation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="implementation" className="flex flex-col items-center space-y-1 p-3">
              <Code className="h-4 w-4" />
              <span className="text-xs">Implementation</span>
              <Progress value={getPhaseProgress('implementation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="testing" className="flex flex-col items-center space-y-1 p-3">
              <TestTube className="h-4 w-4" />
              <span className="text-xs">Testing</span>
              <Progress value={getPhaseProgress('testing')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="documentation" className="flex flex-col items-center space-y-1 p-3">
              <BookOpen className="h-4 w-4" />
              <span className="text-xs">Documentation</span>
              <Progress value={getPhaseProgress('documentation')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="deployment" className="flex flex-col items-center space-y-1 p-3">
              <Rocket className="h-4 w-4" />
              <span className="text-xs">Deployment</span>
              <Progress value={getPhaseProgress('deployment')} className="w-full h-1" />
            </TabsTrigger>
            <TabsTrigger value="monitoring" className="flex flex-col items-center space-y-1 p-3">
              <Activity className="h-4 w-4" />
              <span className="text-xs">Monitoring</span>
              <Progress value={getPhaseProgress('monitoring')} className="w-full h-1" />
            </TabsTrigger>
          </TabsList>

          <TabsContent value="requirements">
            <RequirementsTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="planning">
            <PlanningTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="tasks">
            <TasksTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="implementation">
            <ImplementationTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="testing">
            <TestingTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="documentation">
            <DocumentationTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="deployment">
            <DeploymentTab projectId={project.id!} />
          </TabsContent>

          <TabsContent value="monitoring">
            <MonitoringTab projectId={project.id!} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
