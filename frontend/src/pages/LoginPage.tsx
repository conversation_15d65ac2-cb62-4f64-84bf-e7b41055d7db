import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { GoogleLoginButton } from '@/components/auth/GoogleLoginButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ArrowRight, Shield, Users, Zap } from 'lucide-react';
import { toast } from 'sonner';

export const LoginPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  const handleGuestAccess = () => {
    toast.info('Continuing as guest with read-only access');
    // Navigate to main app - the auth context will handle guest permissions
    window.location.href = '/';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-4xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        
        {/* Left side - Branding and features */}
        <div className="space-y-6">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold text-gray-900">
              Orbit Project Pulse
            </h1>
            <p className="text-xl text-gray-600">
              Enterprise Project Management Platform
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Enterprise Security</h3>
                <p className="text-gray-600">Role-based access control with Google OAuth</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Team Collaboration</h3>
                <p className="text-gray-600">Seamless project and task management</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">AI-Powered</h3>
                <p className="text-gray-600">Intelligent requirements capturing and analysis</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Login form */}
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              Welcome Back
            </CardTitle>
            <CardDescription className="text-center">
              Sign in to access your projects and collaborate with your team
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <GoogleLoginButton
              onSuccess={() => {
                toast.success('Successfully signed in!');
              }}
              onError={(error) => {
                toast.error('Sign in failed. Please try again.');
                console.error('Login error:', error);
              }}
            />

            {/* Alternative OAuth Flow */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                // Direct OAuth2 flow as fallback
                const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
                const redirectUri = encodeURIComponent('http://localhost:8080/auth/callback');
                const scope = encodeURIComponent('openid email profile');
                const responseType = 'code';
                const state = Math.random().toString(36).substring(7);

                const authUrl = `https://accounts.google.com/oauth/authorize?` +
                  `client_id=${clientId}&` +
                  `redirect_uri=${redirectUri}&` +
                  `scope=${scope}&` +
                  `response_type=${responseType}&` +
                  `state=${state}`;

                console.log('Redirecting to Google OAuth:', authUrl);
                console.log('Make sure this redirect URI is in Google Cloud Console:', 'http://localhost:8080/auth/callback');
                window.location.href = authUrl;
              }}
            >
              🔄 Alternative Google Sign-In
            </Button>

            {/* Simple popup test */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                // Test if we can at least open a popup to Google
                const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
                const redirectUri = encodeURIComponent('http://localhost:8080');
                const scope = encodeURIComponent('openid email profile');
                const responseType = 'code';
                const state = Math.random().toString(36).substring(7);

                const authUrl = `https://accounts.google.com/oauth/authorize?` +
                  `client_id=${clientId}&` +
                  `redirect_uri=${redirectUri}&` +
                  `scope=${scope}&` +
                  `response_type=${responseType}&` +
                  `state=${state}`;

                console.log('Opening popup to:', authUrl);
                const popup = window.open(authUrl, 'google-auth', 'width=500,height=600');

                // Monitor the popup
                const checkClosed = setInterval(() => {
                  if (popup?.closed) {
                    clearInterval(checkClosed);
                    console.log('Popup was closed');
                  }
                }, 1000);
              }}
            >
              🪟 Test Popup to Google
            </Button>



            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue as
                </span>
              </div>
            </div>

            <Button
              variant="outline"
              className="w-full"
              onClick={handleGuestAccess}
            >
              Continue as Guest
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>

            <div className="text-xs text-center text-muted-foreground space-y-1">
              <p>
                <strong>Guest Access:</strong> Read-only access to projects and tasks
              </p>
              <p>
                <strong>Authenticated Access:</strong> Full project management capabilities
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
