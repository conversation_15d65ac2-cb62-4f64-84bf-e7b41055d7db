import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const token = searchParams.get('token');
      const error = searchParams.get('error');

      if (error) {
        console.error('OAuth error:', error);
        toast.error('Authentication failed');
        navigate('/login');
        return;
      }

      if (token) {
        // Store token and redirect
        localStorage.setItem('auth_token', token);
        // Clear URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
        toast.success('Successfully signed in!');
        navigate('/');
        return;
      }

      // If no token, something went wrong
      console.error('No token received in callback');
      toast.error('Authentication failed - no token received');
      navigate('/login');
    };

    handleCallback();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-lg">Completing sign-in...</p>
      </div>
    </div>
  );
};
