import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (error) {
        console.error('OAuth error:', error);
        toast.error('Authentication failed');
        navigate('/login');
        return;
      }

      if (!code) {
        console.error('No authorization code received');
        toast.error('No authorization code received');
        navigate('/login');
        return;
      }

      try {
        console.log('Processing OAuth callback with code:', code);
        
        // Exchange code for token on the backend
        const response = await fetch('http://localhost:3000/api/auth/google/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code, state }),
        });

        const data = await response.json();

        if (data.success) {
          // Store token and user data
          localStorage.setItem('auth_token', data.data.token);
          toast.success('Successfully signed in!');
          navigate('/');
        } else {
          throw new Error(data.error || 'Authentication failed');
        }
      } catch (error) {
        console.error('Callback processing failed:', error);
        toast.error('Authentication failed');
        navigate('/login');
      }
    };

    handleCallback();
  }, [searchParams, navigate, login]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-lg">Completing sign-in...</p>
      </div>
    </div>
  );
};
