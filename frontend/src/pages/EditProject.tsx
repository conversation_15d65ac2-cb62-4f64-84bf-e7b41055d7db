import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ProjectForm } from "@/components/projects/ProjectForm";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useNavigateWithFilters } from "@/hooks/useNavigateWithFilters";
import { ArrowLeft, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Project } from "@/types/project";

export default function EditProject() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { navigateToProject } = useNavigateWithFilters();

  const { data: project, isLoading, error } = useQuery({
    queryKey: ['project', id],
    queryFn: async () => {
      if (!id) throw new Error('Project ID is required');
      
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Project;
    },
    enabled: !!id,
  });

  const updateProjectMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!id) throw new Error('Project ID is required');
      
      // Transform empty strings to null for UUID fields
      const cleanedData = {
        ...data,
        project_lead_id: data.project_lead_id || null,
        customer_lead_id: data.customer_lead_id || null,
      };
      
      const { error } = await supabase
        .from('projects')
        .update(cleanedData)
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', id] });
      toast({
        title: "Success",
        description: "Project updated successfully!",
      });
      navigateToProject(id!);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update project. Please try again.",
        variant: "destructive",
      });
      console.error('Error updating project:', error);
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !project) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load project. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  const isArchived = project.status === 'archived';

  if (isArchived) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={() => navigateToProject(id!)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Project</h1>
            <p className="text-muted-foreground">
              This project is archived and cannot be edited
            </p>
          </div>
        </div>

        <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800 dark:text-orange-200">
            <div className="space-y-2">
              <p className="font-medium">Cannot Edit Archived Project</p>
              <p>This project is archived and cannot be modified. To make changes, you need to restore the project first.</p>
              <div className="mt-4">
                <Button 
                  variant="outline"
                  onClick={() => navigateToProject(id!)}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Return to Project
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          size="icon" 
          onClick={() => navigateToProject(id!)}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Project</h1>
          <p className="text-muted-foreground">
            Update project details and settings
          </p>
        </div>
      </div>

      <div className="max-w-4xl">
        <ProjectForm
          project={project}
          onSubmit={(data) => updateProjectMutation.mutate(data)}
          isLoading={updateProjectMutation.isPending}
        />
      </div>
    </div>
  );
}