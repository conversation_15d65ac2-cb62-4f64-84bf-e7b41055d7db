{"name": "orbit-project-pulse", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install && cd backend && npm install", "setup": "npm run install:all && npm run generate:prisma", "generate:prisma": "cd shared && npx prisma generate", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"dotenv": "^17.2.0"}}