export type ProjectType = 'internal' | 'external';
export type ProjectStatus = 'backlog' | 'not-started' | 'in-progress' | 'completed' | 'archived';
export type TaskStatus = 'to-do' | 'in-progress' | 'done';

// New priority system types
export type PriorityLevel = 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
export type EffortEstimate = 'S' | 'M' | 'L' | 'XL';
export type ImpactType = 'Revenue' | 'Platform' | 'Bug Fix' | 'R&D';

export interface Project {
  id: string;
  name: string;
  company_name: string;
  type: ProjectType;
  customer_name?: string;
  project_lead: string; // Legacy field - will be deprecated
  project_lead_id?: string;
  customer_lead?: string; // Legacy field - will be deprecated
  customer_lead_id?: string;
  customer_contact?: string;
  description?: string;
  status: ProjectStatus;
  start_date?: string;
  end_date?: string;
  original_end_date?: string;
  prd_document_link?: string;
  poc_url?: string;
  priority_order?: number; // Legacy field - will be deprecated
  created_at: string;
  updated_at: string;
  completed_at?: string;
  archived_at?: string;
  status_changed_at?: string;
  // New priority system fields
  priority_level?: PriorityLevel;
  effort_estimate?: EffortEstimate;
  impact_type?: ImpactType;
  impact_type_id?: string; // Database reference to impact_types table
  priority_assigned_at?: string;
  last_reviewed_at?: string;
  auto_escalated?: boolean;
}

export interface Task {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  assignee: string; // Legacy field - will be deprecated
  assignee_id?: string;
  due_date?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface SubTask {
  id: string;
  task_id: string;
  name: string;
  description?: string;
  assignee: string; // Legacy field - will be deprecated
  assignee_id?: string;
  due_date?: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface ProjectIntegration {
  id: string;
  project_id: string;
  integration_type: 'google_drive' | 'slack_webhook';
  integration_url: string;
  integration_data?: any;
  created_at: string;
  updated_at: string;
}

export interface ProjectWithDetails extends Project {
  tasks?: TaskWithSubTasks[];
  integrations?: ProjectIntegration[];
  progress?: number;
}

export interface TaskWithSubTasks extends Task {
  sub_tasks?: SubTask[];
}

// Priority system interfaces
export interface PriorityHistory {
  id: string;
  project_id: string;
  old_priority?: PriorityLevel;
  new_priority: PriorityLevel;
  changed_by?: string;
  change_reason?: string;
  auto_escalated: boolean;
  created_at: string;
}

export interface PriorityRule {
  id: string;
  from_priority: PriorityLevel;
  to_priority: PriorityLevel;
  max_days: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}