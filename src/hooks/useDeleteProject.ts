import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { FEATURE_FLAGS } from "@/lib/database-config";

// Import Prisma hook
import { useDeleteProject as usePrismaDeleteProject } from "@/hooks/prisma/useProjectMutations";

export function useDeleteProject() {
  if (FEATURE_FLAGS.USE_PRISMA_PROJECTS) {
    return usePrismaDeleteProject();
  }

  const queryClient = useQueryClient();
  const { toast } = useToast();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (projectId: string) => {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);

      if (error) throw error;
    },
    onSuccess: (_, projectId) => {
      // Invalidate and refetch project queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });

      toast({
        title: "Project deleted",
        description: "The project and all its tasks have been permanently deleted.",
      });

      // Navigate to dashboard if we're on the deleted project's detail page
      if (window.location.pathname.includes(projectId)) {
        navigate('/');
      }
    },
    onError: (error) => {
      console.error('Error deleting project:', error);
      toast({
        title: "Error deleting project",
        description: "Failed to delete the project. Please try again.",
        variant: "destructive",
      });
    },
  });
}