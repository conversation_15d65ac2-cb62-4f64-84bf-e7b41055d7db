import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { apiClient } from "@/lib/api-client";
import { useToast } from "@/hooks/use-toast";

export function useUpdateProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: any }) => {
      const project = await apiClient.updateProject(id, updates);
      return project;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', data.id] });
      queryClient.invalidateQueries({ queryKey: ['project-detail', data.id] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });

      toast({
        title: "Success",
        description: "Project updated successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update project.",
        variant: "destructive",
      });
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (projectId: string) => {
      await apiClient.deleteProject(projectId);
    },
    onSuccess: (_, projectId) => {
      // Invalidate and refetch project queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      queryClient.invalidateQueries({ queryKey: ['project-progress'] });

      toast({
        title: "Project deleted",
        description: "The project and all its tasks have been permanently deleted.",
      });

      // Navigate to dashboard if we're on the deleted project's detail page
      if (window.location.pathname.includes(projectId)) {
        navigate('/');
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete project.",
        variant: "destructive",
      });
    },
  });
}

export function useUpdateProjectStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectId, status }: { projectId: string, status: string }) => {
      await apiClient.updateProject(projectId, {
        status,
        status_changed_at: new Date(),
        updated_at: new Date()
      });

      return { projectId, status };
    },
    onSuccess: (data) => {
      // Optimistic update with immediate cache refresh
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error, variables) => {
      console.error('Error updating project status:', error);

      // Revert optimistic update by refreshing cache
      queryClient.invalidateQueries({ queryKey: ['projects'] });

      toast({
        title: "Update Failed",
        description: "Failed to update project status. Please try again.",
        variant: "destructive",
      });
    },
    retry: 1,
    retryDelay: 1000,
  });
}

export function useCreateProject() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: any) => {
      const project = await apiClient.createProject(data);
      return project;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
      toast({
        title: "Success",
        description: "Project created successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create project.",
        variant: "destructive",
      });
    },
  });
}
