import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { prisma } from "@/lib/prisma";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";

export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const projects = await prisma.project.findMany({
        include: {
          tasks: {
            include: {
              sub_tasks: true,
              assignee_member: true
            },
            orderBy: { created_at: 'asc' }
          },
          project_lead_member: true,
          customer_lead_member: true,
          impact_type_ref: true
        },
        orderBy: { priority_order: 'asc' }
      });

      return projects as ProjectWithDetails[];
    },
  });
}

export function useProjectsBasic() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async () => {
      const projects = await prisma.project.findMany({
        orderBy: { priority_order: 'asc' }
      });

      return projects as Project[];
    },
  });
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      const projects = await prisma.project.findMany({
        select: { id: true }
      });

      const progressMap: Record<string, number> = {};
      
      for (const project of projects) {
        // Calculate progress manually since we don't have the RPC function
        const tasks = await prisma.task.findMany({
          where: { project_id: project.id },
          include: { sub_tasks: true }
        });

        let totalTasks = 0;
        let completedTasks = 0;

        tasks.forEach(task => {
          if (task.sub_tasks.length > 0) {
            // If task has subtasks, count subtasks
            totalTasks += task.sub_tasks.length;
            completedTasks += task.sub_tasks.filter(st => st.status === 'done').length;
          } else {
            // If no subtasks, count the task itself
            totalTasks += 1;
            if (task.status === 'done') completedTasks += 1;
          }
        });

        progressMap[project.id] = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
      }

      return progressMap;
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      // Update each project's priority_order
      const updatePromises = projectIds.map((id, index) => 
        prisma.project.update({
          where: { id },
          data: { priority_order: newOrders[index] }
        })
      );
      
      await Promise.all(updatePromises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}
