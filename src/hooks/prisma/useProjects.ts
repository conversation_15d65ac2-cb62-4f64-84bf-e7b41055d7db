import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api-client";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";

export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const projects = await apiClient.getProjects();
      return projects as ProjectWithDetails[];
    },
  });
}

export function useProjectsBasic() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async () => {
      const projects = await apiClient.getProjectsBasic();
      return projects as Project[];
    },
  });
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      return await apiClient.getProjectProgress();
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      await apiClient.reorderProjects(projectIds, newOrders);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}
