import { useQuery } from '@tanstack/react-query';
import { prisma } from '@/lib/prisma';

export interface ImpactType {
  id: string;
  name: string;
  label: string;
  description: string;
  color: string;
  bg_color: string;
  border_color: string;
  is_active: boolean;
  sort_order: number;
}

export function useImpactTypes() {
  return useQuery({
    queryKey: ['impact-types'],
    queryFn: async (): Promise<ImpactType[]> => {
      const impactTypes = await prisma.impactTypes.findMany({
        where: { is_active: true },
        orderBy: { sort_order: 'asc' }
      });
      
      return impactTypes as ImpactType[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
