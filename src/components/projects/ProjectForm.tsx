import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, X, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { PROJECT_TYPES, PROJECT_STATUSES } from "@/lib/constants";
import { Project } from "@/types/project";
import { TeamMemberSelect } from "@/components/team/TeamMemberSelect";
import { cn } from "@/lib/utils";
import { PRIORITY_CONFIG, EFFORT_CONFIG, IMPACT_CONFIG } from "@/lib/priority-utils";
import { Badge } from "@/components/ui/badge";

const projectSchema = z.object({
  company_name: z.string().optional(),
  name: z.string().min(1, "Project name is required"),
  type: z.enum(['internal', 'external']),
  customer_name: z.string().optional(),
  project_lead_id: z.string().min(1, "Project lead is required"),
  customer_lead_id: z.string().optional(),
  customer_contact: z.string().optional(),
  description: z.string().optional(),
  prd_document_link: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  poc_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  status: z.enum(['backlog', 'not-started', 'in-progress', 'completed', 'archived']).default('backlog'),
  start_date: z.date().optional(),
  end_date: z.date().optional(),
  priority_level: z.enum(["P0", "P1", "P2", "P3", "P4"]).default("P3"),
  effort_estimate: z.enum(["S", "M", "L", "XL"]).default("M"),
  impact_type: z.enum(["Revenue", "Platform", "Bug Fix", "R&D"]).default("Platform"),
  // original_end_date is not included in form - it's auto-set and immutable
}).refine((data) => {
  if (data.type === "external") {
    return data.customer_name && data.customer_name.length > 0 && data.company_name && data.company_name.length > 0;
  }
  return true;
}, {
  message: "Customer name and company name are required for external projects",
  path: ["customer_name"],
}).refine((data) => {
  if (data.type === "external") {
    return data.company_name && data.company_name.length > 0;
  }
  return true;
}, {
  message: "Company name is required for external projects",
  path: ["company_name"],
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface ProjectFormProps {
  project?: Project;
  onSubmit: (data: ProjectFormData & { original_end_date?: Date }) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export function ProjectForm({ project, onSubmit, isLoading, disabled = false }: ProjectFormProps) {
  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      company_name: project?.company_name || (project?.type === "external" ? "" : undefined),
      name: project?.name || "",
      type: project?.type || "internal",
      customer_name: project?.customer_name || "",
      project_lead_id: project?.project_lead_id || "",
      customer_lead_id: project?.customer_lead_id || "",
      customer_contact: project?.customer_contact || "",
      description: project?.description || "",
      prd_document_link: project?.prd_document_link || "",
      poc_url: project?.poc_url || "",
      status: project?.status || "backlog",
      start_date: project?.start_date ? new Date(project.start_date) : undefined,
      end_date: project?.end_date ? new Date(project.end_date) : undefined,
      priority_level: project?.priority_level || "P3",
      effort_estimate: project?.effort_estimate || "M",
      impact_type: project?.impact_type || "Platform",
    },
  });

  const watchedType = form.watch("type");
  const isEditing = !!project;

  const handleFormSubmit = (data: ProjectFormData) => {
    // For internal projects, set company_name to default
    const submitData = {
      ...data,
      company_name: data.type === "internal" ? "TwoDot AI" : data.company_name,
      original_end_date: !isEditing && data.end_date ? data.end_date : undefined
    };
    onSubmit(submitData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-8">
        {/* Basic Information Section */}
        <div className="space-y-6">
          <div className="border-b pb-4">
            <h2 className="text-xl font-semibold text-foreground">Basic Information</h2>
            <p className="text-sm text-muted-foreground">Core project details and identification</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter project name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select project type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {PROJECT_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">Project Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="h-11 border-2">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {PROJECT_STATUSES.map((status) => (
                      <SelectItem 
                        key={status.value} 
                        value={status.value}
                        className={cn(
                          "cursor-pointer py-3 px-4",
                          status.value === 'backlog' && "text-purple-600",
                          status.value === 'not-started' && "text-gray-600",
                          status.value === 'in-progress' && "text-blue-600", 
                          status.value === 'completed' && "text-green-600",
                          status.value === 'archived' && "text-gray-500"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <div 
                            className={cn(
                              "w-3 h-3 rounded-full",
                              status.value === 'backlog' && "bg-purple-600",
                              status.value === 'not-started' && "bg-gray-600",
                              status.value === 'in-progress' && "bg-blue-600",
                              status.value === 'completed' && "bg-green-600",
                              status.value === 'archived' && "bg-gray-500"
                            )}
                          />
                          {status.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {watchedType === 'external' && (
            <FormField
              control={form.control}
              name="customer_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter customer name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="project_lead_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Lead</FormLabel>
                <FormControl>
                  <TeamMemberSelect
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Select project lead"
                    required
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {watchedType === 'external' && (
            <>
              <FormField
                control={form.control}
                name="company_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customer_lead_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Lead (Internal)</FormLabel>
                    <FormControl>
                      <TeamMemberSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select customer lead"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customer_contact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Contact</FormLabel>
                    <FormControl>
                      <Input placeholder="Email or phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          </div>
        </div>

        {/* Timeline Section */}
        <div className="space-y-6">
          <div className="border-b pb-4">
            <h2 className="text-xl font-semibold text-foreground">Project Timeline</h2>
            <p className="text-sm text-muted-foreground">Define project start and end dates</p>
          </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Start Date</FormLabel>
                <div className="relative">
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 pr-3 text-left font-normal justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <span>
                            {field.value ? (
                              format(field.value, "EEEE, MMMM do, yyyy")
                            ) : (
                              "Select start date"
                            )}
                          </span>
                          <CalendarIcon className="h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 z-50 bg-background border shadow-lg" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {field.value && (
                    <button
                      type="button"
                      onClick={() => field.onChange(undefined)}
                      className="absolute right-8 top-1/2 -translate-y-1/2 z-10 hover:bg-muted rounded p-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  End Date
                  {!isEditing && (
                    <span className="text-xs text-muted-foreground ml-2">
                      (Will set as original end date)
                    </span>
                  )}
                </FormLabel>
                <div className="relative">
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 pr-3 text-left font-normal justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <span>
                            {field.value ? (
                              format(field.value, "EEEE, MMMM do, yyyy")
                            ) : (
                              "Select end date"
                            )}
                          </span>
                          <CalendarIcon className="h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 z-50 bg-background border shadow-lg" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                        className="pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {field.value && (
                    <button
                      type="button"
                      onClick={() => field.onChange(undefined)}
                      className="absolute right-8 top-1/2 -translate-y-1/2 z-10 hover:bg-muted rounded p-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Show original end date for editing (read-only) */}
        {isEditing && project?.original_end_date && (
          <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div className="flex items-center text-sm">
              <CalendarIcon className="w-4 h-4 mr-2 text-amber-600" />
              <span className="text-amber-800 dark:text-amber-200 font-medium">Original End Date (Immutable):</span>
              <span className="ml-2 font-semibold text-amber-900 dark:text-amber-100">
                {format(new Date(project.original_end_date), "EEEE, MMMM do, yyyy")}
              </span>
            </div>
            <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
              This date was set when the project was created and cannot be changed.
            </p>
          </div>
        )}

        {/* Priority & Impact Assessment Section */}
        <div className="space-y-6">
          <div className="border-b pb-4">
            <h2 className="text-xl font-semibold text-foreground">Priority & Impact Assessment</h2>
            <p className="text-sm text-muted-foreground">Define project priority, effort, and business impact</p>
          </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="priority_level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Priority Level</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12">  
                          <SelectValue>
                            {field.value && (
                              <div className="flex flex-col gap-0.5">
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs font-medium", PRIORITY_CONFIG[field.value as keyof typeof PRIORITY_CONFIG]?.color)}
                                  >
                                    {field.value}
                                  </Badge>
                                  <span className="text-sm font-medium">{PRIORITY_CONFIG[field.value as keyof typeof PRIORITY_CONFIG]?.label}</span>
                                </div>
                                <p className="text-xs text-muted-foreground truncate">{PRIORITY_CONFIG[field.value as keyof typeof PRIORITY_CONFIG]?.description}</p>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(PRIORITY_CONFIG).map(([key, config]) => (
                          <SelectItem key={key} value={key} className="py-4 cursor-pointer">
                            <div className="flex flex-col gap-1">
                              <div className="flex items-center gap-3">
                                <Badge 
                                  variant="outline" 
                                  className={cn("text-xs font-medium", config.color)}
                                >
                                  {key}
                                </Badge>
                                <span className="text-sm font-medium">{config.label}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{config.description}</p>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="effort_estimate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Effort Estimate</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue>
                            {field.value && (
                              <div className="flex flex-col gap-0.5">
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs font-medium", EFFORT_CONFIG[field.value as keyof typeof EFFORT_CONFIG]?.color)}
                                  >
                                    {field.value}
                                  </Badge>
                                  <span className="text-sm font-medium">{EFFORT_CONFIG[field.value as keyof typeof EFFORT_CONFIG]?.label}</span>
                                </div>
                                <p className="text-xs text-muted-foreground truncate">{EFFORT_CONFIG[field.value as keyof typeof EFFORT_CONFIG]?.description}</p>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(EFFORT_CONFIG).map(([key, config]) => (
                          <SelectItem key={key} value={key} className="py-4 cursor-pointer">
                            <div className="flex flex-col gap-1">
                              <div className="flex items-center gap-3">
                                <Badge 
                                  variant="outline" 
                                  className={cn("text-xs font-medium", config.color)}
                                >
                                  {key}
                                </Badge>
                                <span className="text-sm font-medium">{config.label}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{config.description}</p>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="impact_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Impact Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-12">
                          <SelectValue>
                            {field.value && (
                              <div className="flex flex-col gap-0.5">
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs font-medium", IMPACT_CONFIG[field.value as keyof typeof IMPACT_CONFIG]?.color)}
                                  >
                                    {field.value}
                                  </Badge>
                                  <span className="text-sm font-medium">{IMPACT_CONFIG[field.value as keyof typeof IMPACT_CONFIG]?.label}</span>
                                </div>
                                <p className="text-xs text-muted-foreground truncate">{IMPACT_CONFIG[field.value as keyof typeof IMPACT_CONFIG]?.description}</p>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(IMPACT_CONFIG).map(([key, config]) => (
                          <SelectItem key={key} value={key} className="py-4 cursor-pointer">
                            <div className="flex flex-col gap-1">
                              <div className="flex items-center gap-3">
                                <Badge 
                                  variant="outline" 
                                  className={cn("text-xs font-medium", config.color)}
                                >
                                  {config.label}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground text-left">{config.description}</p>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {/* Additional Details Section */}
        <div className="space-y-6">
          <div className="border-b pb-4">
            <h2 className="text-xl font-semibold text-foreground">Additional Details</h2>
            <p className="text-sm text-muted-foreground">Project description and documentation links</p>
          </div>
          
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter project description..." 
                    className="min-h-[120px] resize-y"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

        <FormField
          control={form.control}
          name="prd_document_link"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PRD Document Link</FormLabel>
              <FormControl>
                <Input
                  type="url"
                  placeholder="Enter PRD document URL (optional)"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="poc_url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>POC/Demo URL</FormLabel>
              <FormControl>
                <Input
                  type="url"
                  placeholder="Enter proof of concept or demo URL (optional)"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => window.history.back()}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isLoading}
            className="min-w-[120px]"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? 'Update Project' : 'Create Project'}
          </Button>
        </div>
      </form>
    </Form>
  );
}