import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, AlertTriangle, TrendingUp, Edit2 } from "lucide-react";
import { ProjectWithDetails } from "@/types/project";
import { format, differenceInDays } from "date-fns";
import { cn } from "@/lib/utils";
import { EditTimelineDialog } from "./EditTimelineDialog";

interface ProjectTimelineCardProps {
  project: ProjectWithDetails;
}

export function ProjectTimelineCard({ project }: ProjectTimelineCardProps) {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const getTimelineStatus = () => {
    if (!project.end_date) return null;
    
    const endDate = new Date(project.end_date);
    const today = new Date();
    
    if (project.status === 'completed' && project.completed_at) {
      const completedDate = new Date(project.completed_at);
      const daysFromDeadline = differenceInDays(completedDate, endDate);
      
      if (daysFromDeadline < 0) {
        // Completed early
        return { 
          status: 'completed-early', 
          message: `Completed ${Math.abs(daysFromDeadline)} days early`, 
          color: 'text-emerald-600', 
          bgColor: 'bg-emerald-50',
          borderColor: 'border-emerald-200',
          icon: TrendingUp
        };
      } else if (daysFromDeadline === 0) {
        // Completed on time
        return { 
          status: 'completed-on-time', 
          message: 'Completed on deadline', 
          color: 'text-green-600', 
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: TrendingUp
        };
      } else {
        // Completed late
        return { 
          status: 'completed-late', 
          message: `Completed ${daysFromDeadline} days late`, 
          color: 'text-red-600', 
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: AlertTriangle
        };
      }
    } else if (project.status === 'completed') {
      // Completed but no end date or completed date
      return { 
        status: 'completed', 
        message: 'Project Completed', 
        color: 'text-green-600', 
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: TrendingUp
      };
    }
    
    const daysUntilEnd = differenceInDays(endDate, today);
    
    if (daysUntilEnd < 0) {
      return { 
        status: 'overdue', 
        message: `${Math.abs(daysUntilEnd)} days overdue`, 
        color: 'text-red-600', 
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: AlertTriangle
      };
    }
    
    if (daysUntilEnd <= 7) {
      return { 
        status: 'warning', 
        message: `${daysUntilEnd} days remaining`, 
        color: 'text-orange-600', 
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        icon: Clock
      };
    }
    
    return { 
      status: 'on-track', 
      message: `${daysUntilEnd} days remaining`, 
      color: 'text-green-600', 
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      icon: TrendingUp
    };
  };

  const timelineStatus = getTimelineStatus();
  const hasDateChanges = project.original_end_date && project.end_date && 
    project.original_end_date !== project.end_date;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Timeline
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setEditDialogOpen(true)}
            className="h-8 w-8 p-0"
          >
            <Edit2 className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Timeline Status */}
        {timelineStatus && (
          <div className={cn(
            "p-3 rounded-lg border",
            timelineStatus.bgColor,
            timelineStatus.borderColor
          )}>
            <div className="flex items-center gap-2 mb-1">
              <timelineStatus.icon className={cn("h-4 w-4", timelineStatus.color)} />
              <span className={cn("text-sm font-medium", timelineStatus.color)}>
                {timelineStatus.message}
              </span>
            </div>
          </div>
        )}

        {/* Start Date */}
        {project.start_date && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Start Date</span>
            <span className="text-sm font-medium">
              {format(new Date(project.start_date), 'MMM dd, yyyy')}
            </span>
          </div>
        )}

        {/* End Date */}
        {project.end_date && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Target End</span>
            <span className="text-sm font-medium">
              {format(new Date(project.end_date), 'MMM dd, yyyy')}
            </span>
          </div>
        )}

        {/* Original End Date (if changed) */}
        {hasDateChanges && (
          <div className="pt-2 border-t">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium text-orange-700">Date Changed</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Original End</span>
              <span className="text-sm line-through text-muted-foreground">
                {format(new Date(project.original_end_date!), 'MMM dd, yyyy')}
              </span>
            </div>
          </div>
        )}

        {/* Completion Date */}
        {project.completed_at && (
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Completed</span>
              <span className="text-sm font-medium text-green-600">
                {format(new Date(project.completed_at), 'MMM dd, yyyy')}
              </span>
            </div>
          </div>
        )}
      </CardContent>

      <EditTimelineDialog
        project={project}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
      />
    </Card>
  );
}