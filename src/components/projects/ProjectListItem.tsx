import { Project } from "@/types/project";
import { User, Home, Building2, Calendar, Archive } from "lucide-react";
import { cn } from "@/lib/utils";
import { PROJECT_STATUSES } from "@/lib/constants";
import { format } from "date-fns";
import { ProjectTags } from "@/components/ui/priority-badge";
import { ConvertToTaskButton } from "./ConvertToTaskButton";
import { ArchiveProjectButton } from "./ArchiveProjectButton";
import { UnarchiveProjectButton } from "./UnarchiveProjectButton";
import { Badge } from "@/components/ui/badge";
import { getDueDateColorScheme } from "@/lib/date-utils";
import { useNavigateWithFilters } from "@/hooks/useNavigateWithFilters";

interface ProjectListItemProps {
  project: Project;
  tasks?: Array<{ status: string; sub_tasks?: Array<{ status: string }> }>;
}

export function ProjectListItem({ project, tasks = [] }: ProjectListItemProps) {
  const { navigateToProject } = useNavigateWithFilters();
  // Get due date proximity colors
  const colorScheme = getDueDateColorScheme(project);
  
  const getCardBackgroundColor = () => colorScheme.background;
  const getCardTextColor = () => colorScheme.text;
  const getCardMutedColor = () => colorScheme.muted;

  const getStatusBadgeStyles = (status: string): string => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-200 dark:border-green-800';
      case 'in-progress':
        return 'bg-orange-200 text-orange-800 border-orange-300 dark:bg-orange-800/50 dark:text-orange-200 dark:border-orange-700';
      case 'not-started':
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/50 dark:text-gray-200 dark:border-gray-800';
      case 'backlog':
        return 'bg-red-200 text-red-800 border-red-300 dark:bg-red-800/50 dark:text-red-200 dark:border-red-700';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/50 dark:text-gray-200 dark:border-gray-800';
    }
  };

  const getCompanyDisplay = () => {
    if (project.type === 'internal') {
      return 'Internal';
    } else {
      return project.company_name || 'External';
    }
  };

  const getCompanyIcon = () => {
    return project.type === 'internal' ? Home : Building2;
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch {
      return "-";
    }
  };

  return (
    <div className="group relative">
      <div 
        onClick={() => navigateToProject(project.id)}
        className={cn(
          "rounded-lg transition-all duration-200 cursor-pointer hover:shadow-md p-4", 
          getCardBackgroundColor(),
          project.status === 'archived' && "opacity-70"
        )}
      >
        {/* Archived Badge Row */}
        {project.status === 'archived' && (
          <div className="mb-3">
            <Badge variant="secondary" className="bg-muted text-muted-foreground text-xs font-medium">
              <Archive className="h-3 w-3 mr-1" />
              ARCHIVED
            </Badge>
          </div>
        )}
          
          {/* Header Row */}
          <div className="grid grid-cols-[2fr_0.8fr_0.8fr_1fr_1fr_1.5fr_1fr] gap-4 items-center">
          {/* Project Name */}
          <h3 className={cn("text-base font-medium truncate min-w-0", getCardTextColor())}>
            {project.name}
          </h3>
          
          {/* Lead */}
          <div className={cn("flex items-center gap-1 text-sm min-w-0", getCardMutedColor())}>
            <User className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{project.project_lead}</span>
          </div>
          
          {/* Company */}
          <div className={cn("flex items-center gap-1 text-sm min-w-0", getCardMutedColor())}>
            {(() => {
              const CompanyIcon = getCompanyIcon();
              return <CompanyIcon className="w-4 h-4 flex-shrink-0" />;
            })()}
            <span className="truncate">{getCompanyDisplay()}</span>
          </div>
          
          {/* Start Date */}
          <div className={cn("flex items-center gap-1 text-sm min-w-0", getCardMutedColor())}>
            <Calendar className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{formatDate(project.start_date)}</span>
          </div>
          
          {/* End Date */}
          <div className={cn("flex items-center gap-1 text-sm min-w-0", getCardMutedColor())}>
            <Calendar className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{formatDate(project.end_date)}</span>
          </div>
          
          {/* Priority Tags */}
          <div className="flex flex-wrap gap-1 min-w-0">
            <ProjectTags
              priority={project.priority_level || 'P3'}
              effort={project.effort_estimate || 'M'}
              impact={project.impact_type || 'Platform'}
              priorityAssignedAt={project.priority_assigned_at}
              autoEscalated={project.auto_escalated}
              className="scale-90"
            />
          </div>
          
          {/* Status */}
          <div className="flex justify-end">
            <span className={cn(
              "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border",
              getStatusBadgeStyles(project.status)
            )}>
              {PROJECT_STATUSES.find(s => s.value === project.status)?.label || project.status}
            </span>
          </div>
        </div>
      </div>
      
      {/* Action buttons - positioned absolutely */}
      <div className="absolute top-2 right-2 z-10 flex gap-1">
        {project.status === 'archived' ? (
          <UnarchiveProjectButton
            projectId={project.id}
            projectName={project.name}
            variant="outline"
            className="opacity-100 transition-opacity"
          />
        ) : project.status === 'completed' ? (
          <ArchiveProjectButton
            projectId={project.id}
            projectName={project.name}
            variant="outline"
            className="opacity-100 transition-opacity"
          />
        ) : null}
        <ConvertToTaskButton 
          project={project} 
          variant="outline"
          size="sm"
          className="opacity-0 group-hover:opacity-100 transition-opacity"
        />
      </div>
    </div>
  );
}