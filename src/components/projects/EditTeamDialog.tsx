import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { ProjectWithDetails } from "@/types/project";
import { useUpdateProject } from "@/hooks/useUpdateProject";
import { TeamMemberSelect } from "@/components/team/TeamMemberSelect";
import { supabase } from "@/integrations/supabase/client";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const teamSchema = z.object({
  project_lead_id: z.string().min(1, "Project lead is required"),
  customer_lead_id: z.string().optional(),
  customer_name: z.string().optional(),
  customer_contact: z.string().optional(),
});

type TeamFormData = z.infer<typeof teamSchema>;

interface EditTeamDialogProps {
  project: ProjectWithDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditTeamDialog({ project, open, onOpenChange }: EditTeamDialogProps) {
  const updateProject = useUpdateProject();

  const form = useForm<TeamFormData>({
    resolver: zodResolver(teamSchema),
    defaultValues: {
      project_lead_id: project.project_lead_id || "",
      customer_lead_id: project.customer_lead_id || "",
      customer_name: project.customer_name || "",
      customer_contact: project.customer_contact || "",
    },
  });

  const handleSubmit = async (data: TeamFormData) => {
    try {
      // Get team member names for the selected IDs
      const teamMemberIds = [data.project_lead_id, data.customer_lead_id].filter(Boolean);
      const { data: teamMembers } = await supabase
        .from('team_members')
        .select('id, name')
        .in('id', teamMemberIds);

      const projectLead = teamMembers?.find(member => member.id === data.project_lead_id);
      const customerLead = teamMembers?.find(member => member.id === data.customer_lead_id);

      const updates = {
        project_lead_id: data.project_lead_id || null,
        project_lead: projectLead?.name || project.project_lead,
        customer_lead_id: data.customer_lead_id || null,
        customer_lead: customerLead?.name || null,
        customer_name: data.customer_name || null,
        customer_contact: data.customer_contact || null,
      };

      updateProject.mutate(
        { id: project.id, updates },
        {
          onSuccess: () => {
            onOpenChange(false);
            form.reset();
          },
        }
      );
    } catch (error) {
      console.error('Error updating project team:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Team</DialogTitle>
          <DialogDescription>
            Update the project lead and customer information.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="project_lead_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Lead</FormLabel>
                  <FormControl>
                    <TeamMemberSelect
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select project lead"
                      required
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customer_lead_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Lead (Internal)</FormLabel>
                  <FormControl>
                    <TeamMemberSelect
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select customer lead"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customer_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter customer name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customer_contact"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Contact</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Email or phone number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateProject.isPending}>
                {updateProject.isPending ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}