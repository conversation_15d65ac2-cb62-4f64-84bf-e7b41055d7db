import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Tit<PERSON> } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useConvertTaskToProject } from "@/hooks/useConvertTaskToProject";
import { PROJECT_TYPES } from "@/lib/constants";
import type { Task } from "@/types/project";

interface ConvertToProjectD<PERSON>ogProps {
  task: Task;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ConvertToProjectDialog = ({ task, open, onOpenChange }: ConvertToProjectDialogProps) => {
  const [formData, setFormData] = useState({
    projectName: task.name,
    projectDescription: task.description || "",
    type: "internal",
    companyName: "TwoDot AI",
    startDate: "",
    endDate: task.due_date || "",
    customerName: "",
    customerLead: "",
    customerContact: "",
    prdDocumentLink: "",
    pocUrl: "",
    effortEstimate: "M" as const,
    impactType: "Platform" as const,
    priorityLevel: "P3" as const,
  });

  const [showConfirmation, setShowConfirmation] = useState(false);
  const convertTaskToProject = useConvertTaskToProject();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleConvert = () => {
    convertTaskToProject.mutate({
      taskId: task.id,
      ...formData,
    });
    onOpenChange(false);
    setShowConfirmation(false);
  };

  // Note: subtasks are passed separately in the task management system
  const subtaskCount = 0; // Will be handled by the conversion function

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Convert Task to Project</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="projectName">Project Name *</Label>
              <Input
                id="projectName"
                value={formData.projectName}
                onChange={(e) => handleInputChange("projectName", e.target.value)}
                placeholder="Enter project name"
              />
            </div>
            
            <div>
              <Label htmlFor="type">Project Type *</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="projectDescription">Project Description</Label>
            <Textarea
              id="projectDescription"
              value={formData.projectDescription}
              onChange={(e) => handleInputChange("projectDescription", e.target.value)}
              placeholder="Enter project description"
              rows={3}
            />
          </div>

          {formData.type === "external" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange("companyName", e.target.value)}
                  placeholder="Enter company name"
                />
              </div>
              
              <div>
                <Label htmlFor="customerName">Customer Name *</Label>
                <Input
                  id="customerName"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange("customerName", e.target.value)}
                  placeholder="Enter customer name"
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange("startDate", e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange("endDate", e.target.value)}
              />
            </div>
          </div>

          {formData.type === "external" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customerLead">Customer Lead</Label>
                <Input
                  id="customerLead"
                  value={formData.customerLead}
                  onChange={(e) => handleInputChange("customerLead", e.target.value)}
                  placeholder="Enter customer lead"
                />
              </div>
              
              <div>
                <Label htmlFor="customerContact">Customer Contact</Label>
                <Input
                  id="customerContact"
                  value={formData.customerContact}
                  onChange={(e) => handleInputChange("customerContact", e.target.value)}
                  placeholder="Enter customer contact"
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="prdDocumentLink">PRD Document Link</Label>
              <Input
                id="prdDocumentLink"
                value={formData.prdDocumentLink}
                onChange={(e) => handleInputChange("prdDocumentLink", e.target.value)}
                placeholder="Enter PRD document link"
              />
            </div>
            
            <div>
              <Label htmlFor="pocUrl">POC URL</Label>
              <Input
                id="pocUrl"
                value={formData.pocUrl}
                onChange={(e) => handleInputChange("pocUrl", e.target.value)}
                placeholder="Enter POC URL"
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="effortEstimate">Effort Estimate</Label>
              <Select value={formData.effortEstimate} onValueChange={(value: any) => handleInputChange("effortEstimate", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="S">S - Small</SelectItem>
                  <SelectItem value="M">M - Medium</SelectItem>
                  <SelectItem value="L">L - Large</SelectItem>
                  <SelectItem value="XL">XL - Extra Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="impactType">Impact Type</Label>
              <Select value={formData.impactType} onValueChange={(value: any) => handleInputChange("impactType", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Revenue">Revenue</SelectItem>
                  <SelectItem value="Platform">Platform</SelectItem>
                  <SelectItem value="Bug Fix">Bug Fix</SelectItem>
                  <SelectItem value="R&D">R&D</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priorityLevel">Priority Level</Label>
              <Select value={formData.priorityLevel} onValueChange={(value: any) => handleInputChange("priorityLevel", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="P0">P0 - Critical</SelectItem>
                  <SelectItem value="P1">P1 - High</SelectItem>
                  <SelectItem value="P2">P2 - Medium</SelectItem>
                  <SelectItem value="P3">P3 - Low</SelectItem>
                  <SelectItem value="P4">P4 - Lowest</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          
          <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
            <AlertDialogTrigger asChild>
              <Button 
                onClick={() => setShowConfirmation(true)}
                disabled={
                  !formData.projectName.trim() ||
                  (formData.type === "external" && (!formData.customerName.trim() || !formData.companyName.trim()))
                }
              >
                Convert to Project
              </Button>
            </AlertDialogTrigger>
            
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirm Task Conversion</AlertDialogTitle>
                <AlertDialogDescription className="space-y-2">
                  <p>Are you sure you want to convert this task to a project?</p>
                  <div className="bg-muted p-3 rounded-md text-sm">
                    <p><strong>Task:</strong> {task.name}</p>
                    <p><strong>New Project:</strong> {formData.projectName}</p>
                    <p><strong>Assigned to:</strong> {task.assignee}</p>
                    {subtaskCount > 0 && (
                      <p><strong>Subtasks:</strong> {subtaskCount} subtasks will become regular tasks</p>
                    )}
                  </div>
                  <p className="text-destructive font-medium">
                    Warning: This action cannot be undone. The original task will be deleted.
                  </p>
                </AlertDialogDescription>
              </AlertDialogHeader>
              
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleConvert} disabled={convertTaskToProject.isPending}>
                  {convertTaskToProject.isPending ? "Converting..." : "Convert to Project"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </DialogContent>
    </Dialog>
  );
};