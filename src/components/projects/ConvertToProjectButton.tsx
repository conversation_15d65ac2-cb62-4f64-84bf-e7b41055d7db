import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import { ConvertToProjectDialog } from "./ConvertToProjectDialog";
import type { Task } from "@/types/project";

interface ConvertToProjectButtonProps {
  task: Task;
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
}

export const ConvertToProjectButton = ({ 
  task, 
  variant = "outline", 
  size = "sm",
  className 
}: ConvertToProjectButtonProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setDialogOpen(true)}
        className={className}
      >
        <ArrowUpRight className="h-4 w-4 mr-1" />
        Convert to Project
      </Button>
      
      <ConvertToProjectDialog
        task={task}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
      />
    </>
  );
};