import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, CheckCircle2, Clock, TrendingUp } from "lucide-react";
import { ProjectWithDetails } from "@/types/project";
import { differenceInDays, format } from "date-fns";
import { cn } from "@/lib/utils";

interface ProjectKeyMetricsProps {
  project: ProjectWithDetails;
}

export function ProjectKeyMetrics({ project }: ProjectKeyMetricsProps) {
  const getTimelineStatus = () => {
    if (!project.end_date) return null;
    
    const endDate = new Date(project.end_date);
    const today = new Date();
    const daysUntilEnd = differenceInDays(endDate, today);
    
    if (project.status === 'completed') {
      return { status: 'completed', message: 'Completed', color: 'text-green-600', bgColor: 'bg-green-50', icon: CheckCircle2 };
    }
    
    if (daysUntilEnd < 0) {
      return { 
        status: 'overdue', 
        message: `${Math.abs(daysUntilEnd)} days overdue`, 
        color: 'text-red-600', 
        bgColor: 'bg-red-50',
        icon: AlertTriangle
      };
    }
    
    if (daysUntilEnd <= 7) {
      return { 
        status: 'warning', 
        message: `${daysUntilEnd} days left`, 
        color: 'text-orange-600', 
        bgColor: 'bg-orange-50',
        icon: Clock
      };
    }
    
    return { 
      status: 'on-track', 
      message: `${daysUntilEnd} days left`, 
      color: 'text-green-600', 
      bgColor: 'bg-green-50',
      icon: TrendingUp
    };
  };

  const getTaskSummary = () => {
    const allTasks = project.tasks || [];
    const totalTasks = allTasks.length;
    const completedTasks = allTasks.filter(task => task.status === 'done').length;
    
    const allSubTasks = allTasks.flatMap(task => task.sub_tasks || []);
    const totalSubTasks = allSubTasks.length;
    const completedSubTasks = allSubTasks.filter(subTask => subTask.status === 'done').length;
    
    const totalItems = totalTasks + totalSubTasks;
    const completedItems = completedTasks + completedSubTasks;
    
    return {
      totalTasks,
      completedTasks,
      totalSubTasks,
      completedSubTasks,
      totalItems,
      completedItems,
      completionRate: totalItems > 0 ? (completedItems / totalItems) * 100 : 0
    };
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'P0':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'P1':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'P2':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'P3':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'P4':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const timelineStatus = getTimelineStatus();
  const taskSummary = getTaskSummary();

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Priority Level */}
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-muted">
              <AlertTriangle className="h-5 w-5 text-muted-foreground" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Priority</p>
              <Badge 
                variant="outline" 
                className={cn("font-medium", getPriorityColor(project.priority_level || 'P3'))}
              >
                {project.priority_level || 'P3'}
              </Badge>
            </div>
          </div>

          {/* Timeline Status */}
          {timelineStatus && (
            <div className="flex items-center gap-3">
              <div className={cn("p-2 rounded-lg", timelineStatus.bgColor)}>
                <timelineStatus.icon className={cn("h-5 w-5", timelineStatus.color)} />
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Timeline</p>
                <p className={cn("text-sm font-medium", timelineStatus.color)}>
                  {timelineStatus.message}
                </p>
              </div>
            </div>
          )}

          {/* Task Completion Summary */}
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-muted">
              <CheckCircle2 className="h-5 w-5 text-muted-foreground" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Progress</p>
              <div className="flex flex-col gap-1">
                {taskSummary.totalTasks > 0 && (
                  <p className="text-sm font-medium">
                    {taskSummary.completedTasks} of {taskSummary.totalTasks} tasks
                  </p>
                )}
                {taskSummary.totalSubTasks > 0 && (
                  <p className="text-xs text-muted-foreground">
                    {taskSummary.completedSubTasks} of {taskSummary.totalSubTasks} subtasks
                  </p>
                )}
                {taskSummary.totalItems === 0 && (
                  <p className="text-sm text-muted-foreground">No tasks yet</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}