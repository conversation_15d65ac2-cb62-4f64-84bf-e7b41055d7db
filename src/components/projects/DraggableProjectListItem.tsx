import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { ProjectListItem } from './ProjectListItem';
import { Project } from '@/types/project';

interface DraggableProjectListItemProps {
  project: Project;
  tasks?: Array<{ status: string; sub_tasks?: Array<{ status: string }> }>;
}

export function DraggableProjectListItem({ project, tasks }: DraggableProjectListItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: project.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative ${isDragging ? 'opacity-50 z-50' : ''}`}
    >
      <div className="group relative">
        <div
          className="absolute top-1/2 left-2 -translate-y-1/2 z-10 cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-4 w-4 text-muted-foreground hover:text-foreground" />
        </div>
        <div className="pl-8">
          <ProjectListItem project={project} tasks={tasks} />
        </div>
      </div>
    </div>
  );
}