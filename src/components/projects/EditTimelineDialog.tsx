import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { format } from "date-fns";
import { Calendar as CalendarIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { ProjectWithDetails } from "@/types/project";
import { useUpdateProject } from "@/hooks/useUpdateProject";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const timelineSchema = z.object({
  start_date: z.date().optional(),
  end_date: z.date().optional(),
}).refine((data) => {
  if (data.start_date && data.end_date) {
    return data.end_date >= data.start_date;
  }
  return true;
}, {
  message: "End date must be after start date",
  path: ["end_date"],
});

type TimelineFormData = z.infer<typeof timelineSchema>;

interface EditTimelineDialogProps {
  project: ProjectWithDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditTimelineDialog({ project, open, onOpenChange }: EditTimelineDialogProps) {
  const updateProject = useUpdateProject();

  const form = useForm<TimelineFormData>({
    resolver: zodResolver(timelineSchema),
    defaultValues: {
      start_date: project.start_date ? new Date(project.start_date) : undefined,
      end_date: project.end_date ? new Date(project.end_date) : undefined,
    },
  });

  const handleSubmit = async (data: TimelineFormData) => {
    const updates: { start_date?: string | null; end_date?: string | null; original_end_date?: string } = {};
    
    if (data.start_date) {
      updates.start_date = data.start_date.toISOString().split('T')[0];
    } else {
      updates.start_date = null;
    }
    
    if (data.end_date) {
      updates.end_date = data.end_date.toISOString().split('T')[0];
      
      // Set original_end_date only if it doesn't exist and we're setting an end_date
      if (!project.original_end_date) {
        updates.original_end_date = data.end_date.toISOString().split('T')[0];
      }
    } else {
      updates.end_date = null;
    }

    updateProject.mutate(
      { id: project.id, updates },
      {
        onSuccess: () => {
          onOpenChange(false);
          form.reset();
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Timeline</DialogTitle>
          <DialogDescription>
            Update the project start and end dates.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="start_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Start Date</FormLabel>
                  <div className="relative">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 pr-3 text-left font-normal justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <span>
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                "Select start date"
                              )}
                            </span>
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    {field.value && (
                      <button
                        type="button"
                        onClick={() => field.onChange(undefined)}
                        className="absolute right-8 top-1/2 -translate-y-1/2 z-10 hover:bg-muted rounded p-1"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="end_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>End Date</FormLabel>
                  <div className="relative">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 pr-3 text-left font-normal justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <span>
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                "Select end date"
                              )}
                            </span>
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    {field.value && (
                      <button
                        type="button"
                        onClick={() => field.onChange(undefined)}
                        className="absolute right-8 top-1/2 -translate-y-1/2 z-10 hover:bg-muted rounded p-1"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Show original end date if it exists */}
            {project.original_end_date && (
              <div className="bg-muted/30 border rounded-lg p-3">
                <div className="flex items-center text-sm">
                  <CalendarIcon className="w-4 h-4 mr-2 text-orange-600" />
                  <span className="text-muted-foreground">Original End Date:</span>
                  <span className="ml-2 font-medium">
                    {format(new Date(project.original_end_date), "PPP")}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  This date was set when the project was created and cannot be changed.
                </p>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateProject.isPending}>
                {updateProject.isPending ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}