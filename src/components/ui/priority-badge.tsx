import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { PriorityLevel, EffortEstimate, ImpactType } from '@/types/project';
import { 
  getPriorityConfig, 
  getEffortConfig, 
  getImpactConfig,
  calculateDaysInPriority,
  isOverdue,
  getPriorityIntensity 
} from '@/lib/priority-utils';
import { useImpactTypes, getImpactTypeById } from '@/hooks/useImpactTypes';
import { Clock, AlertTriangle } from 'lucide-react';

interface PriorityBadgeProps {
  priority: PriorityLevel;
  priorityAssignedAt?: string;
  autoEscalated?: boolean;
  showAge?: boolean;
  className?: string;
}

export function PriorityBadge({ 
  priority, 
  priorityAssignedAt, 
  autoEscalated, 
  showAge = false,
  className 
}: PriorityBadgeProps) {
  const config = getPriorityConfig(priority);
  const daysInPriority = priorityAssignedAt ? calculateDaysInPriority(priorityAssignedAt) : 0;
  const overdue = priorityAssignedAt ? isOverdue(priority, daysInPriority) : false;
  const intensity = priorityAssignedAt ? getPriorityIntensity(priority, daysInPriority) : 0;

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <Badge
        className={cn(
          "relative text-xs font-medium border",
          overdue && "ring-2 ring-destructive/50",
        )}
        style={{
          backgroundColor: config.bgColor,
          color: config.textColor,
          borderColor: config.borderColor,
          opacity: intensity > 50 ? 1 : 0.8 + (intensity / 100 * 0.2),
        }}
      >
        <span>{priority}</span>
        {autoEscalated && (
          <AlertTriangle className="h-3 w-3 ml-1" />
        )}
      </Badge>
      
      {showAge && priorityAssignedAt && (
        <Badge 
          variant="outline" 
          className={cn(
            "text-xs",
            overdue && "text-destructive border-destructive"
          )}
        >
          <Clock className="h-3 w-3 mr-1" />
          {daysInPriority}d
        </Badge>
      )}
      
      {autoEscalated && (
        <Badge variant="destructive" className="text-xs">
          AGED
        </Badge>
      )}
    </div>
  );
}

interface EffortBadgeProps {
  effort: EffortEstimate;
  className?: string;
}

export function EffortBadge({ effort, className }: EffortBadgeProps) {
  const config = getEffortConfig(effort);
  
  return (
    <Badge 
      className={cn("text-xs", className)}
      style={{
        backgroundColor: `${config.color} / 0.1`,
        color: config.color,
        borderColor: `${config.color} / 0.3`,
      }}
    >
      {effort}
    </Badge>
  );
}

interface ImpactBadgeProps {
  impact?: ImpactType;
  impactTypeId?: string;
  className?: string;
}

export function ImpactBadge({ impact, impactTypeId, className }: ImpactBadgeProps) {
  const { data: impactTypes = [] } = useImpactTypes();
  
  // Use database impact type if ID is provided
  const dbImpactType = impactTypeId ? getImpactTypeById(impactTypes, impactTypeId) : null;
  
  if (dbImpactType) {
    return (
      <Badge 
        className={cn("text-xs", className)}
        style={{
          backgroundColor: dbImpactType.bg_color,
          color: dbImpactType.color,
          borderColor: dbImpactType.border_color,
        }}
      >
        {dbImpactType.label}
      </Badge>
    );
  }
  
  // Fallback to legacy impact type
  if (impact) {
    const config = getImpactConfig(impact);
    return (
      <Badge 
        className={cn("text-xs", className)}
        style={{
          backgroundColor: config.bgColor,
          color: config.color,
          borderColor: config.borderColor,
        }}
      >
        {config.label}
      </Badge>
    );
  }
  
  // No impact type
  return (
    <Badge className={cn("text-xs text-muted-foreground", className)}>
      No Impact
    </Badge>
  );
}


interface ProjectTagsProps {
  priority: PriorityLevel;
  effort: EffortEstimate;
  impact?: ImpactType;
  impactTypeId?: string;
  priorityAssignedAt?: string;
  autoEscalated?: boolean;
  className?: string;
}

export function ProjectTags({ 
  priority, 
  effort, 
  impact, 
  impactTypeId,
  priorityAssignedAt, 
  autoEscalated,
  className 
}: ProjectTagsProps) {
  return (
    <div className={cn("flex items-center gap-1 flex-wrap", className)}>
      <PriorityBadge 
        priority={priority} 
        priorityAssignedAt={priorityAssignedAt} 
        autoEscalated={autoEscalated}
        showAge={true}
      />
      <EffortBadge effort={effort} />
      <ImpactBadge impact={impact} impactTypeId={impactTypeId} />
    </div>
  );
}