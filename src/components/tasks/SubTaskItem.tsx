import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SubTask } from "@/types/project";
import { Calendar, User, Edit, CheckCircle2, Circle, Trophy, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";
import { TaskStatusSelect } from "./TaskStatusSelect";
import { format } from "date-fns";

interface SubTaskItemProps {
  subTask: SubTask;
  onEdit: (subTask: SubTask) => void;
  onComplete: (subTask: SubTask) => void;
  onStatusChange: (subTask: SubTask, status: string) => void;
  disabled?: boolean;
}

export function SubTaskItem({ subTask, onEdit, onComplete, onStatusChange, disabled = false }: SubTaskItemProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'to-do':
        return 'bg-gray-50 border-gray-200';
      case 'in-progress':
        return 'bg-blue-50 border-blue-200';
      case 'done':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className={cn("border rounded bg-background p-3 transition-colors duration-200", getStatusColor(subTask.status))}>
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <h5 className="font-medium text-sm">{subTask.name}</h5>
            <div className="min-w-0 flex-1">
              <TaskStatusSelect
                value={subTask.status}
                onValueChange={disabled ? undefined : (status) => onStatusChange(subTask, status)}
                className="w-auto min-w-[140px]"
                disabled={disabled}
              />
            </div>
          </div>
          
          {subTask.description && (
            <p className="text-sm text-muted-foreground mb-2">
              {subTask.description}
            </p>
          )}
          
          {/* Completion Date Display */}
          {subTask.status === 'done' && subTask.completed_at && (
            <div className="mb-2 p-2 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-md">
              <div className="flex items-center gap-2 text-xs">
                <div className="flex items-center justify-center w-3 h-3 bg-green-500 rounded-full">
                  <Trophy className="w-1.5 h-1.5 text-white" />
                </div>
                <span className="text-green-700 font-medium">Done:</span>
                <span className="text-green-800 font-semibold">
                  {format(new Date(subTask.completed_at), "MMM d, h:mm a")}
                </span>
                <Sparkles className="w-2.5 h-2.5 text-green-600 animate-pulse" />
              </div>
            </div>
          )}
          
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center">
              <User className="w-3 h-3 mr-1" />
              <span>{(subTask as any).assignee_member?.name || subTask.assignee || "Unassigned"}</span>
            </div>
            
            {subTask.due_date && (
              <div className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                <span>{new Date(subTask.due_date).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={disabled ? undefined : () => onComplete(subTask)}
            className="h-7 w-7 p-0"
            title={disabled ? 'Cannot edit archived project' : (subTask.status === 'done' ? 'Mark as incomplete' : 'Mark as complete')}
            disabled={disabled}
          >
            {subTask.status === 'done' ? (
              <CheckCircle2 className="h-3 w-3 text-green-600" />
            ) : (
              <Circle className="h-3 w-3" />
            )}
          </Button>
          {!disabled && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(subTask)}
              className="h-7 w-7 p-0"
            >
              <Edit className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}