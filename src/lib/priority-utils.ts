import { PriorityLevel, EffortEstimate, ImpactType } from '@/types/project';

// Priority configuration
export const PRIORITY_CONFIG = {
  P0: {
    label: 'Critical',
    description: 'Start today',
    color: 'hsl(var(--destructive))',
    textColor: 'hsl(var(--destructive-foreground))',
    bgColor: 'hsl(var(--destructive))',
    borderColor: 'hsl(var(--destructive))',
    maxDays: null,
  },
  P1: {
    label: 'Urgent',
    description: 'This week',
    color: 'hsl(var(--orange))',
    textColor: 'hsl(var(--orange-foreground))',
    bgColor: 'hsl(var(--orange))',
    borderColor: 'hsl(var(--orange))',
    maxDays: 7,
  },
  P2: {
    label: 'Important',
    description: 'Next 2 weeks',
    color: 'hsl(var(--yellow))',
    textColor: 'hsl(var(--yellow-foreground))',
    bgColor: 'hsl(var(--yellow))',
    borderColor: 'hsl(var(--yellow))',
    maxDays: 14,
  },
  P3: {
    label: 'Scheduled',
    description: 'Next month',
    color: 'hsl(var(--blue))',
    textColor: 'hsl(var(--blue-foreground))',
    bgColor: 'hsl(var(--blue))',
    borderColor: 'hsl(var(--blue))',
    maxDays: 28,
  },
  P4: {
    label: 'Backlog',
    description: 'Future',
    color: 'hsl(var(--muted-foreground))',
    textColor: 'hsl(var(--muted-foreground))',
    bgColor: 'hsl(var(--muted))',
    borderColor: 'hsl(var(--border))',
    maxDays: null,
  },
} as const;

export const EFFORT_CONFIG = {
  S: { 
    label: 'Small', 
    description: '≤2 days', 
    color: 'hsl(var(--green))',
    bgColor: 'hsl(var(--green) / 0.1)',
    borderColor: 'hsl(var(--green) / 0.3)'
  },
  M: { 
    label: 'Medium', 
    description: '3-7 days', 
    color: 'hsl(var(--blue))',
    bgColor: 'hsl(var(--blue) / 0.1)',
    borderColor: 'hsl(var(--blue) / 0.3)'
  },
  L: { 
    label: 'Large', 
    description: '1-3 weeks', 
    color: 'hsl(var(--orange))',
    bgColor: 'hsl(var(--orange) / 0.1)',
    borderColor: 'hsl(var(--orange) / 0.3)'
  },
  XL: { 
    label: 'Extra Large', 
    description: '>3 weeks', 
    color: 'hsl(var(--destructive))',
    bgColor: 'hsl(var(--destructive) / 0.1)',
    borderColor: 'hsl(var(--destructive) / 0.3)'
  },
} as const;

// Legacy fallback config for backward compatibility
export const IMPACT_CONFIG = {
  Revenue: { 
    label: 'Revenue', 
    description: 'Direct customer/sales impact',
    color: 'hsl(var(--green))',
    bgColor: 'hsl(var(--green) / 0.1)',
    borderColor: 'hsl(var(--green) / 0.3)'
  },
  Platform: { 
    label: 'Platform', 
    description: 'Core infrastructure, agent framework, future mission',
    color: 'hsl(var(--blue))',
    bgColor: 'hsl(var(--blue) / 0.1)',
    borderColor: 'hsl(var(--blue) / 0.3)'
  },
  'Bug Fix': { 
    label: 'Bug Fix', 
    description: 'Resolving existing issues/technical problems',
    color: 'hsl(var(--purple))',
    bgColor: 'hsl(var(--purple) / 0.1)',
    borderColor: 'hsl(var(--purple) / 0.3)'
  },
  'R&D': { 
    label: 'R&D', 
    description: 'Research, experimentation, proof of concepts',
    color: 'hsl(var(--orange))',
    bgColor: 'hsl(var(--orange) / 0.1)',
    borderColor: 'hsl(var(--orange) / 0.3)'
  },
} as const;


// Utility functions
export function getPriorityConfig(priority: PriorityLevel) {
  return PRIORITY_CONFIG[priority];
}

export function getEffortConfig(effort: EffortEstimate) {
  return EFFORT_CONFIG[effort];
}

export function getImpactConfig(impact: ImpactType) {
  return IMPACT_CONFIG[impact];
}


export function calculateDaysInPriority(priorityAssignedAt: string): number {
  const assignedDate = new Date(priorityAssignedAt);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - assignedDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export function shouldEscalate(priority: PriorityLevel, daysInPriority: number): boolean {
  const config = PRIORITY_CONFIG[priority];
  return config.maxDays !== null && daysInPriority >= config.maxDays;
}

export function getNextPriority(currentPriority: PriorityLevel): PriorityLevel | null {
  const priorities: PriorityLevel[] = ['P4', 'P3', 'P2', 'P1', 'P0'];
  const currentIndex = priorities.indexOf(currentPriority);
  return currentIndex > 0 ? priorities[currentIndex - 1] : null;
}

export function formatPriorityAge(priority: PriorityLevel, daysInPriority: number): string {
  return `${priority} - ${daysInPriority} days`;
}

export function getPriorityIntensity(priority: PriorityLevel, daysInPriority: number): number {
  const config = PRIORITY_CONFIG[priority];
  if (!config.maxDays) return 0;
  
  const intensity = Math.min(daysInPriority / config.maxDays, 1);
  return Math.round(intensity * 100);
}

export function isOverdue(priority: PriorityLevel, daysInPriority: number): boolean {
  const config = PRIORITY_CONFIG[priority];
  return config.maxDays !== null && daysInPriority > config.maxDays;
}

export function getProjectTags(
  priority: PriorityLevel,
  effort: EffortEstimate,
  impact: ImpactType
): string {
  return `${priority}-${effort}-${impact}`;
}