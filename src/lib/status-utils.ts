import { differenceInDays, differenceInWeeks, differenceInMonths } from "date-fns";

export function calculateDaysInStatus(statusChangedAt: string | null | undefined): number {
  if (!statusChangedAt) return 0;
  
  try {
    const statusDate = new Date(statusChangedAt);
    const now = new Date();
    return differenceInDays(now, statusDate);
  } catch {
    return 0;
  }
}

export function formatDaysInStatus(days: number): string {
  if (days === 0) return "Today";
  if (days === 1) return "1 day";
  if (days < 7) return `${days} days`;
  
  const weeks = Math.floor(days / 7);
  if (weeks === 1) return "1 week";
  if (weeks < 4) return `${weeks} weeks`;
  
  const months = Math.floor(days / 30);
  if (months === 1) return "1 month";
  if (months < 12) return `${months} months`;
  
  const years = Math.floor(days / 365);
  if (years === 1) return "1 year";
  return `${years} years`;
}

export function getStatusDurationColor(days: number): string {
  if (days < 7) return "text-green-600 dark:text-green-400"; // Less than a week - green
  if (days < 30) return "text-yellow-600 dark:text-yellow-400"; // Less than a month - yellow
  if (days < 90) return "text-orange-600 dark:text-orange-400"; // Less than 3 months - orange
  return "text-red-600 dark:text-red-400"; // 3+ months - red
}