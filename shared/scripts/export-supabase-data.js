import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Create exports directory
const exportsDir = path.join(__dirname, '..', 'exports');
if (!fs.existsSync(exportsDir)) {
  fs.mkdirSync(exportsDir, { recursive: true });
}

// Tables to export in dependency order
const tables = [
  'team_members',
  'impact_types', 
  'projects',
  'tasks',
  'sub_tasks',
  'project_integrations',
  'priority_history',
  'priority_rules'
];

async function exportTable(tableName) {
  console.log(`Exporting ${tableName}...`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .order('created_at', { ascending: true });

    if (error) {
      console.error(`Error exporting ${tableName}:`, error);
      return;
    }

    const filePath = path.join(exportsDir, `${tableName}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    console.log(`✓ Exported ${data.length} records from ${tableName} to ${filePath}`);
    
    return data;
  } catch (err) {
    console.error(`Error exporting ${tableName}:`, err);
  }
}

async function exportAllData() {
  console.log('Starting Supabase data export...\n');
  
  const exportedData = {};
  
  for (const table of tables) {
    const data = await exportTable(table);
    if (data) {
      exportedData[table] = data;
    }
  }
  
  // Create a summary file
  const summary = {
    exportDate: new Date().toISOString(),
    tables: Object.keys(exportedData).map(table => ({
      name: table,
      recordCount: exportedData[table].length
    })),
    totalRecords: Object.values(exportedData).reduce((sum, data) => sum + data.length, 0)
  };
  
  fs.writeFileSync(
    path.join(exportsDir, 'export-summary.json'), 
    JSON.stringify(summary, null, 2)
  );
  
  console.log('\n=== Export Summary ===');
  console.log(`Export completed at: ${summary.exportDate}`);
  console.log(`Total tables exported: ${summary.tables.length}`);
  console.log(`Total records exported: ${summary.totalRecords}`);
  
  summary.tables.forEach(table => {
    console.log(`  ${table.name}: ${table.recordCount} records`);
  });
  
  console.log(`\nAll data exported to: ${exportsDir}`);
}

// Run the export
exportAllData().catch(console.error);
