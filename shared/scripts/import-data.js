import { PrismaClient } from '../generated/prisma/index.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

// Import data in dependency order
const importOrder = [
  'team_members',
  'impact_types',
  'priority_rules',
  'projects',
  'tasks',
  'sub_tasks',
  'project_integrations',
  'priority_history'
];

async function importTable(tableName) {
  console.log(`Importing ${tableName}...`);
  
  try {
    const filePath = path.join(__dirname, '..', 'exports', `${tableName}.json`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return 0;
    }
    
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    if (data.length === 0) {
      console.log(`ℹ️  No data to import for ${tableName}`);
      return 0;
    }

    // Transform data based on table
    const transformedData = transformTableData(tableName, data);
    
    let imported = 0;
    
    // Import records one by one to handle any conflicts
    for (const record of transformedData) {
      try {
        await prisma[getModelName(tableName)].create({
          data: record
        });
        imported++;
      } catch (error) {
        console.error(`Error importing record in ${tableName}:`, error.message);
        console.error('Record:', record);
      }
    }
    
    console.log(`✓ Imported ${imported}/${data.length} records into ${tableName}`);
    return imported;
    
  } catch (error) {
    console.error(`Error importing ${tableName}:`, error);
    return 0;
  }
}

function getModelName(tableName) {
  const modelMap = {
    'team_members': 'teamMember',
    'impact_types': 'impactTypes',
    'priority_rules': 'priorityRules',
    'projects': 'project',
    'tasks': 'task',
    'sub_tasks': 'subTask',
    'project_integrations': 'projectIntegration',
    'priority_history': 'priorityHistory'
  };
  return modelMap[tableName] || tableName;
}

function transformTableData(tableName, data) {
  return data.map(record => {
    // Convert date strings to Date objects
    const transformed = { ...record };
    
    // Handle date fields
    const dateFields = ['created_at', 'updated_at', 'completed_at', 'archived_at', 
                       'status_changed_at', 'priority_assigned_at', 'last_reviewed_at',
                       'due_date', 'start_date', 'end_date', 'original_end_date'];
    
    dateFields.forEach(field => {
      if (transformed[field]) {
        transformed[field] = new Date(transformed[field]);
      }
    });
    
    // Handle specific transformations per table
    switch (tableName) {
      case 'projects':
        // Handle enum mappings
        if (transformed.impact_type === 'Bug Fix') {
          transformed.impact_type = 'Bug_Fix';
        } else if (transformed.impact_type === 'R&D') {
          transformed.impact_type = 'R_D';
        }
        break;
    }
    
    return transformed;
  });
}

async function importAllData() {
  console.log('Starting data import to local PostgreSQL...\n');
  
  let totalImported = 0;
  
  for (const table of importOrder) {
    const imported = await importTable(table);
    totalImported += imported;
  }
  
  console.log('\n=== Import Summary ===');
  console.log(`Total records imported: ${totalImported}`);
  console.log('Data import completed successfully!');
}

// Run the import
importAllData()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect();
  });
