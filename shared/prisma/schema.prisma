// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum PriorityLevel {
  P0
  P1
  P2
  P3
  P4
}

enum EffortEstimate {
  S
  M
  L
  XL
}

enum ImpactType {
  Revenue
  Platform
  Bug_Fix @map("Bug Fix")
  R_D     @map("R&D")
}

// Models
model TeamMember {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String
  email       String   @unique
  role        String?
  department  String?
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  led_projects      Project[] @relation("ProjectLead")
  customer_projects Project[] @relation("CustomerLead")
  assigned_tasks    Task[]
  assigned_subtasks SubTask[]

  @@map("team_members")
}

model ImpactTypes {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name         String   @unique
  label        String
  description  String
  color        String
  bg_color     String
  border_color String
  is_active    Boolean  @default(true)
  sort_order   Int      @default(0)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  projects Project[]

  @@map("impact_types")
}

model Project {
  id                   String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                 String
  type                 String // 'internal' or 'external'
  customer_name        String?
  project_lead         String
  customer_lead        String?
  customer_contact     String?
  description          String?
  status               String         @default("not-started") // 'not-started', 'in-progress', 'completed', 'backlog', 'archived'
  company_name         String         @default("")
  start_date           DateTime?      @db.Date
  end_date             DateTime?      @db.Date
  original_end_date    DateTime?      @db.Date
  poc_url              String?
  prd_document_link    String?
  priority_order       Int?
  completed_at         DateTime?
  archived_at          DateTime?
  status_changed_at    DateTime?      @default(now())
  priority_level       PriorityLevel? @default(P3)
  effort_estimate      EffortEstimate? @default(M)
  impact_type          ImpactType?    @default(Platform)
  priority_assigned_at DateTime?      @default(now())
  last_reviewed_at     DateTime?      @default(now())
  auto_escalated       Boolean?       @default(false)
  created_at           DateTime       @default(now())
  updated_at           DateTime       @updatedAt

  // Foreign keys
  project_lead_id   String? @db.Uuid
  customer_lead_id  String? @db.Uuid
  impact_type_id    String? @db.Uuid

  // Relations
  project_lead_member   TeamMember?        @relation("ProjectLead", fields: [project_lead_id], references: [id])
  customer_lead_member  TeamMember?        @relation("CustomerLead", fields: [customer_lead_id], references: [id])
  impact_type_ref       ImpactTypes?       @relation(fields: [impact_type_id], references: [id])
  tasks                 Task[]
  integrations          ProjectIntegration[]
  priority_history      PriorityHistory[]

  @@map("projects")
}

model Task {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id   String    @db.Uuid
  name         String
  description  String?
  assignee     String
  due_date     DateTime? @db.Date
  status       String    @default("to-do") // 'to-do', 'in-progress', 'done'
  completed_at DateTime?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // Foreign keys
  assignee_id String? @db.Uuid

  // Relations
  project         Project      @relation(fields: [project_id], references: [id], onDelete: Cascade)
  assignee_member TeamMember?  @relation(fields: [assignee_id], references: [id])
  sub_tasks       SubTask[]

  @@map("tasks")
}

model SubTask {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  task_id      String    @db.Uuid
  name         String
  description  String?
  assignee     String
  due_date     DateTime? @db.Date
  status       String    @default("to-do") // 'to-do', 'in-progress', 'done'
  completed_at DateTime?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // Foreign keys
  assignee_id String? @db.Uuid

  // Relations
  task            Task         @relation(fields: [task_id], references: [id], onDelete: Cascade)
  assignee_member TeamMember?  @relation(fields: [assignee_id], references: [id])

  @@map("sub_tasks")
}

model ProjectIntegration {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id       String   @db.Uuid
  integration_type String   // 'google_drive' or 'slack_webhook'
  integration_url  String
  integration_data Json?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("project_integrations")
}

model PriorityHistory {
  id            String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id    String        @db.Uuid
  old_priority  PriorityLevel?
  new_priority  PriorityLevel
  changed_by    String?
  change_reason String?
  auto_escalated Boolean      @default(false)
  created_at    DateTime     @default(now())

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("priority_history")
}

model PriorityRules {
  id           String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  from_priority PriorityLevel
  to_priority   PriorityLevel
  max_days      Int
  is_active     Boolean       @default(true)
  created_at    DateTime      @default(now())
  updated_at    DateTime      @updatedAt

  @@map("priority_rules")
}

// Authentication and RBAC Models
model User {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email      String   @unique
  name       String
  avatar_url String?
  google_id  String?  @unique
  is_active  Boolean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relations
  user_roles UserRole[]

  @@map("users")
}

model Role {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String   @unique
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  user_roles       UserRole[]
  role_permissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String   @unique
  resource    String
  action      String
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  role_permissions RolePermission[]

  @@map("permissions")
}

model UserRole {
  user_id     String   @db.Uuid
  role_id     String   @db.Uuid
  assigned_at DateTime @default(now())
  assigned_by String

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  role Role @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@id([user_id, role_id])
  @@map("user_roles")
}

model RolePermission {
  role_id       String @db.Uuid
  permission_id String @db.Uuid

  // Relations
  role       Role       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@id([role_id, permission_id])
  @@map("role_permissions")
}
