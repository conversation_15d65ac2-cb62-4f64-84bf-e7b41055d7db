[{"id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804", "name": "Platform", "label": "Platform", "description": "Core infrastructure, agent framework, future mission", "color": "hsl(var(--blue))", "bg_color": "hsl(var(--blue) / 0.1)", "border_color": "hsl(var(--blue) / 0.3)", "is_active": true, "sort_order": 2, "created_at": "2025-07-17T07:46:04.382726+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00"}, {"id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4", "name": "Bug Fix", "label": "Bug Fix", "description": "Resolving existing issues/technical problems", "color": "hsl(var(--purple))", "bg_color": "hsl(var(--purple) / 0.1)", "border_color": "hsl(var(--purple) / 0.3)", "is_active": true, "sort_order": 3, "created_at": "2025-07-17T07:46:04.382726+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00"}, {"id": "516d941e-fc49-483f-ac76-79b8f944812f", "name": "R&D", "label": "R&D", "description": "Research, experimentation, proof of concepts", "color": "hsl(var(--orange))", "bg_color": "hsl(var(--orange) / 0.1)", "border_color": "hsl(var(--orange) / 0.3)", "is_active": true, "sort_order": 4, "created_at": "2025-07-17T07:46:04.382726+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00"}, {"id": "efd7ddfd-876f-45d7-93cc-4b82e071be47", "name": "<PERSON><PERSON><PERSON>", "label": "Revenue", "description": "Direct customer/sales impact", "color": "hsl(var(--green))", "bg_color": "hsl(var(--green) / 0.1)", "border_color": "hsl(var(--green) / 0.3)", "is_active": true, "sort_order": 1, "created_at": "2025-07-17T07:46:04.382726+00:00", "updated_at": "2025-07-17T08:05:45.747689+00:00"}]