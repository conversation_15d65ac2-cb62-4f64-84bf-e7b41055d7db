// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  google_id?: string;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  created_at: Date;
  updated_at: Date;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface UserRole {
  user_id: string;
  role_id: string;
  assigned_at: Date;
  assigned_by: string;
}

// JWT Token Types
export interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];
  iat: number;
  exp: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Request Types
export interface AuthenticatedRequest extends Request {
  user?: User;
  permissions?: string[];
}

// Google OAuth Types
export interface GoogleProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
  verified_email: boolean;
}

// RBAC Types
export enum ResourceType {
  PROJECT = 'project',
  TASK = 'task',
  USER = 'user',
  TEAM_MEMBER = 'team_member',
  REQUIREMENT = 'requirement',
  ADMIN = 'admin'
}

export enum ActionType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage'
}

export interface PermissionCheck {
  resource: ResourceType;
  action: ActionType;
  resourceId?: string;
}

// Default Roles
export enum DefaultRoles {
  GUEST = 'guest',
  USER = 'user',
  PROJECT_MANAGER = 'project_manager',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}
