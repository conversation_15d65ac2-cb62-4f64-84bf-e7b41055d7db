import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/authService.js';
import { RBACService } from '../services/rbacService.js';
import { ResourceType, ActionType } from '../types/index.js';

// Extend Request interface to include user data
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name: string;
        roles: string[];
        permissions: string[];
      };
    }
  }
}

/**
 * Middleware to authenticate JWT token
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({ error: 'Access token required' });
      return;
    }

    const payload = AuthService.verifyToken(token);
    if (!payload) {
      res.status(403).json({ error: 'Invalid or expired token' });
      return;
    }

    // Get fresh user data with permissions
    const userWithPermissions = await AuthService.getUserWithPermissions(payload.userId);
    if (!userWithPermissions) {
      res.status(403).json({ error: 'User not found or inactive' });
      return;
    }

    // Attach user data to request
    req.user = {
      id: userWithPermissions.user.id,
      email: userWithPermissions.user.email,
      name: userWithPermissions.user.name,
      roles: userWithPermissions.roles,
      permissions: userWithPermissions.permissions
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

/**
 * Middleware to check if user has required permission
 */
export const requirePermission = (
  resource: ResourceType,
  action: ActionType
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const hasPermission = await RBACService.hasPermission(
        req.user.id,
        resource,
        action
      );

      if (!hasPermission) {
        res.status(403).json({ 
          error: 'Insufficient permissions',
          required: `${resource}:${action}`
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

/**
 * Middleware to check if user has any of the required roles
 */
export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const hasRole = req.user.roles.some(role => roles.includes(role));
      if (!hasRole) {
        res.status(403).json({ 
          error: 'Insufficient role permissions',
          required: roles,
          current: req.user.roles
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({ error: 'Role check failed' });
    }
  };
};

/**
 * Optional authentication - doesn't fail if no token provided
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const payload = AuthService.verifyToken(token);
      if (payload) {
        const userWithPermissions = await AuthService.getUserWithPermissions(payload.userId);
        if (userWithPermissions) {
          req.user = {
            id: userWithPermissions.user.id,
            email: userWithPermissions.user.email,
            name: userWithPermissions.user.name,
            roles: userWithPermissions.roles,
            permissions: userWithPermissions.permissions
          };
        }
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    next(); // Continue even if auth fails
  }
};

/**
 * Middleware for guest-by-default access
 * Allows access but with limited permissions if not authenticated
 */
export const guestByDefault = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      // Try to authenticate
      const payload = AuthService.verifyToken(token);
      if (payload) {
        const userWithPermissions = await AuthService.getUserWithPermissions(payload.userId);
        if (userWithPermissions) {
          req.user = {
            id: userWithPermissions.user.id,
            email: userWithPermissions.user.email,
            name: userWithPermissions.user.name,
            roles: userWithPermissions.roles,
            permissions: userWithPermissions.permissions
          };
        }
      }
    }

    // If no user authenticated, set as guest
    if (!req.user) {
      req.user = {
        id: 'guest',
        email: '<EMAIL>',
        name: 'Guest User',
        roles: ['guest'],
        permissions: ['project:read', 'task:read', 'team_member:read']
      };
    }

    next();
  } catch (error) {
    console.error('Guest by default error:', error);
    // Set as guest on error
    req.user = {
      id: 'guest',
      email: '<EMAIL>',
      name: 'Guest User',
      roles: ['guest'],
      permissions: ['project:read', 'task:read', 'team_member:read']
    };
    next();
  }
};
