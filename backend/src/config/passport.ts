import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { AuthService } from '../services/authService.js';

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: '/api/auth/google/callback',
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        console.log('Google OAuth profile received:', {
          id: profile.id,
          email: profile.emails?.[0]?.value,
          name: profile.displayName,
        });

        // Extract user info from Google profile
        const googleUser = {
          id: profile.id,
          email: profile.emails?.[0]?.value || '',
          name: profile.displayName || '',
          picture: profile.photos?.[0]?.value,
          verified_email: profile.emails?.[0]?.verified || false,
        };

        if (!googleUser.email) {
          return done(new Error('Email not provided by Google'));
        }

        // Create or update user
        const user = await AuthService.createOrUpdateUserFromGoogle(googleUser);

        // Get user with permissions
        const userWithPermissions = await AuthService.getUserWithPermissions(user.id);
        if (!userWithPermissions) {
          return done(new Error('Failed to get user permissions'));
        }

        // Generate JWT token pair
        const tokens = AuthService.generateTokenPair(
          userWithPermissions.user,
          userWithPermissions.roles,
          userWithPermissions.permissions
        );

        const result = {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar_url: user.avatar_url,
            roles: userWithPermissions.roles,
            permissions: userWithPermissions.permissions
          },
          tokens: {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken
          }
        };
        
        return done(null, result);
      } catch (error) {
        console.error('Google OAuth strategy error:', error);
        return done(error);
      }
    }
  )
);

// Serialize user for session (required by Passport)
passport.serializeUser((user: any, done) => {
  done(null, user);
});

passport.deserializeUser((user: any, done) => {
  done(null, user);
});

export default passport;
