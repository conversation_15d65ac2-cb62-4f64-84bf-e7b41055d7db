import fs from 'fs';
import path from 'path';
import { PrismaClient } from '../../generated/prisma/index.js';

const prisma = new PrismaClient();

export interface DocumentAnalysisResult {
  requirements: Array<{
    title: string;
    description: string;
    type: 'functional' | 'non-functional' | 'constraint' | 'assumption';
    priority: 'low' | 'medium' | 'high' | 'critical';
    acceptance_criteria?: string;
    source: string;
  }>;
  metadata: {
    documentType: string;
    pageCount?: number;
    wordCount?: number;
    language: string;
    extractedAt: string;
  };
  confidence: number;
}

export interface ConversationAnalysisResult {
  requirements: Array<{
    title: string;
    description: string;
    type: 'functional' | 'non-functional' | 'constraint' | 'assumption';
    priority: 'low' | 'medium' | 'high' | 'critical';
    acceptance_criteria?: string;
    rationale?: string;
  }>;
  suggestions: string[];
  confidence: number;
}

export class AIAnalysisService {
  private static instance: AIAnalysisService;

  public static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * Analyze uploaded document and extract requirements
   */
  async analyzeDocument(
    filePath: string, 
    fileName: string, 
    mimeType: string
  ): Promise<DocumentAnalysisResult> {
    try {
      // For now, we'll simulate AI analysis with intelligent mock data
      // In production, this would integrate with OpenAI, Claude, or other AI services
      
      const fileContent = await this.extractTextFromFile(filePath, mimeType);
      const analysisResult = await this.performDocumentAnalysis(fileContent, fileName);
      
      return analysisResult;
    } catch (error) {
      console.error('Document analysis error:', error);
      throw new Error('Failed to analyze document');
    }
  }

  /**
   * Analyze conversational input and generate requirements
   */
  async analyzeConversation(input: string): Promise<ConversationAnalysisResult> {
    try {
      // Simulate AI conversation analysis
      const requirements = await this.extractRequirementsFromText(input);
      const suggestions = await this.generateSuggestions(input, requirements);
      
      return {
        requirements,
        suggestions,
        confidence: 0.85 + Math.random() * 0.1
      };
    } catch (error) {
      console.error('Conversation analysis error:', error);
      throw new Error('Failed to analyze conversation');
    }
  }

  /**
   * Generate smart templates based on project type
   */
  async generateTemplate(projectType: string, industry?: string): Promise<DocumentAnalysisResult> {
    const templates = {
      'web-application': {
        requirements: [
          {
            title: "User Authentication System",
            description: "Users must be able to register, login, and manage their accounts securely",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "Users can register with email, login with credentials, reset password, and update profile information",
            source: "AI Template: Web Application"
          },
          {
            title: "Responsive Design Requirements",
            description: "The application must be fully responsive and work on all device sizes",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "UI adapts seamlessly to screen sizes from 320px to 1920px width",
            source: "AI Template: Web Application"
          },
          {
            title: "Performance Requirements",
            description: "The application must load quickly and respond to user interactions promptly",
            type: "non-functional" as const,
            priority: "medium" as const,
            acceptance_criteria: "Initial page load under 3 seconds, user interactions respond within 200ms",
            source: "AI Template: Web Application"
          },
          {
            title: "Security Requirements",
            description: "All user data must be protected and transmitted securely",
            type: "non-functional" as const,
            priority: "critical" as const,
            acceptance_criteria: "HTTPS encryption, secure session management, input validation, XSS protection",
            source: "AI Template: Web Application"
          }
        ],
        metadata: {
          documentType: "AI-Generated Template",
          language: "English",
          extractedAt: new Date().toISOString()
        },
        confidence: 0.95
      },
      'api-service': {
        requirements: [
          {
            title: "RESTful API Design",
            description: "API must follow REST principles with proper HTTP methods and status codes",
            type: "functional" as const,
            priority: "high" as const,
            acceptance_criteria: "Proper use of GET, POST, PUT, DELETE methods with appropriate status codes",
            source: "AI Template: API Service"
          },
          {
            title: "API Authentication",
            description: "Secure authentication mechanism for API access",
            type: "functional" as const,
            priority: "critical" as const,
            acceptance_criteria: "JWT or OAuth2 implementation with proper token validation",
            source: "AI Template: API Service"
          },
          {
            title: "Rate Limiting",
            description: "API must implement rate limiting to prevent abuse",
            type: "non-functional" as const,
            priority: "medium" as const,
            acceptance_criteria: "Configurable rate limits per user/IP with proper error responses",
            source: "AI Template: API Service"
          },
          {
            title: "API Documentation",
            description: "Comprehensive API documentation must be available",
            type: "constraint" as const,
            priority: "medium" as const,
            acceptance_criteria: "OpenAPI/Swagger documentation with examples and testing interface",
            source: "AI Template: API Service"
          }
        ],
        metadata: {
          documentType: "AI-Generated Template",
          language: "English",
          extractedAt: new Date().toISOString()
        },
        confidence: 0.95
      }
    };

    return templates[projectType as keyof typeof templates] || templates['web-application'];
  }

  private async extractTextFromFile(filePath: string, mimeType: string): Promise<string> {
    // In production, this would use libraries like:
    // - pdf-parse for PDFs
    // - mammoth for Word documents
    // - xlsx for Excel files
    // - Tesseract.js for image OCR
    
    // For now, simulate text extraction
    const fileName = path.basename(filePath).toLowerCase();
    
    if (fileName.includes('requirement') || fileName.includes('spec')) {
      return `
        User Authentication Requirements:
        - Users must be able to register with email and password
        - System should support password reset functionality
        - Multi-factor authentication should be available
        
        Performance Requirements:
        - System must respond within 200ms for 95% of requests
        - Database queries should be optimized
        - Caching should be implemented for frequently accessed data
        
        Security Requirements:
        - All data must be encrypted in transit and at rest
        - Input validation must be implemented
        - Session management must be secure
      `;
    }
    
    return `Sample document content for analysis from ${fileName}`;
  }

  private async performDocumentAnalysis(content: string, fileName: string): Promise<DocumentAnalysisResult> {
    // Simulate intelligent document analysis
    const requirements = [];
    
    // Look for authentication-related content
    if (content.toLowerCase().includes('authentication') || content.toLowerCase().includes('login')) {
      requirements.push({
        title: "User Authentication System",
        description: "System must provide secure user authentication capabilities",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and manage their accounts securely",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Look for performance-related content
    if (content.toLowerCase().includes('performance') || content.toLowerCase().includes('response time')) {
      requirements.push({
        title: "System Performance Requirements",
        description: "System must meet specified performance criteria",
        type: "non-functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System responds within specified time limits under normal load",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Look for security-related content
    if (content.toLowerCase().includes('security') || content.toLowerCase().includes('encryption')) {
      requirements.push({
        title: "Security Requirements",
        description: "System must implement comprehensive security measures",
        type: "non-functional" as const,
        priority: "critical" as const,
        acceptance_criteria: "Data encryption, secure transmission, and access controls implemented",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    // Default requirement if no specific patterns found
    if (requirements.length === 0) {
      requirements.push({
        title: "General System Requirement",
        description: `Requirement extracted from ${fileName}`,
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the specified functional requirements",
        source: `AI Analysis: ${fileName}`
      });
    }
    
    return {
      requirements,
      metadata: {
        documentType: this.detectDocumentType(fileName),
        wordCount: content.split(' ').length,
        language: "English",
        extractedAt: new Date().toISOString()
      },
      confidence: 0.8 + Math.random() * 0.15
    };
  }

  private async extractRequirementsFromText(input: string): Promise<ConversationAnalysisResult['requirements']> {
    const requirements = [];
    const lowerInput = input.toLowerCase();
    
    // Analyze input for different types of requirements
    if (lowerInput.includes('user') && (lowerInput.includes('register') || lowerInput.includes('login') || lowerInput.includes('account'))) {
      requirements.push({
        title: "User Management System",
        description: "Users should be able to create and manage their accounts",
        type: "functional" as const,
        priority: "high" as const,
        acceptance_criteria: "Users can register, login, and update their profiles",
        rationale: "Essential for user identification and personalization"
      });
    }
    
    if (lowerInput.includes('secure') || lowerInput.includes('security') || lowerInput.includes('protect')) {
      requirements.push({
        title: "Security Requirements",
        description: "System must implement comprehensive security measures",
        type: "non-functional" as const,
        priority: "critical" as const,
        acceptance_criteria: "Data protection, secure authentication, and access controls",
        rationale: "Critical for protecting user data and system integrity"
      });
    }
    
    if (lowerInput.includes('fast') || lowerInput.includes('performance') || lowerInput.includes('speed')) {
      requirements.push({
        title: "Performance Requirements",
        description: "System must provide fast and responsive user experience",
        type: "non-functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "Quick response times and efficient resource usage",
        rationale: "Important for user satisfaction and system scalability"
      });
    }
    
    // Default requirement based on input
    if (requirements.length === 0) {
      requirements.push({
        title: "Core System Functionality",
        description: "System must provide the core functionality as described",
        type: "functional" as const,
        priority: "medium" as const,
        acceptance_criteria: "System meets the basic functional requirements",
        rationale: "Based on user input analysis"
      });
    }
    
    return requirements;
  }

  private async generateSuggestions(input: string, requirements: any[]): Promise<string[]> {
    const suggestions = [
      "Consider adding more specific acceptance criteria",
      "Think about edge cases and error handling",
      "Define measurable success metrics",
      "Consider user experience implications",
      "Plan for scalability and future growth"
    ];
    
    // Add context-specific suggestions
    if (input.toLowerCase().includes('mobile')) {
      suggestions.push("Consider mobile-specific requirements and constraints");
    }
    
    if (input.toLowerCase().includes('api')) {
      suggestions.push("Define API versioning and backward compatibility requirements");
    }
    
    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  private detectDocumentType(fileName: string): string {
    const name = fileName.toLowerCase();
    
    if (name.includes('requirement') || name.includes('req')) return "Requirements Document";
    if (name.includes('spec') || name.includes('specification')) return "Technical Specification";
    if (name.includes('design')) return "Design Document";
    if (name.includes('user') && name.includes('story')) return "User Stories";
    if (name.includes('test')) return "Test Documentation";
    
    return "General Document";
  }
}

export const aiAnalysisService = AIAnalysisService.getInstance();
