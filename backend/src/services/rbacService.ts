import { PrismaClient } from '../../generated/prisma/index.js';
import { ResourceType, ActionType, PermissionCheck, DefaultRoles } from '../types/index.js';

const prisma = new PrismaClient();

export class RBACService {
  /**
   * Check if user has permission for a specific action on a resource
   */
  static async hasPermission(
    userId: string,
    resource: ResourceType,
    action: ActionType,
    resourceId?: string
  ): Promise<boolean> {
    try {
      // Get user with roles and permissions
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          user_roles: {
            include: {
              role: {
                include: {
                  role_permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!user || !user.is_active) return false;

      // Check if user has the specific permission
      const hasPermission = user.user_roles.some(userRole =>
        userRole.role.role_permissions.some(rolePermission => {
          const permission = rolePermission.permission;
          return (
            permission.resource === resource &&
            (permission.action === action || permission.action === ActionType.MANAGE)
          );
        })
      );

      return hasPermission;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Check multiple permissions at once
   */
  static async hasPermissions(
    userId: string,
    permissions: PermissionCheck[]
  ): Promise<boolean[]> {
    const results = await Promise.all(
      permissions.map(perm =>
        this.hasPermission(userId, perm.resource, perm.action, perm.resourceId)
      )
    );
    return results;
  }

  /**
   * Get all permissions for a user
   */
  static async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          user_roles: {
            include: {
              role: {
                include: {
                  role_permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!user) return [];

      const permissions = user.user_roles
        .flatMap(ur => ur.role.role_permissions.map(rp => rp.permission.name))
        .filter((permission, index, self) => self.indexOf(permission) === index);

      return permissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * Assign role to user
   */
  static async assignRole(userId: string, roleName: string, assignedBy: string): Promise<boolean> {
    try {
      const role = await prisma.role.findUnique({
        where: { name: roleName }
      });

      if (!role) {
        throw new Error(`Role '${roleName}' not found`);
      }

      // Check if user already has this role
      const existingUserRole = await prisma.userRole.findUnique({
        where: {
          user_id_role_id: {
            user_id: userId,
            role_id: role.id
          }
        }
      });

      if (existingUserRole) {
        return true; // Already has role
      }

      await prisma.userRole.create({
        data: {
          user_id: userId,
          role_id: role.id,
          assigned_by: assignedBy
        }
      });

      return true;
    } catch (error) {
      console.error('Error assigning role:', error);
      return false;
    }
  }

  /**
   * Remove role from user
   */
  static async removeRole(userId: string, roleName: string): Promise<boolean> {
    try {
      const role = await prisma.role.findUnique({
        where: { name: roleName }
      });

      if (!role) return false;

      await prisma.userRole.delete({
        where: {
          user_id_role_id: {
            user_id: userId,
            role_id: role.id
          }
        }
      });

      return true;
    } catch (error) {
      console.error('Error removing role:', error);
      return false;
    }
  }

  /**
   * Initialize default roles and permissions
   */
  static async initializeDefaultRoles(): Promise<void> {
    try {
      // Define default permissions
      const defaultPermissions = [
        // Project permissions
        { name: 'project:read', resource: ResourceType.PROJECT, action: ActionType.READ },
        { name: 'project:create', resource: ResourceType.PROJECT, action: ActionType.CREATE },
        { name: 'project:update', resource: ResourceType.PROJECT, action: ActionType.UPDATE },
        { name: 'project:delete', resource: ResourceType.PROJECT, action: ActionType.DELETE },
        { name: 'project:manage', resource: ResourceType.PROJECT, action: ActionType.MANAGE },

        // Task permissions
        { name: 'task:read', resource: ResourceType.TASK, action: ActionType.READ },
        { name: 'task:create', resource: ResourceType.TASK, action: ActionType.CREATE },
        { name: 'task:update', resource: ResourceType.TASK, action: ActionType.UPDATE },
        { name: 'task:delete', resource: ResourceType.TASK, action: ActionType.DELETE },
        { name: 'task:manage', resource: ResourceType.TASK, action: ActionType.MANAGE },

        // User permissions
        { name: 'user:read', resource: ResourceType.USER, action: ActionType.READ },
        { name: 'user:update', resource: ResourceType.USER, action: ActionType.UPDATE },
        { name: 'user:manage', resource: ResourceType.USER, action: ActionType.MANAGE },

        // Team member permissions
        { name: 'team_member:read', resource: ResourceType.TEAM_MEMBER, action: ActionType.READ },
        { name: 'team_member:create', resource: ResourceType.TEAM_MEMBER, action: ActionType.CREATE },
        { name: 'team_member:update', resource: ResourceType.TEAM_MEMBER, action: ActionType.UPDATE },
        { name: 'team_member:delete', resource: ResourceType.TEAM_MEMBER, action: ActionType.DELETE },

        // Admin permissions
        { name: 'admin:manage', resource: ResourceType.ADMIN, action: ActionType.MANAGE }
      ];

      // Create permissions
      for (const perm of defaultPermissions) {
        await prisma.permission.upsert({
          where: { name: perm.name },
          update: {},
          create: {
            name: perm.name,
            resource: perm.resource,
            action: perm.action,
            description: `${perm.action} access to ${perm.resource}`
          }
        });
      }

      // Define default roles with their permissions
      const defaultRoles = [
        {
          name: DefaultRoles.GUEST,
          description: 'Guest user with read-only access',
          permissions: ['project:read', 'task:read', 'team_member:read']
        },
        {
          name: DefaultRoles.USER,
          description: 'Regular user with basic project access',
          permissions: ['project:read', 'project:create', 'task:read', 'task:create', 'task:update', 'team_member:read']
        },
        {
          name: DefaultRoles.PROJECT_MANAGER,
          description: 'Project manager with full project and task management',
          permissions: ['project:manage', 'task:manage', 'team_member:read', 'team_member:create', 'team_member:update']
        },
        {
          name: DefaultRoles.ADMIN,
          description: 'Administrator with full system access',
          permissions: ['project:manage', 'task:manage', 'user:manage', 'team_member:manage']
        },
        {
          name: DefaultRoles.SUPER_ADMIN,
          description: 'Super administrator with all permissions',
          permissions: ['admin:manage', 'project:manage', 'task:manage', 'user:manage', 'team_member:manage']
        }
      ];

      // Create roles and assign permissions
      for (const roleData of defaultRoles) {
        const role = await prisma.role.upsert({
          where: { name: roleData.name },
          update: { description: roleData.description },
          create: {
            name: roleData.name,
            description: roleData.description
          }
        });

        // Assign permissions to role
        for (const permissionName of roleData.permissions) {
          const permission = await prisma.permission.findUnique({
            where: { name: permissionName }
          });

          if (permission) {
            await prisma.rolePermission.upsert({
              where: {
                role_id_permission_id: {
                  role_id: role.id,
                  permission_id: permission.id
                }
              },
              update: {},
              create: {
                role_id: role.id,
                permission_id: permission.id
              }
            });
          }
        }
      }

      console.log('Default roles and permissions initialized successfully');
    } catch (error) {
      console.error('Error initializing default roles:', error);
      throw error;
    }
  }
}
