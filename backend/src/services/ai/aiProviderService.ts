import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Anthropic from '@anthropic-ai/sdk';
import axios from 'axios';

// AI Provider Types
export type AIProvider = 'openai' | 'gemini' | 'claude' | 'kimi' | 'openrouter';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  provider: AIProvider;
  model: string;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
}

export interface AIProviderConfig {
  apiKey: string;
  model: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Multi-AI Provider Service
 * Supports OpenAI, Gemini, Claude, Kimi K2, and OpenRouter
 */
export class AIProviderService {
  private openaiClient?: OpenAI;
  private geminiClient?: GoogleGenerativeAI;
  private claudeClient?: Anthropic;
  
  private defaultProvider: AIProvider;
  private fallbackProviders: AIProvider[];

  constructor() {
    this.defaultProvider = (process.env.DEFAULT_AI_PROVIDER as AIProvider) || 'openai';
    this.fallbackProviders = process.env.AI_FALLBACK_PROVIDERS?.split(',') as AIProvider[] || [];
    
    this.initializeClients();
  }

  private initializeClients() {
    // Initialize OpenAI
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key-here') {
      this.openaiClient = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL
      });
      console.log('✅ OpenAI client initialized');
    }

    // Initialize Gemini
    if (process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'your-gemini-api-key-here') {
      this.geminiClient = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      console.log('✅ Gemini client initialized');
    }

    // Initialize Claude
    if (process.env.CLAUDE_API_KEY && process.env.CLAUDE_API_KEY !== 'your-claude-api-key-here') {
      this.claudeClient = new Anthropic({
        apiKey: process.env.CLAUDE_API_KEY,
        baseURL: process.env.CLAUDE_BASE_URL
      });
      console.log('✅ Claude client initialized');
    }

    console.log(`🤖 AI Provider Service initialized with default: ${this.defaultProvider}`);
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): AIProvider[] {
    const available: AIProvider[] = [];
    
    if (this.openaiClient) available.push('openai');
    if (this.geminiClient) available.push('gemini');
    if (this.claudeClient) available.push('claude');
    if (this.isKimiAvailable()) available.push('kimi');
    if (this.isOpenRouterAvailable()) available.push('openrouter');
    
    return available;
  }

  /**
   * Check if a provider is available
   */
  isProviderAvailable(provider: AIProvider): boolean {
    return this.getAvailableProviders().includes(provider);
  }

  /**
   * Generate AI response with automatic fallback
   */
  async generateResponse(
    messages: AIMessage[],
    options: {
      provider?: AIProvider;
      maxTokens?: number;
      temperature?: number;
      systemPrompt?: string;
    } = {}
  ): Promise<AIResponse> {
    const targetProvider = options.provider || this.defaultProvider;
    const providers = [targetProvider, ...this.fallbackProviders].filter(p => 
      this.isProviderAvailable(p)
    );

    if (providers.length === 0) {
      throw new Error('No AI providers available. Please configure at least one AI provider.');
    }

    // Add system prompt if provided
    if (options.systemPrompt) {
      messages = [
        { role: 'system', content: options.systemPrompt },
        ...messages.filter(m => m.role !== 'system')
      ];
    }

    let lastError: Error | null = null;

    for (const provider of providers) {
      try {
        console.log(`🤖 Attempting to use ${provider} provider...`);
        const response = await this.callProvider(provider, messages, options);
        console.log(`✅ Successfully used ${provider} provider`);
        return response;
      } catch (error) {
        console.error(`❌ ${provider} provider failed:`, error);
        lastError = error as Error;
        continue;
      }
    }

    throw new Error(`All AI providers failed. Last error: ${lastError?.message}`);
  }

  /**
   * Call specific AI provider
   */
  private async callProvider(
    provider: AIProvider,
    messages: AIMessage[],
    options: any
  ): Promise<AIResponse> {
    switch (provider) {
      case 'openai':
        return this.callOpenAI(messages, options);
      case 'gemini':
        return this.callGemini(messages, options);
      case 'claude':
        return this.callClaude(messages, options);
      case 'kimi':
        return this.callKimi(messages, options);
      case 'openrouter':
        return this.callOpenRouter(messages, options);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * OpenAI API call
   */
  private async callOpenAI(messages: AIMessage[], options: any): Promise<AIResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    const response = await this.openaiClient.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: messages as any,
      max_tokens: options.maxTokens || 2000,
      temperature: options.temperature || 0.3
    });

    return {
      content: response.choices[0]?.message?.content || '',
      provider: 'openai',
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      usage: {
        prompt_tokens: response.usage?.prompt_tokens,
        completion_tokens: response.usage?.completion_tokens,
        total_tokens: response.usage?.total_tokens
      }
    };
  }

  /**
   * Google Gemini API call
   */
  private async callGemini(messages: AIMessage[], options: any): Promise<AIResponse> {
    if (!this.geminiClient) {
      throw new Error('Gemini client not initialized');
    }

    const model = this.geminiClient.getGenerativeModel({ 
      model: process.env.GEMINI_MODEL || 'gemini-1.5-flash' 
    });

    // Convert messages to Gemini format
    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n\n');

    const result = await model.generateContent(prompt);
    const response = await result.response;

    return {
      content: response.text(),
      provider: 'gemini',
      model: process.env.GEMINI_MODEL || 'gemini-1.5-flash'
    };
  }

  /**
   * Anthropic Claude API call
   */
  private async callClaude(messages: AIMessage[], options: any): Promise<AIResponse> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    // Separate system message from other messages
    const systemMessage = messages.find(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');

    const response = await this.claudeClient.messages.create({
      model: process.env.CLAUDE_MODEL || 'claude-3-haiku-20240307',
      max_tokens: options.maxTokens || 2000,
      temperature: options.temperature || 0.3,
      system: systemMessage?.content,
      messages: conversationMessages.map(m => ({
        role: m.role as 'user' | 'assistant',
        content: m.content
      }))
    });

    return {
      content: response.content[0]?.type === 'text' ? response.content[0].text : '',
      provider: 'claude',
      model: process.env.CLAUDE_MODEL || 'claude-3-haiku-20240307',
      usage: {
        prompt_tokens: response.usage.input_tokens,
        completion_tokens: response.usage.output_tokens,
        total_tokens: response.usage.input_tokens + response.usage.output_tokens
      }
    };
  }

  /**
   * Kimi K2 API call (Moonshot AI)
   */
  private async callKimi(messages: AIMessage[], options: any): Promise<AIResponse> {
    if (!this.isKimiAvailable()) {
      throw new Error('Kimi API key not configured');
    }

    const response = await axios.post(
      `${process.env.KIMI_BASE_URL}/chat/completions`,
      {
        model: process.env.KIMI_MODEL || 'moonshot-v1-8k',
        messages: messages,
        max_tokens: options.maxTokens || 2000,
        temperature: options.temperature || 0.3
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.KIMI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      provider: 'kimi',
      model: process.env.KIMI_MODEL || 'moonshot-v1-8k',
      usage: response.data.usage
    };
  }

  /**
   * OpenRouter API call
   */
  private async callOpenRouter(messages: AIMessage[], options: any): Promise<AIResponse> {
    if (!this.isOpenRouterAvailable()) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await axios.post(
      `${process.env.OPENROUTER_BASE_URL}/chat/completions`,
      {
        model: process.env.OPENROUTER_MODEL || 'anthropic/claude-3-haiku',
        messages: messages,
        max_tokens: options.maxTokens || 2000,
        temperature: options.temperature || 0.3
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://your-app.com',
          'X-Title': 'PRD Requirements System'
        }
      }
    );

    return {
      content: response.data.choices[0]?.message?.content || '',
      provider: 'openrouter',
      model: process.env.OPENROUTER_MODEL || 'anthropic/claude-3-haiku',
      usage: response.data.usage
    };
  }

  /**
   * Check if Kimi is available
   */
  private isKimiAvailable(): boolean {
    return !!(process.env.KIMI_API_KEY && 
             process.env.KIMI_API_KEY !== 'your-kimi-api-key-here');
  }

  /**
   * Check if OpenRouter is available
   */
  private isOpenRouterAvailable(): boolean {
    return !!(process.env.OPENROUTER_API_KEY && 
             process.env.OPENROUTER_API_KEY !== 'your-openrouter-api-key-here');
  }

  /**
   * Get provider status
   */
  getProviderStatus(): Record<AIProvider, { available: boolean; model?: string }> {
    return {
      openai: {
        available: !!this.openaiClient,
        model: process.env.OPENAI_MODEL
      },
      gemini: {
        available: !!this.geminiClient,
        model: process.env.GEMINI_MODEL
      },
      claude: {
        available: !!this.claudeClient,
        model: process.env.CLAUDE_MODEL
      },
      kimi: {
        available: this.isKimiAvailable(),
        model: process.env.KIMI_MODEL
      },
      openrouter: {
        available: this.isOpenRouterAvailable(),
        model: process.env.OPENROUTER_MODEL
      }
    };
  }
}

// Export singleton instance
export const aiProviderService = new AIProviderService();
