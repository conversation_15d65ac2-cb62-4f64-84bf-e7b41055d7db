import jwt from 'jsonwebtoken';
import { PrismaClient } from '../../generated/prisma/index.js';
import { User, JWTPayload, GoogleProfile } from '../types/index.js';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

export class AuthService {
  /**
   * Generate JWT token for user
   */
  static generateToken(user: User, roles: string[], permissions: string[]): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      roles,
      permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    };

    // Don't use expiresIn option since we're setting exp manually
    return jwt.sign(payload, JWT_SECRET);
  }

  /**
   * Verify JWT token
   */
  static verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as JWTPayload;
    } catch (error) {
      console.error('Token verification failed:', error);
      return null;
    }
  }

  /**
   * Create or update user from Google OAuth profile
   */
  static async createOrUpdateUserFromGoogle(profile: GoogleProfile): Promise<User> {
    try {
      // Check if user exists by Google ID
      let user = await prisma.user.findUnique({
        where: { google_id: profile.id }
      });

      if (user) {
        // Update existing user
        user = await prisma.user.update({
          where: { id: user.id },
          data: {
            name: profile.name,
            email: profile.email,
            avatar_url: profile.picture,
            updated_at: new Date()
          }
        });
      } else {
        // Check if user exists by email
        const existingUser = await prisma.user.findUnique({
          where: { email: profile.email }
        });

        if (existingUser) {
          // Link Google account to existing user
          user = await prisma.user.update({
            where: { id: existingUser.id },
            data: {
              google_id: profile.id,
              name: profile.name,
              avatar_url: profile.picture,
              updated_at: new Date()
            }
          });
        } else {
          // Create new user
          user = await prisma.user.create({
            data: {
              email: profile.email,
              name: profile.name,
              avatar_url: profile.picture,
              google_id: profile.id,
              is_active: true
            }
          });

          // Assign default guest role to new user
          await this.assignDefaultRole(user.id);
        }
      }

      return user;
    } catch (error) {
      console.error('Error creating/updating user from Google:', error);
      throw new Error('Failed to process Google authentication');
    }
  }

  /**
   * Assign default guest role to new user
   */
  static async assignDefaultRole(userId: string): Promise<void> {
    try {
      // Find or create guest role
      let guestRole = await prisma.role.findUnique({
        where: { name: 'guest' }
      });

      if (!guestRole) {
        guestRole = await prisma.role.create({
          data: {
            name: 'guest',
            description: 'Default role for new users with limited permissions'
          }
        });
      }

      // Assign role to user
      await prisma.userRole.create({
        data: {
          user_id: userId,
          role_id: guestRole.id,
          assigned_by: 'system'
        }
      });
    } catch (error) {
      console.error('Error assigning default role:', error);
      throw new Error('Failed to assign default role');
    }
  }

  /**
   * Get user with roles and permissions
   */
  static async getUserWithPermissions(userId: string): Promise<{
    user: User;
    roles: string[];
    permissions: string[];
  } | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          user_roles: {
            include: {
              role: {
                include: {
                  role_permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!user) return null;

      const roles = user.user_roles.map(ur => ur.role.name);
      const permissions = user.user_roles
        .flatMap(ur => ur.role.role_permissions.map(rp => rp.permission.name))
        .filter((permission, index, self) => self.indexOf(permission) === index); // Remove duplicates

      return {
        user: user as User,
        roles,
        permissions
      };
    } catch (error) {
      console.error('Error getting user with permissions:', error);
      return null;
    }
  }

  /**
   * Refresh user token
   */
  static async refreshToken(userId: string): Promise<string | null> {
    try {
      const userWithPermissions = await this.getUserWithPermissions(userId);
      if (!userWithPermissions) return null;

      return this.generateToken(
        userWithPermissions.user,
        userWithPermissions.roles,
        userWithPermissions.permissions
      );
    } catch (error) {
      console.error('Error refreshing token:', error);
      return null;
    }
  }
}
