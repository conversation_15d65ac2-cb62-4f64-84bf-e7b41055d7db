import jwt from 'jsonwebtoken';
import { PrismaClient } from '../../generated/prisma/index.js';
import { User, JWTPayload, GoogleProfile } from '../types/index.js';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

export class AuthService {
  /**
   * Generate JWT access token for user (short-lived)
   */
  static generateAccessToken(user: User, roles: string[], permissions: string[]): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      roles,
      permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (15 * 60) // 15 minutes
    };

    return jwt.sign(payload, JWT_SECRET);
  }

  /**
   * Generate JWT refresh token for user (long-lived)
   */
  static generateRefreshToken(user: User): string {
    const payload = {
      userId: user.id,
      email: user.email,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
    };

    return jwt.sign(payload, JWT_REFRESH_SECRET);
  }

  /**
   * Generate both access and refresh tokens
   */
  static generateTokenPair(user: User, roles: string[], permissions: string[]): { accessToken: string, refreshToken: string } {
    return {
      accessToken: this.generateAccessToken(user, roles, permissions),
      refreshToken: this.generateRefreshToken(user)
    };
  }

  /**
   * Legacy method for backward compatibility
   */
  static generateToken(user: User, roles: string[], permissions: string[]): string {
    return this.generateAccessToken(user, roles, permissions);
  }

  /**
   * Verify JWT access token
   */
  static verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as JWTPayload;
    } catch (error) {
      console.error('Access token verification failed:', error);
      return null;
    }
  }

  /**
   * Verify JWT refresh token
   */
  static verifyRefreshToken(token: string): { userId: string, email: string, type: string } | null {
    try {
      const payload = jwt.verify(token, JWT_REFRESH_SECRET) as any;
      if (payload.type !== 'refresh') {
        console.error('Invalid token type:', payload.type);
        return null;
      }
      return payload;
    } catch (error) {
      console.error('Refresh token verification failed:', error);
      return null;
    }
  }

  /**
   * Create or update user from Google OAuth profile
   */
  static async createOrUpdateUserFromGoogle(profile: GoogleProfile): Promise<User> {
    try {
      // Check if user exists by Google ID
      let user = await prisma.user.findUnique({
        where: { google_id: profile.id }
      });

      if (user) {
        // Update existing user
        user = await prisma.user.update({
          where: { id: user.id },
          data: {
            name: profile.name,
            email: profile.email,
            avatar_url: profile.picture,
            updated_at: new Date()
          }
        });
      } else {
        // Check if user exists by email
        const existingUser = await prisma.user.findUnique({
          where: { email: profile.email }
        });

        if (existingUser) {
          // Link Google account to existing user
          user = await prisma.user.update({
            where: { id: existingUser.id },
            data: {
              google_id: profile.id,
              name: profile.name,
              avatar_url: profile.picture,
              updated_at: new Date()
            }
          });
        } else {
          // Create new user
          user = await prisma.user.create({
            data: {
              email: profile.email,
              name: profile.name,
              avatar_url: profile.picture,
              google_id: profile.id,
              is_active: true
            }
          });

          // Assign default role to new user (<NAME_EMAIL>, guest for others)
          await this.assignDefaultRole(user.id, profile.email);
        }
      }

      return user;
    } catch (error) {
      console.error('Error creating/updating user from Google:', error);
      throw new Error('Failed to process Google authentication');
    }
  }

  /**
   * Assign default role to new user (<NAME_EMAIL>, guest for others)
   */
  static async assignDefaultRole(userId: string, email?: string): Promise<void> {
    try {
      // Determine role based on email
      const isKavi = email === '<EMAIL>';
      const roleName = isKavi ? 'admin' : 'guest';

      console.log(`Assigning ${roleName} role to new user: ${email}`);

      // Find or create the role
      let role = await prisma.role.findUnique({
        where: { name: roleName }
      });

      if (!role) {
        const roleDescription = isKavi
          ? 'Administrator with full system access'
          : 'Default role for new users with limited permissions';

        role = await prisma.role.create({
          data: {
            name: roleName,
            description: roleDescription
          }
        });
      }

      // Assign role to user
      await prisma.userRole.create({
        data: {
          user_id: userId,
          role_id: role.id,
          assigned_by: 'system'
        }
      });

      console.log(`✅ Successfully assigned ${roleName} role to user: ${email}`);
    } catch (error) {
      console.error('Error assigning default role:', error);
      throw new Error('Failed to assign default role');
    }
  }

  /**
   * Get user with roles and permissions
   */
  static async getUserWithPermissions(userId: string): Promise<{
    user: User;
    roles: string[];
    permissions: string[];
  } | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          user_roles: {
            include: {
              role: {
                include: {
                  role_permissions: {
                    include: {
                      permission: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!user) return null;

      const roles = user.user_roles.map(ur => ur.role.name);
      const permissions = user.user_roles
        .flatMap(ur => ur.role.role_permissions.map(rp => rp.permission.name))
        .filter((permission, index, self) => self.indexOf(permission) === index); // Remove duplicates

      return {
        user: user as User,
        roles,
        permissions
      };
    } catch (error) {
      console.error('Error getting user with permissions:', error);
      return null;
    }
  }

  /**
   * Refresh user tokens using refresh token
   */
  static async refreshTokens(refreshToken: string): Promise<{ accessToken: string, refreshToken: string } | null> {
    try {
      // Verify refresh token
      const payload = this.verifyRefreshToken(refreshToken);
      if (!payload) return null;

      // Get user with fresh permissions
      const userWithPermissions = await this.getUserWithPermissions(payload.userId);
      if (!userWithPermissions) return null;

      // Generate new token pair
      return this.generateTokenPair(
        userWithPermissions.user,
        userWithPermissions.roles,
        userWithPermissions.permissions
      );
    } catch (error) {
      console.error('Error refreshing tokens:', error);
      return null;
    }
  }

  /**
   * Legacy refresh method for backward compatibility
   */
  static async refreshToken(userId: string): Promise<string | null> {
    try {
      const userWithPermissions = await this.getUserWithPermissions(userId);
      if (!userWithPermissions) return null;

      return this.generateAccessToken(
        userWithPermissions.user,
        userWithPermissions.roles,
        userWithPermissions.permissions
      );
    } catch (error) {
      console.error('Error refreshing token:', error);
      return null;
    }
  }
}
