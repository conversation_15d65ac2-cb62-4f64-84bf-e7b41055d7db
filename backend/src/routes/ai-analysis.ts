import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { guestByDefault } from '../middleware/auth.js';
import { aiAnalysisService } from '../services/aiAnalysisService.js';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/documents';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'));
    }
  }
});

/**
 * POST /api/ai-analysis/document
 * Upload and analyze document for requirements extraction
 */
router.post('/document',
  guestByDefault,
  upload.single('document'),
  async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const { projectId } = req.body;
      
      if (!projectId) {
        return res.status(400).json({ error: 'Project ID is required' });
      }

      // Analyze the uploaded document
      const analysisResult = await aiAnalysisService.analyzeDocument(
        req.file.path,
        req.file.originalname,
        req.file.mimetype
      );

      // Store file metadata (in production, you might want to store this in database)
      const fileMetadata = {
        id: req.file.filename,
        originalName: req.file.originalname,
        path: req.file.path,
        size: req.file.size,
        mimeType: req.file.mimetype,
        uploadedAt: new Date().toISOString(),
        projectId
      };

      res.json({
        success: true,
        data: {
          file: fileMetadata,
          analysis: analysisResult
        }
      });
    } catch (error: any) {
      console.error('Document analysis error:', error);
      
      // Clean up uploaded file on error
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({ 
        error: error.message || 'Failed to analyze document' 
      });
    }
  }
);

/**
 * POST /api/ai-analysis/conversation
 * Analyze conversational input and generate requirements
 */
router.post('/conversation',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { input, projectId } = req.body;
      
      if (!input || !input.trim()) {
        return res.status(400).json({ error: 'Input text is required' });
      }
      
      if (!projectId) {
        return res.status(400).json({ error: 'Project ID is required' });
      }

      // Analyze the conversational input
      const analysisResult = await aiAnalysisService.analyzeConversation(input.trim());

      res.json({
        success: true,
        data: analysisResult
      });
    } catch (error: any) {
      console.error('Conversation analysis error:', error);
      res.status(500).json({ 
        error: error.message || 'Failed to analyze conversation' 
      });
    }
  }
);

/**
 * POST /api/ai-analysis/template
 * Generate smart requirements template based on project type
 */
router.post('/template',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { projectType, industry, projectId } = req.body;
      
      if (!projectType) {
        return res.status(400).json({ error: 'Project type is required' });
      }
      
      if (!projectId) {
        return res.status(400).json({ error: 'Project ID is required' });
      }

      // Generate template based on project type
      const templateResult = await aiAnalysisService.generateTemplate(projectType, industry);

      res.json({
        success: true,
        data: templateResult
      });
    } catch (error: any) {
      console.error('Template generation error:', error);
      res.status(500).json({ 
        error: error.message || 'Failed to generate template' 
      });
    }
  }
);

/**
 * GET /api/ai-analysis/files/:projectId
 * Get uploaded files for a project
 */
router.get('/files/:projectId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      
      // In production, this would query the database for file metadata
      // For now, we'll return a simple response
      const uploadDir = 'uploads/documents';
      const files: any[] = [];
      
      if (fs.existsSync(uploadDir)) {
        const fileList = fs.readdirSync(uploadDir);
        fileList.forEach(filename => {
          const filePath = path.join(uploadDir, filename);
          const stats = fs.statSync(filePath);
          
          files.push({
            id: filename,
            name: filename,
            size: stats.size,
            uploadedAt: stats.birthtime.toISOString(),
            projectId
          });
        });
      }

      res.json({
        success: true,
        data: files
      });
    } catch (error: any) {
      console.error('Get files error:', error);
      res.status(500).json({ 
        error: error.message || 'Failed to get files' 
      });
    }
  }
);

/**
 * DELETE /api/ai-analysis/files/:fileId
 * Delete uploaded file
 */
router.delete('/files/:fileId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { fileId } = req.params;
      const filePath = path.join('uploads/documents', fileId);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      res.json({
        success: true,
        message: 'File deleted successfully'
      });
    } catch (error: any) {
      console.error('Delete file error:', error);
      res.status(500).json({ 
        error: error.message || 'Failed to delete file' 
      });
    }
  }
);

/**
 * GET /api/ai-analysis/health
 * Health check for AI analysis service
 */
router.get('/health',
  async (req: Request, res: Response) => {
    res.json({
      success: true,
      service: 'AI Analysis Service',
      status: 'operational',
      timestamp: new Date().toISOString()
    });
  }
);

export default router;
