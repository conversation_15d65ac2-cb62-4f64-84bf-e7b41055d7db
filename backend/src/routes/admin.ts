import express, { Request, Response } from 'express';
import { PrismaClient } from '../../generated/prisma/index.js';
import { authenticateToken, requirePermission, requireRole } from '../middleware/auth.js';
import { RBACService } from '../services/rbacService.js';
import { ResourceType, ActionType } from '../types/index.js';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * GET /api/admin/users
 * Get all users (admin only)
 */
router.get('/users', 
  authenticateToken,
  requirePermission(ResourceType.USER, ActionType.READ),
  async (req: Request, res: Response) => {
    try {
      const { page = 1, limit = 20, search } = req.query;
      const skip = (Number(page) - 1) * Number(limit);

      const where = search ? {
        OR: [
          { name: { contains: String(search), mode: 'insensitive' as const } },
          { email: { contains: String(search), mode: 'insensitive' as const } }
        ]
      } : {};

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: Number(limit),
          include: {
            user_roles: {
              include: {
                role: true
              }
            }
          },
          orderBy: { created_at: 'desc' }
        }),
        prisma.user.count({ where })
      ]);

      const usersWithRoles = users.map(user => ({
        ...user,
        roles: user.user_roles.map(ur => ur.role.name)
      }));

      res.json({
        success: true,
        data: usersWithRoles,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error: any) {
      console.error('Get users error:', error);
      res.status(500).json({ error: 'Failed to get users' });
    }
  }
);

/**
 * PUT /api/admin/users/:id/roles
 * Update user roles (admin only)
 */
router.put('/users/:id/roles',
  authenticateToken,
  requirePermission(ResourceType.USER, ActionType.MANAGE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { roles } = req.body;

      if (!Array.isArray(roles)) {
        return res.status(400).json({ error: 'Roles must be an array' });
      }

      // Remove all existing roles
      await prisma.userRole.deleteMany({
        where: { user_id: id }
      });

      // Add new roles
      for (const roleName of roles) {
        await RBACService.assignRole(id, roleName, req.user!.id);
      }

      res.json({
        success: true,
        message: 'User roles updated successfully'
      });
    } catch (error: any) {
      console.error('Update user roles error:', error);
      res.status(500).json({ error: 'Failed to update user roles' });
    }
  }
);

/**
 * PUT /api/admin/users/:id/status
 * Update user active status (admin only)
 */
router.put('/users/:id/status',
  authenticateToken,
  requirePermission(ResourceType.USER, ActionType.MANAGE),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { is_active } = req.body;

      if (typeof is_active !== 'boolean') {
        return res.status(400).json({ error: 'is_active must be a boolean' });
      }

      await prisma.user.update({
        where: { id },
        data: { is_active }
      });

      res.json({
        success: true,
        message: `User ${is_active ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error: any) {
      console.error('Update user status error:', error);
      res.status(500).json({ error: 'Failed to update user status' });
    }
  }
);

/**
 * GET /api/admin/roles
 * Get all roles (admin only)
 */
router.get('/roles',
  authenticateToken,
  requireRole(['admin', 'super_admin']),
  async (req: Request, res: Response) => {
    try {
      const roles = await prisma.role.findMany({
        include: {
          role_permissions: {
            include: {
              permission: true
            }
          },
          _count: {
            select: {
              user_roles: true
            }
          }
        },
        orderBy: { name: 'asc' }
      });

      const rolesWithPermissions = roles.map(role => ({
        ...role,
        permissions: role.role_permissions.map(rp => rp.permission.name),
        user_count: role._count.user_roles
      }));

      res.json({
        success: true,
        data: rolesWithPermissions
      });
    } catch (error: any) {
      console.error('Get roles error:', error);
      res.status(500).json({ error: 'Failed to get roles' });
    }
  }
);

/**
 * GET /api/admin/permissions
 * Get all permissions (admin only)
 */
router.get('/permissions',
  authenticateToken,
  requireRole(['admin', 'super_admin']),
  async (req: Request, res: Response) => {
    try {
      const permissions = await prisma.permission.findMany({
        orderBy: [
          { resource: 'asc' },
          { action: 'asc' }
        ]
      });

      // Group permissions by resource
      const groupedPermissions = permissions.reduce((acc, permission) => {
        if (!acc[permission.resource]) {
          acc[permission.resource] = [];
        }
        acc[permission.resource].push(permission);
        return acc;
      }, {} as Record<string, typeof permissions>);

      res.json({
        success: true,
        data: {
          permissions,
          grouped: groupedPermissions
        }
      });
    } catch (error: any) {
      console.error('Get permissions error:', error);
      res.status(500).json({ error: 'Failed to get permissions' });
    }
  }
);

/**
 * POST /api/admin/initialize
 * Initialize default roles and permissions (super admin only)
 */
router.post('/initialize',
  authenticateToken,
  requireRole(['super_admin']),
  async (req: Request, res: Response) => {
    try {
      await RBACService.initializeDefaultRoles();

      res.json({
        success: true,
        message: 'Default roles and permissions initialized successfully'
      });
    } catch (error: any) {
      console.error('Initialize RBAC error:', error);
      res.status(500).json({ error: 'Failed to initialize RBAC system' });
    }
  }
);

/**
 * POST /api/admin/promote-kavi
 * Promote <EMAIL> to admin (temporary endpoint)
 */
router.post('/promote-kavi', async (req: Request, res: Response) => {
  try {
    // Find Kavi's user account
    const kaviUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!kaviUser) {
      return res.status(404).json({ error: 'Kavi user not found' });
    }

    // Remove existing roles
    await prisma.userRole.deleteMany({
      where: { user_id: kaviUser.id }
    });

    // Assign admin role
    const success = await RBACService.assignRole(kaviUser.id, 'admin', 'system');

    if (success) {
      console.log('✅ <NAME_EMAIL> to admin');
      res.json({
        success: true,
        message: 'Kavi promoted to admin successfully'
      });
    } else {
      res.status(500).json({ error: 'Failed to assign admin role' });
    }
  } catch (error: any) {
    console.error('Promote Kavi error:', error);
    res.status(500).json({ error: 'Failed to promote Kavi to admin' });
  }
});

export default router;
