import express, { Request, Response } from 'express';
import { PrismaClient } from '../../generated/prisma/index.js';
import { authenticateToken, requirePermission, guestByDefault } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * GET /api/requirements/:projectId
 * Get all requirements for a project
 */
router.get('/:projectId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const { type, status, priority } = req.query;

      // Build filter conditions
      const where: any = { project_id: projectId };
      
      if (type && type !== 'all') {
        where.type = type;
      }
      
      if (status && status !== 'all') {
        where.status = status;
      }
      
      if (priority && priority !== 'all') {
        where.priority = priority;
      }

      const requirements = await prisma.requirement.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          tasks: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          test_cases: {
            select: {
              id: true,
              title: true,
              status: true
            }
          }
        },
        orderBy: [
          { order_index: 'asc' },
          { created_at: 'desc' }
        ]
      });

      res.json({
        success: true,
        data: requirements
      });
    } catch (error: any) {
      console.error('Get requirements error:', error);
      res.status(500).json({ error: 'Failed to fetch requirements' });
    }
  }
);

/**
 * POST /api/requirements/:projectId
 * Create a new requirement
 */
router.post('/:projectId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const userId = (req as any).user?.id || null;
      
      const {
        title,
        description,
        type = 'functional',
        priority = 'medium',
        status = 'draft',
        source,
        rationale,
        acceptance_criteria,
        order_index
      } = req.body;

      // Validate required fields
      if (!title) {
        return res.status(400).json({ error: 'Title is required' });
      }

      // Verify project exists and user has access
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({ error: 'Project not found' });
      }

      // Get next order index if not provided
      let finalOrderIndex = order_index;
      if (!finalOrderIndex) {
        const lastRequirement = await prisma.requirement.findFirst({
          where: { project_id: projectId },
          orderBy: { order_index: 'desc' }
        });
        finalOrderIndex = (lastRequirement?.order_index || 0) + 1;
      }

      const requirement = await prisma.requirement.create({
        data: {
          project_id: projectId,
          title,
          description,
          type,
          priority,
          status,
          source,
          rationale,
          acceptance_criteria,
          order_index: finalOrderIndex,
          created_by: userId
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          tasks: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          test_cases: {
            select: {
              id: true,
              title: true,
              status: true
            }
          }
        }
      });

      res.status(201).json({
        success: true,
        data: requirement
      });
    } catch (error: any) {
      console.error('Create requirement error:', error);
      res.status(500).json({ error: 'Failed to create requirement' });
    }
  }
);

/**
 * PUT /api/requirements/:requirementId
 * Update a requirement
 */
router.put('/:requirementId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { requirementId } = req.params;
      
      const {
        title,
        description,
        type,
        priority,
        status,
        source,
        rationale,
        acceptance_criteria,
        order_index
      } = req.body;

      // Verify requirement exists
      const existingRequirement = await prisma.requirement.findUnique({
        where: { id: requirementId }
      });

      if (!existingRequirement) {
        return res.status(404).json({ error: 'Requirement not found' });
      }

      const requirement = await prisma.requirement.update({
        where: { id: requirementId },
        data: {
          ...(title !== undefined && { title }),
          ...(description !== undefined && { description }),
          ...(type !== undefined && { type }),
          ...(priority !== undefined && { priority }),
          ...(status !== undefined && { status }),
          ...(source !== undefined && { source }),
          ...(rationale !== undefined && { rationale }),
          ...(acceptance_criteria !== undefined && { acceptance_criteria }),
          ...(order_index !== undefined && { order_index })
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          tasks: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          test_cases: {
            select: {
              id: true,
              title: true,
              status: true
            }
          }
        }
      });

      res.json({
        success: true,
        data: requirement
      });
    } catch (error: any) {
      console.error('Update requirement error:', error);
      res.status(500).json({ error: 'Failed to update requirement' });
    }
  }
);

/**
 * DELETE /api/requirements/:requirementId
 * Delete a requirement
 */
router.delete('/:requirementId',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { requirementId } = req.params;

      // Verify requirement exists
      const existingRequirement = await prisma.requirement.findUnique({
        where: { id: requirementId }
      });

      if (!existingRequirement) {
        return res.status(404).json({ error: 'Requirement not found' });
      }

      await prisma.requirement.delete({
        where: { id: requirementId }
      });

      res.json({
        success: true,
        message: 'Requirement deleted successfully'
      });
    } catch (error: any) {
      console.error('Delete requirement error:', error);
      res.status(500).json({ error: 'Failed to delete requirement' });
    }
  }
);

/**
 * POST /api/requirements/:requirementId/reorder
 * Reorder requirements
 */
router.post('/:requirementId/reorder',
  guestByDefault,
  async (req: Request, res: Response) => {
    try {
      const { requirementId } = req.params;
      const { newOrderIndex } = req.body;

      if (typeof newOrderIndex !== 'number') {
        return res.status(400).json({ error: 'newOrderIndex must be a number' });
      }

      await prisma.requirement.update({
        where: { id: requirementId },
        data: { order_index: newOrderIndex }
      });

      res.json({
        success: true,
        message: 'Requirement reordered successfully'
      });
    } catch (error: any) {
      console.error('Reorder requirement error:', error);
      res.status(500).json({ error: 'Failed to reorder requirement' });
    }
  }
);

export default router;
