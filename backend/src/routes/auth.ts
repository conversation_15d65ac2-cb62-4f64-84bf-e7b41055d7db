import express, { Request, Response } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { AuthService } from '../services/authService.js';
import { RBACService } from '../services/rbacService.js';
import { authenticateToken, optionalAuth } from '../middleware/auth.js';
import { GoogleProfile } from '../types/index.js';

const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

const router = express.Router();

/**
 * POST /api/auth/google
 * Authenticate user with Google OAuth token
 */
router.post('/google', async (req: Request, res: Response) => {
  try {
    const { token, profile } = req.body;

    console.log('Google auth request received:', {
      hasToken: !!token,
      profile: profile ? {
        id: profile.id,
        email: profile.email,
        name: profile.name
      } : null
    });

    if (!token) {
      return res.status(400).json({ error: 'Google token is required' });
    }

    let verifiedPayload;
    try {
      // Verify the Google token
      const ticket = await googleClient.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID,
      });
      verifiedPayload = ticket.getPayload();
      console.log('Google token verified successfully:', {
        sub: verifiedPayload?.sub,
        email: verifiedPayload?.email,
        name: verifiedPayload?.name
      });
    } catch (verifyError) {
      console.error('Google token verification failed:', verifyError);
      return res.status(401).json({ error: 'Invalid Google token' });
    }

    if (!verifiedPayload || !verifiedPayload.email) {
      return res.status(400).json({ error: 'Invalid token payload' });
    }

    // Use verified payload instead of client-provided profile
    const googleProfile: GoogleProfile = {
      id: verifiedPayload.sub!,
      email: verifiedPayload.email!,
      name: verifiedPayload.name || verifiedPayload.email!,
      picture: verifiedPayload.picture,
      verified_email: verifiedPayload.email_verified !== false
    };

    console.log('Using verified Google profile:', googleProfile);

    // Create or update user
    const user = await AuthService.createOrUpdateUserFromGoogle(googleProfile);

    // Get user with permissions
    const userWithPermissions = await AuthService.getUserWithPermissions(user.id);
    if (!userWithPermissions) {
      return res.status(500).json({ error: 'Failed to get user permissions' });
    }

    // Generate JWT token
    const jwtToken = AuthService.generateToken(
      userWithPermissions.user,
      userWithPermissions.roles,
      userWithPermissions.permissions
    );

    res.json({
      success: true,
      data: {
        token: jwtToken,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar_url: user.avatar_url,
          roles: userWithPermissions.roles,
          permissions: userWithPermissions.permissions
        }
      }
    });
  } catch (error: any) {
    console.error('Google auth error:', error);
    res.status(500).json({ error: error.message || 'Authentication failed' });
  }
});

/**
 * POST /api/auth/refresh
 * Refresh JWT token
 */
router.post('/refresh', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const newToken = await AuthService.refreshToken(req.user.id);
    if (!newToken) {
      return res.status(500).json({ error: 'Failed to refresh token' });
    }

    res.json({
      success: true,
      data: { token: newToken }
    });
  } catch (error: any) {
    console.error('Token refresh error:', error);
    res.status(500).json({ error: 'Token refresh failed' });
  }
});

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const userWithPermissions = await AuthService.getUserWithPermissions(req.user.id);
    if (!userWithPermissions) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: userWithPermissions.user.id,
          email: userWithPermissions.user.email,
          name: userWithPermissions.user.name,
          avatar_url: userWithPermissions.user.avatar_url,
          roles: userWithPermissions.roles,
          permissions: userWithPermissions.permissions,
          is_active: userWithPermissions.user.is_active,
          created_at: userWithPermissions.user.created_at
        }
      }
    });
  } catch (error: any) {
    console.error('Get user profile error:', error);
    res.status(500).json({ error: 'Failed to get user profile' });
  }
});

/**
 * POST /api/auth/logout
 * Logout user (client-side token removal)
 */
router.post('/logout', optionalAuth, (req: Request, res: Response) => {
  // Since we're using stateless JWT tokens, logout is handled client-side
  // This endpoint exists for consistency and future server-side session management
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

/**
 * GET /api/auth/permissions
 * Get current user's permissions
 */
router.get('/permissions', authenticateToken, async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const permissions = await RBACService.getUserPermissions(req.user.id);

    res.json({
      success: true,
      data: {
        permissions,
        roles: req.user.roles
      }
    });
  } catch (error: any) {
    console.error('Get permissions error:', error);
    res.status(500).json({ error: 'Failed to get permissions' });
  }
});

export default router;
