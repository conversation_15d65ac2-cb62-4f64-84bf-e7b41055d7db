import express from 'express';
import { aiProviderService } from '../services/ai/aiProviderService.js';

const router = express.Router();

/**
 * GET /api/ai-providers/status
 * Get status of all AI providers
 */
router.get('/status', async (req, res) => {
  try {
    const status = aiProviderService.getProviderStatus();
    const availableProviders = aiProviderService.getAvailableProviders();
    
    const response = {
      default_provider: process.env.DEFAULT_AI_PROVIDER || 'openai',
      fallback_providers: process.env.AI_FALLBACK_PROVIDERS?.split(',') || [],
      available_providers: availableProviders,
      provider_status: status,
      total_available: availableProviders.length,
      recommendations: []
    };

    // Add recommendations
    if (availableProviders.length === 0) {
      response.recommendations.push('Configure at least one AI provider to enable AI features');
    } else if (availableProviders.length === 1) {
      response.recommendations.push('Consider configuring multiple AI providers for better reliability');
    }

    // Check if default provider is available
    const defaultProvider = process.env.DEFAULT_AI_PROVIDER as any;
    if (defaultProvider && !availableProviders.includes(defaultProvider)) {
      response.recommendations.push(`Default provider '${defaultProvider}' is not available. Configure it or change DEFAULT_AI_PROVIDER`);
    }

    res.json(response);
  } catch (error) {
    console.error('AI provider status error:', error);
    res.status(500).json({ error: 'Failed to get AI provider status' });
  }
});

/**
 * POST /api/ai-providers/test
 * Test AI provider functionality
 */
router.post('/test', async (req, res) => {
  try {
    const { provider, message = 'Hello, this is a test message. Please respond with "Test successful".' } = req.body;

    console.log(`🧪 Testing AI provider: ${provider || 'default'}`);

    const response = await aiProviderService.generateResponse(
      [{ role: 'user', content: message }],
      { 
        provider: provider || undefined,
        maxTokens: 100,
        temperature: 0.1
      }
    );

    res.json({
      success: true,
      provider: response.provider,
      model: response.model,
      response: response.content,
      usage: response.usage,
      test_message: message
    });

  } catch (error: any) {
    console.error('AI provider test error:', error);
    res.status(500).json({ 
      success: false,
      error: error.message,
      provider: req.body.provider || 'default'
    });
  }
});

/**
 * GET /api/ai-providers/models
 * Get available models for each provider
 */
router.get('/models', async (req, res) => {
  try {
    const models = {
      openai: [
        { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model' },
        { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Fast and efficient' },
        { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'High performance' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Cost effective' }
      ],
      gemini: [
        { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Most capable Gemini model' },
        { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient' },
        { id: 'gemini-pro', name: 'Gemini Pro', description: 'Balanced performance' }
      ],
      claude: [
        { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most capable Claude model' },
        { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance' },
        { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fast and cost-effective' }
      ],
      kimi: [
        { id: 'moonshot-v1-8k', name: 'Moonshot v1 8K', description: '8K context window' },
        { id: 'moonshot-v1-32k', name: 'Moonshot v1 32K', description: '32K context window' },
        { id: 'moonshot-v1-128k', name: 'Moonshot v1 128K', description: '128K context window' }
      ],
      openrouter: [
        { id: 'anthropic/claude-3-opus', name: 'Claude 3 Opus (OpenRouter)', description: 'Via OpenRouter' },
        { id: 'anthropic/claude-3-sonnet', name: 'Claude 3 Sonnet (OpenRouter)', description: 'Via OpenRouter' },
        { id: 'anthropic/claude-3-haiku', name: 'Claude 3 Haiku (OpenRouter)', description: 'Via OpenRouter' },
        { id: 'openai/gpt-4o', name: 'GPT-4o (OpenRouter)', description: 'Via OpenRouter' },
        { id: 'google/gemini-pro-1.5', name: 'Gemini Pro 1.5 (OpenRouter)', description: 'Via OpenRouter' }
      ]
    };

    res.json(models);
  } catch (error) {
    console.error('Get models error:', error);
    res.status(500).json({ error: 'Failed to get available models' });
  }
});

/**
 * POST /api/ai-providers/configure
 * Update AI provider configuration (for admin use)
 */
router.post('/configure', async (req, res) => {
  try {
    const { default_provider, fallback_providers } = req.body;

    // This would typically update environment variables or database configuration
    // For now, we'll just return the current configuration
    
    res.json({
      message: 'Configuration updated successfully',
      current_config: {
        default_provider: process.env.DEFAULT_AI_PROVIDER,
        fallback_providers: process.env.AI_FALLBACK_PROVIDERS?.split(',') || []
      },
      note: 'Restart server to apply environment variable changes'
    });

  } catch (error) {
    console.error('Configure AI providers error:', error);
    res.status(500).json({ error: 'Failed to configure AI providers' });
  }
});

export default router;
