
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.12.0
 * Query Engine version: 8047c96bbd92db98a2abc7c9323ce77c02c89dbc
 */
Prisma.prismaVersion = {
  client: "6.12.0",
  engine: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.TeamMemberScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  role: 'role',
  department: 'department',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ImpactTypesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  label: 'label',
  description: 'description',
  color: 'color',
  bg_color: 'bg_color',
  border_color: 'border_color',
  is_active: 'is_active',
  sort_order: 'sort_order',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  customer_name: 'customer_name',
  project_lead: 'project_lead',
  customer_lead: 'customer_lead',
  customer_contact: 'customer_contact',
  description: 'description',
  status: 'status',
  company_name: 'company_name',
  start_date: 'start_date',
  end_date: 'end_date',
  original_end_date: 'original_end_date',
  poc_url: 'poc_url',
  prd_document_link: 'prd_document_link',
  priority_order: 'priority_order',
  completed_at: 'completed_at',
  archived_at: 'archived_at',
  status_changed_at: 'status_changed_at',
  priority_level: 'priority_level',
  effort_estimate: 'effort_estimate',
  impact_type: 'impact_type',
  priority_assigned_at: 'priority_assigned_at',
  last_reviewed_at: 'last_reviewed_at',
  auto_escalated: 'auto_escalated',
  created_at: 'created_at',
  updated_at: 'updated_at',
  template_id: 'template_id',
  current_phase: 'current_phase',
  methodology: 'methodology',
  project_health: 'project_health',
  completion_percentage: 'completion_percentage',
  budget_allocated: 'budget_allocated',
  budget_spent: 'budget_spent',
  team_size: 'team_size',
  risk_level: 'risk_level',
  project_lead_id: 'project_lead_id',
  customer_lead_id: 'customer_lead_id',
  impact_type_id: 'impact_type_id'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  name: 'name',
  description: 'description',
  assignee: 'assignee',
  due_date: 'due_date',
  status: 'status',
  completed_at: 'completed_at',
  created_at: 'created_at',
  updated_at: 'updated_at',
  phase_id: 'phase_id',
  milestone_id: 'milestone_id',
  requirement_id: 'requirement_id',
  task_type: 'task_type',
  priority: 'priority',
  effort_hours: 'effort_hours',
  actual_hours: 'actual_hours',
  progress: 'progress',
  assignee_id: 'assignee_id'
};

exports.Prisma.SubTaskScalarFieldEnum = {
  id: 'id',
  task_id: 'task_id',
  name: 'name',
  description: 'description',
  assignee: 'assignee',
  due_date: 'due_date',
  status: 'status',
  completed_at: 'completed_at',
  created_at: 'created_at',
  updated_at: 'updated_at',
  assignee_id: 'assignee_id'
};

exports.Prisma.ProjectIntegrationScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  integration_type: 'integration_type',
  integration_url: 'integration_url',
  integration_data: 'integration_data',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PriorityHistoryScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  old_priority: 'old_priority',
  new_priority: 'new_priority',
  changed_by: 'changed_by',
  change_reason: 'change_reason',
  auto_escalated: 'auto_escalated',
  created_at: 'created_at'
};

exports.Prisma.PriorityRulesScalarFieldEnum = {
  id: 'id',
  from_priority: 'from_priority',
  to_priority: 'to_priority',
  max_days: 'max_days',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  avatar_url: 'avatar_url',
  google_id: 'google_id',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  resource: 'resource',
  action: 'action',
  description: 'description',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  user_id: 'user_id',
  role_id: 'role_id',
  assigned_at: 'assigned_at',
  assigned_by: 'assigned_by'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  role_id: 'role_id',
  permission_id: 'permission_id'
};

exports.Prisma.ProjectTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  methodology: 'methodology',
  category: 'category',
  is_active: 'is_active',
  created_by: 'created_by',
  created_at: 'created_at',
  updated_at: 'updated_at',
  default_phases: 'default_phases',
  default_milestones: 'default_milestones',
  default_requirements: 'default_requirements',
  default_tasks: 'default_tasks'
};

exports.Prisma.ProjectPhaseScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  name: 'name',
  description: 'description',
  status: 'status',
  order_index: 'order_index',
  start_date: 'start_date',
  end_date: 'end_date',
  progress: 'progress',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ProjectMilestoneScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  phase_id: 'phase_id',
  name: 'name',
  description: 'description',
  due_date: 'due_date',
  completed_at: 'completed_at',
  status: 'status',
  priority: 'priority',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RequirementScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  description: 'description',
  type: 'type',
  priority: 'priority',
  status: 'status',
  source: 'source',
  rationale: 'rationale',
  acceptance_criteria: 'acceptance_criteria',
  order_index: 'order_index',
  created_by: 'created_by',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.TestCaseScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  requirement_id: 'requirement_id',
  title: 'title',
  description: 'description',
  preconditions: 'preconditions',
  test_steps: 'test_steps',
  expected_result: 'expected_result',
  actual_result: 'actual_result',
  status: 'status',
  priority: 'priority',
  test_type: 'test_type',
  executed_by: 'executed_by',
  executed_at: 'executed_at',
  created_by: 'created_by',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.DeploymentScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  version: 'version',
  environment: 'environment',
  status: 'status',
  deployed_by: 'deployed_by',
  deployed_at: 'deployed_at',
  rollback_at: 'rollback_at',
  notes: 'notes',
  commit_hash: 'commit_hash',
  build_number: 'build_number',
  deployment_url: 'deployment_url',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ProjectDocumentScalarFieldEnum = {
  id: 'id',
  project_id: 'project_id',
  title: 'title',
  content: 'content',
  document_type: 'document_type',
  file_url: 'file_url',
  file_name: 'file_name',
  file_size: 'file_size',
  mime_type: 'mime_type',
  version: 'version',
  is_active: 'is_active',
  created_by: 'created_by',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.PriorityLevel = exports.$Enums.PriorityLevel = {
  P0: 'P0',
  P1: 'P1',
  P2: 'P2',
  P3: 'P3',
  P4: 'P4'
};

exports.EffortEstimate = exports.$Enums.EffortEstimate = {
  S: 'S',
  M: 'M',
  L: 'L',
  XL: 'XL'
};

exports.ImpactType = exports.$Enums.ImpactType = {
  Revenue: 'Revenue',
  Platform: 'Platform',
  Bug_Fix: 'Bug_Fix',
  R_D: 'R_D'
};

exports.Prisma.ModelName = {
  TeamMember: 'TeamMember',
  ImpactTypes: 'ImpactTypes',
  Project: 'Project',
  Task: 'Task',
  SubTask: 'SubTask',
  ProjectIntegration: 'ProjectIntegration',
  PriorityHistory: 'PriorityHistory',
  PriorityRules: 'PriorityRules',
  User: 'User',
  Role: 'Role',
  Permission: 'Permission',
  UserRole: 'UserRole',
  RolePermission: 'RolePermission',
  ProjectTemplate: 'ProjectTemplate',
  ProjectPhase: 'ProjectPhase',
  ProjectMilestone: 'ProjectMilestone',
  Requirement: 'Requirement',
  TestCase: 'TestCase',
  Deployment: 'Deployment',
  ProjectDocument: 'ProjectDocument'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
