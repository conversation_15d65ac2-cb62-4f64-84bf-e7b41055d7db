{"name": "orbit-api-server", "version": "1.0.0", "description": "API server for Orbit project management", "main": "index.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "tsx src/server.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/multer": "^2.0.0", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "openai": "^5.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "react-dropzone": "^14.3.8"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.14", "nodemon": "^3.1.10", "prisma": "^6.12.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}