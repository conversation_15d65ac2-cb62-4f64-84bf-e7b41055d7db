import { PrismaClient } from '../generated/prisma/index.js';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function restoreProductionData() {
  try {
    console.log('🚀 Restoring production data...');

    // Read the exported data files
    const dataDir = '../shared/exports';
    
    // Import team members first (they're referenced by projects)
    console.log('📋 Importing team members...');
    const teamMembersData = JSON.parse(fs.readFileSync(path.join(dataDir, 'team_members.json'), 'utf8'));
    for (const member of teamMembersData) {
      try {
        await prisma.teamMember.create({
          data: member
        });
        console.log(`✅ Imported team member: ${member.name}`);
      } catch (error) {
        console.log(`⚠️  Skipping existing team member: ${member.name}`);
      }
    }

    // Import impact types
    console.log('📋 Importing impact types...');
    const impactTypesData = JSON.parse(fs.readFileSync(path.join(dataDir, 'impact_types.json'), 'utf8'));
    for (const impactType of impactTypesData) {
      try {
        await prisma.impactTypes.create({
          data: impactType
        });
        console.log(`✅ Imported impact type: ${impactType.name}`);
      } catch (error) {
        console.log(`⚠️  Skipping existing impact type: ${impactType.name}`);
      }
    }

    // Import priority rules
    console.log('📋 Importing priority rules...');
    const priorityRulesData = JSON.parse(fs.readFileSync(path.join(dataDir, 'priority_rules.json'), 'utf8'));
    for (const rule of priorityRulesData) {
      try {
        await prisma.priorityRules.create({
          data: rule
        });
        console.log(`✅ Imported priority rule: ${rule.from_priority} -> ${rule.to_priority}`);
      } catch (error) {
        console.log(`⚠️  Skipping existing priority rule`);
      }
    }

    // Import projects
    console.log('📋 Importing projects...');
    const projectsData = JSON.parse(fs.readFileSync(path.join(dataDir, 'projects.json'), 'utf8'));
    for (const project of projectsData) {
      try {
        // Check if project already exists
        const existing = await prisma.project.findUnique({
          where: { id: project.id }
        });

        if (!existing) {
          // Fix date formats for Prisma
          const projectData = { ...project };
          if (projectData.start_date && typeof projectData.start_date === 'string' && projectData.start_date.length === 10) {
            projectData.start_date = new Date(projectData.start_date + 'T00:00:00.000Z');
          }
          if (projectData.end_date && typeof projectData.end_date === 'string' && projectData.end_date.length === 10) {
            projectData.end_date = new Date(projectData.end_date + 'T00:00:00.000Z');
          }
          if (projectData.original_end_date && typeof projectData.original_end_date === 'string' && projectData.original_end_date.length === 10) {
            projectData.original_end_date = new Date(projectData.original_end_date + 'T00:00:00.000Z');
          }

          await prisma.project.create({
            data: projectData
          });
          console.log(`✅ Imported project: ${project.name}`);
        } else {
          console.log(`⚠️  Project already exists: ${project.name}`);
        }
      } catch (error) {
        console.log(`❌ Error importing project ${project.name}:`, error.message);
      }
    }

    // Import tasks
    console.log('📋 Importing tasks...');
    const tasksData = JSON.parse(fs.readFileSync(path.join(dataDir, 'tasks.json'), 'utf8'));
    for (const task of tasksData) {
      try {
        // Check if task already exists
        const existing = await prisma.task.findUnique({
          where: { id: task.id }
        });

        if (!existing) {
          // Fix date formats for Prisma
          const taskData = { ...task };
          if (taskData.due_date && typeof taskData.due_date === 'string' && taskData.due_date.length === 10) {
            taskData.due_date = new Date(taskData.due_date + 'T00:00:00.000Z');
          }

          await prisma.task.create({
            data: taskData
          });
          console.log(`✅ Imported task: ${task.name}`);
        } else {
          console.log(`⚠️  Task already exists: ${task.name}`);
        }
      } catch (error) {
        console.log(`❌ Error importing task ${task.name}:`, error.message);
      }
    }

    // Import priority history
    console.log('📋 Importing priority history...');
    const priorityHistoryData = JSON.parse(fs.readFileSync(path.join(dataDir, 'priority_history.json'), 'utf8'));
    for (const history of priorityHistoryData) {
      try {
        await prisma.priorityHistory.create({
          data: history
        });
      } catch (error) {
        // Skip silently for priority history duplicates
      }
    }

    console.log('✅ Production data restored successfully!');

    // Verify the data
    const projectCount = await prisma.project.count();
    const taskCount = await prisma.task.count();
    const teamMemberCount = await prisma.teamMember.count();
    const impactTypeCount = await prisma.impactTypes.count();

    console.log(`📊 Production data summary:`);
    console.log(`   - Projects: ${projectCount}`);
    console.log(`   - Tasks: ${taskCount}`);
    console.log(`   - Team Members: ${teamMemberCount}`);
    console.log(`   - Impact Types: ${impactTypeCount}`);

    console.log('🎉 All your production projects and data have been restored!');

  } catch (error) {
    console.error('❌ Error restoring production data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

restoreProductionData();
