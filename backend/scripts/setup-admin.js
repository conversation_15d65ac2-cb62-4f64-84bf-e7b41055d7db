import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function setupAdmin() {
  try {
    console.log('🚀 Setting up admin user...');

    // First, initialize default roles and permissions
    console.log('📋 Creating default roles and permissions...');

    // Define default permissions
    const defaultPermissions = [
      // Project permissions
      { name: 'project:read', resource: 'project', action: 'read', description: 'Read project information' },
      { name: 'project:create', resource: 'project', action: 'create', description: 'Create new projects' },
      { name: 'project:update', resource: 'project', action: 'update', description: 'Update project information' },
      { name: 'project:delete', resource: 'project', action: 'delete', description: 'Delete projects' },
      { name: 'project:manage', resource: 'project', action: 'manage', description: 'Full project management' },

      // Task permissions
      { name: 'task:read', resource: 'task', action: 'read', description: 'Read task information' },
      { name: 'task:create', resource: 'task', action: 'create', description: 'Create new tasks' },
      { name: 'task:update', resource: 'task', action: 'update', description: 'Update task information' },
      { name: 'task:delete', resource: 'task', action: 'delete', description: 'Delete tasks' },
      { name: 'task:manage', resource: 'task', action: 'manage', description: 'Full task management' },

      // User permissions
      { name: 'user:read', resource: 'user', action: 'read', description: 'Read user information' },
      { name: 'user:create', resource: 'user', action: 'create', description: 'Create new users' },
      { name: 'user:update', resource: 'user', action: 'update', description: 'Update user information' },
      { name: 'user:delete', resource: 'user', action: 'delete', description: 'Delete users' },
      { name: 'user:manage', resource: 'user', action: 'manage', description: 'Full user management' },

      // Team member permissions
      { name: 'team_member:read', resource: 'team_member', action: 'read', description: 'Read team member information' },
      { name: 'team_member:create', resource: 'team_member', action: 'create', description: 'Add team members' },
      { name: 'team_member:update', resource: 'team_member', action: 'update', description: 'Update team member information' },
      { name: 'team_member:delete', resource: 'team_member', action: 'delete', description: 'Remove team members' },
      { name: 'team_member:manage', resource: 'team_member', action: 'manage', description: 'Full team member management' },

      // Admin permissions
      { name: 'admin:manage', resource: 'admin', action: 'manage', description: 'Manage admin functions' }
    ];

    // Create permissions
    for (const permData of defaultPermissions) {
      await prisma.permission.upsert({
        where: { name: permData.name },
        update: permData,
        create: permData
      });
    }

    // Define default roles with their permissions
    const defaultRoles = [
      {
        name: 'guest',
        description: 'Guest user with read-only access',
        permissions: ['project:read', 'task:read', 'team_member:read']
      },
      {
        name: 'user',
        description: 'Regular user with basic project access',
        permissions: ['project:read', 'project:create', 'task:read', 'task:create', 'task:update', 'team_member:read']
      },
      {
        name: 'project_manager',
        description: 'Project manager with full project and task management',
        permissions: ['project:manage', 'task:manage', 'team_member:read', 'team_member:create', 'team_member:update']
      },
      {
        name: 'admin',
        description: 'Administrator with full system access',
        permissions: ['project:manage', 'task:manage', 'user:manage', 'team_member:manage']
      },
      {
        name: 'super_admin',
        description: 'Super administrator with all permissions',
        permissions: ['admin:manage', 'project:manage', 'task:manage', 'user:manage', 'team_member:manage']
      }
    ];

    // Create roles and assign permissions
    for (const roleData of defaultRoles) {
      const role = await prisma.role.upsert({
        where: { name: roleData.name },
        update: { description: roleData.description },
        create: {
          name: roleData.name,
          description: roleData.description
        }
      });

      // Clear existing permissions for this role
      await prisma.rolePermission.deleteMany({
        where: { role_id: role.id }
      });

      // Assign permissions to role
      for (const permissionName of roleData.permissions) {
        const permission = await prisma.permission.findUnique({
          where: { name: permissionName }
        });

        if (permission) {
          await prisma.rolePermission.create({
            data: {
              role_id: role.id,
              permission_id: permission.id
            }
          });
        }
      }
    }

    console.log('✅ Default roles and permissions created');

    // Now promote Kavi to admin
    console.log('👑 Promoting <EMAIL> to admin...');

    const kaviUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!kaviUser) {
      console.log('❌ Kavi user not found. Please login first.');
      return;
    }

    // Remove existing roles
    await prisma.userRole.deleteMany({
      where: { user_id: kaviUser.id }
    });

    // Get admin role
    const adminRole = await prisma.role.findUnique({
      where: { name: 'admin' }
    });

    if (adminRole) {
      // Assign admin role
      await prisma.userRole.create({
        data: {
          user_id: kaviUser.id,
          role_id: adminRole.id,
          assigned_by: 'system'
        }
      });

      console.log('✅ <NAME_EMAIL> to admin');
    } else {
      console.log('❌ Admin role not found');
    }

    console.log('🎉 Setup complete!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupAdmin();
