import { PrismaClient } from '../generated/prisma/index.js';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function restoreSampleData() {
  try {
    console.log('🚀 Restoring sample data...');

    // Read the exported data files
    const dataDir = '../shared/exports';
    
    // Import team members first (they're referenced by projects)
    console.log('📋 Importing team members...');
    const teamMembersData = JSON.parse(fs.readFileSync(path.join(dataDir, 'team_members.json'), 'utf8'));
    for (const member of teamMembersData) {
      try {
        await prisma.teamMember.create({
          data: member
        });
      } catch (error) {
        console.log(`Skipping existing team member: ${member.name}`);
      }
    }

    // Import impact types
    console.log('📋 Importing impact types...');
    const impactTypesData = JSON.parse(fs.readFileSync(path.join(dataDir, 'impact_types.json'), 'utf8'));
    for (const impactType of impactTypesData) {
      try {
        await prisma.impactTypes.create({
          data: impactType
        });
      } catch (error) {
        console.log(`Skipping existing impact type: ${impactType.name}`);
      }
    }

    // Import priority rules
    console.log('📋 Importing priority rules...');
    const priorityRulesData = JSON.parse(fs.readFileSync(path.join(dataDir, 'priority_rules.json'), 'utf8'));
    for (const rule of priorityRulesData) {
      try {
        await prisma.priorityRules.create({
          data: rule
        });
      } catch (error) {
        console.log(`Skipping existing priority rule: ${rule.id}`);
      }
    }

    // Import projects
    console.log('📋 Importing projects...');
    const projectsData = JSON.parse(fs.readFileSync(path.join(dataDir, 'projects.json'), 'utf8'));
    for (const project of projectsData) {
      try {
        await prisma.project.create({
          data: project
        });
      } catch (error) {
        console.log(`Skipping existing project: ${project.name}`);
      }
    }

    // Import tasks
    console.log('📋 Importing tasks...');
    const tasksData = JSON.parse(fs.readFileSync(path.join(dataDir, 'tasks.json'), 'utf8'));
    for (const task of tasksData) {
      try {
        await prisma.task.create({
          data: task
        });
      } catch (error) {
        console.log(`Skipping existing task: ${task.name}`);
      }
    }

    // Import priority history
    console.log('📋 Importing priority history...');
    const priorityHistoryData = JSON.parse(fs.readFileSync(path.join(dataDir, 'priority_history.json'), 'utf8'));
    for (const history of priorityHistoryData) {
      try {
        await prisma.priorityHistory.create({
          data: history
        });
      } catch (error) {
        console.log(`Skipping existing priority history: ${history.id}`);
      }
    }

    console.log('✅ Sample data restored successfully!');

    // Verify the data
    const projectCount = await prisma.project.count();
    const taskCount = await prisma.task.count();
    const teamMemberCount = await prisma.teamMember.count();

    console.log(`📊 Data summary:`);
    console.log(`   - Projects: ${projectCount}`);
    console.log(`   - Tasks: ${taskCount}`);
    console.log(`   - Team Members: ${teamMemberCount}`);

  } catch (error) {
    console.error('❌ Error restoring sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

restoreSampleData();
