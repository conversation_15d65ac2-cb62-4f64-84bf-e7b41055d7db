// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum PriorityLevel {
  P0
  P1
  P2
  P3
  P4
}

enum EffortEstimate {
  S
  M
  L
  XL
}

enum ImpactType {
  Revenue
  Platform
  Bug_Fix @map("Bug Fix")
  R_D     @map("R&D")
}

// Models
model TeamMember {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String
  email       String   @unique
  role        String?
  department  String?
  is_active   Boolean  @default(true)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  led_projects      Project[] @relation("ProjectLead")
  customer_projects Project[] @relation("CustomerLead")
  assigned_tasks    Task[]
  assigned_subtasks SubTask[]

  @@map("team_members")
}

model ImpactTypes {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name         String   @unique
  label        String
  description  String
  color        String
  bg_color     String
  border_color String
  is_active    Boolean  @default(true)
  sort_order   Int      @default(0)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // Relations
  projects Project[]

  @@map("impact_types")
}

model Project {
  id                   String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                 String
  type                 String // 'internal' or 'external'
  customer_name        String?
  project_lead         String
  customer_lead        String?
  customer_contact     String?
  description          String?
  status               String         @default("not-started") // 'not-started', 'in-progress', 'completed', 'backlog', 'archived'
  company_name         String         @default("")
  start_date           DateTime?      @db.Date
  end_date             DateTime?      @db.Date
  original_end_date    DateTime?      @db.Date
  poc_url              String?
  prd_document_link    String?
  priority_order       Int?
  completed_at         DateTime?
  archived_at          DateTime?
  status_changed_at    DateTime?      @default(now())
  priority_level       PriorityLevel? @default(P3)
  effort_estimate      EffortEstimate? @default(M)
  impact_type          String?        @default("Platform")
  priority_assigned_at DateTime?      @default(now())
  last_reviewed_at     DateTime?      @default(now())
  auto_escalated       Boolean?       @default(false)
  created_at           DateTime       @default(now())
  updated_at           DateTime       @updatedAt

  // SDLC Enhancement Fields
  template_id          String?        @db.Uuid
  current_phase        String?        @default("requirements") // 'requirements', 'planning', 'implementation', 'testing', 'deployment', 'monitoring'
  methodology          String?        @default("agile") // 'agile', 'waterfall', 'hybrid'
  project_health       String?        @default("green") // 'green', 'yellow', 'red'
  completion_percentage Int?          @default(0)
  budget_allocated     Decimal?       @db.Decimal(10, 2)
  budget_spent         Decimal?       @db.Decimal(10, 2)
  team_size           Int?           @default(1)
  risk_level          String?        @default("low") // 'low', 'medium', 'high', 'critical'

  // Foreign keys
  project_lead_id   String? @db.Uuid
  customer_lead_id  String? @db.Uuid
  impact_type_id    String? @db.Uuid

  // Relations
  project_lead_member   TeamMember?        @relation("ProjectLead", fields: [project_lead_id], references: [id])
  customer_lead_member  TeamMember?        @relation("CustomerLead", fields: [customer_lead_id], references: [id])
  impact_type_ref       ImpactTypes?       @relation(fields: [impact_type_id], references: [id])
  template              ProjectTemplate?   @relation(fields: [template_id], references: [id])
  tasks                 Task[]
  integrations          ProjectIntegration[]
  priority_history      PriorityHistory[]
  phases                ProjectPhase[]
  milestones            ProjectMilestone[]
  requirements          Requirement[]
  test_cases            TestCase[]
  deployments           Deployment[]
  documents             ProjectDocument[]
  files                 ProjectFile[]

  @@map("projects")
}

model Task {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id   String    @db.Uuid
  name         String
  description  String?
  assignee     String
  due_date     DateTime? @db.Date
  status       String    @default("to-do") // 'to-do', 'in-progress', 'done'
  completed_at DateTime?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // SDLC Enhancement Fields
  phase_id       String? @db.Uuid
  milestone_id   String? @db.Uuid
  requirement_id String? @db.Uuid
  task_type      String? @default("development") // 'development', 'testing', 'documentation', 'deployment'
  priority       String? @default("medium") // 'low', 'medium', 'high', 'critical'
  effort_hours   Int?    // Estimated effort in hours
  actual_hours   Int?    // Actual time spent
  progress       Int?    @default(0) // 0-100

  // Foreign keys
  assignee_id String? @db.Uuid

  // Relations
  project         Project           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  assignee_member TeamMember?       @relation(fields: [assignee_id], references: [id])
  phase           ProjectPhase?     @relation(fields: [phase_id], references: [id])
  milestone       ProjectMilestone? @relation(fields: [milestone_id], references: [id])
  requirement     Requirement?      @relation(fields: [requirement_id], references: [id])
  sub_tasks       SubTask[]

  @@map("tasks")
}

model SubTask {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  task_id      String    @db.Uuid
  name         String
  description  String?
  assignee     String
  due_date     DateTime? @db.Date
  status       String    @default("to-do") // 'to-do', 'in-progress', 'done'
  completed_at DateTime?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // Foreign keys
  assignee_id String? @db.Uuid

  // Relations
  task            Task         @relation(fields: [task_id], references: [id], onDelete: Cascade)
  assignee_member TeamMember?  @relation(fields: [assignee_id], references: [id])

  @@map("sub_tasks")
}

model ProjectIntegration {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id       String   @db.Uuid
  integration_type String   // 'google_drive' or 'slack_webhook'
  integration_url  String
  integration_data Json?
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("project_integrations")
}

model PriorityHistory {
  id            String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id    String        @db.Uuid
  old_priority  PriorityLevel?
  new_priority  PriorityLevel
  changed_by    String?
  change_reason String?
  auto_escalated Boolean      @default(false)
  created_at    DateTime     @default(now())

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("priority_history")
}

model PriorityRules {
  id           String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  from_priority PriorityLevel
  to_priority   PriorityLevel
  max_days      Int
  is_active     Boolean       @default(true)
  created_at    DateTime      @default(now())
  updated_at    DateTime      @updatedAt

  @@map("priority_rules")
}

// Authentication and RBAC Models
model User {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email      String   @unique
  name       String
  avatar_url String?
  google_id  String?  @unique
  is_active  Boolean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relations
  user_roles         UserRole[]
  created_templates  ProjectTemplate[]
  created_requirements Requirement[]
  created_test_cases TestCase[]
  executed_test_cases TestCase[] @relation("TestCaseExecutor")
  deployments        Deployment[]
  created_documents  ProjectDocument[]
  uploaded_files     ProjectFile[]

  @@map("users")
}

model Role {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String   @unique
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  user_roles       UserRole[]
  role_permissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String   @unique
  resource    String
  action      String
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  role_permissions RolePermission[]

  @@map("permissions")
}

model UserRole {
  user_id     String   @db.Uuid
  role_id     String   @db.Uuid
  assigned_at DateTime @default(now())
  assigned_by String

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  role Role @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@id([user_id, role_id])
  @@map("user_roles")
}

model RolePermission {
  role_id       String @db.Uuid
  permission_id String @db.Uuid

  // Relations
  role       Role       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@id([role_id, permission_id])
  @@map("role_permissions")
}

// SDLC Workflow Models

model ProjectTemplate {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String
  description String?
  methodology String   @default("agile") // 'agile', 'waterfall', 'hybrid'
  category    String?  // 'web_app', 'mobile_app', 'api', 'data_science', etc.
  is_active   Boolean  @default(true)
  created_by  String   @db.Uuid
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Template configuration
  default_phases Json? // Array of phase configurations
  default_milestones Json? // Array of milestone templates
  default_requirements Json? // Array of requirement templates
  default_tasks Json? // Array of task templates

  // Relations
  creator  User      @relation(fields: [created_by], references: [id])
  projects Project[]

  @@map("project_templates")
}

model ProjectPhase {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id  String   @db.Uuid
  name        String   // 'Requirements', 'Planning', 'Implementation', etc.
  description String?
  status      String   @default("not_started") // 'not_started', 'in_progress', 'completed', 'blocked'
  order_index Int
  start_date  DateTime?
  end_date    DateTime?
  progress    Int      @default(0) // 0-100
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project    Project            @relation(fields: [project_id], references: [id], onDelete: Cascade)
  milestones ProjectMilestone[]
  tasks      Task[]

  @@map("project_phases")
}

model ProjectMilestone {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id  String    @db.Uuid
  phase_id    String?   @db.Uuid
  name        String
  description String?
  due_date    DateTime?
  completed_at DateTime?
  status      String    @default("pending") // 'pending', 'completed', 'overdue'
  priority    String    @default("medium") // 'low', 'medium', 'high', 'critical'
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt

  // Relations
  project Project      @relation(fields: [project_id], references: [id], onDelete: Cascade)
  phase   ProjectPhase? @relation(fields: [phase_id], references: [id])
  tasks   Task[]

  @@map("project_milestones")
}

model Requirement {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id  String   @db.Uuid
  title       String
  description String?
  type        String   @default("functional") // 'functional', 'non_functional', 'constraint', 'assumption'
  priority    String   @default("medium") // 'low', 'medium', 'high', 'critical'
  status      String   @default("draft") // 'draft', 'approved', 'implemented', 'tested', 'rejected'
  source      String?  // Who requested this requirement
  rationale   String?  // Why this requirement is needed
  acceptance_criteria String? // How to verify this requirement
  order_index Int?
  created_by  String   @db.Uuid
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)
  creator User    @relation(fields: [created_by], references: [id])
  tasks   Task[]  // Requirements can be linked to tasks
  test_cases TestCase[] // Requirements can be linked to test cases

  @@map("requirements")
}

model TestCase {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id    String   @db.Uuid
  requirement_id String? @db.Uuid
  title         String
  description   String?
  preconditions String?
  test_steps    String?  // JSON array of test steps
  expected_result String?
  actual_result String?
  status        String   @default("not_executed") // 'not_executed', 'passed', 'failed', 'blocked', 'skipped'
  priority      String   @default("medium") // 'low', 'medium', 'high', 'critical'
  test_type     String   @default("manual") // 'manual', 'automated', 'integration', 'unit'
  executed_by   String?  @db.Uuid
  executed_at   DateTime?
  created_by    String   @db.Uuid
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  project     Project     @relation(fields: [project_id], references: [id], onDelete: Cascade)
  requirement Requirement? @relation(fields: [requirement_id], references: [id])
  creator     User        @relation(fields: [created_by], references: [id])
  executor    User?       @relation("TestCaseExecutor", fields: [executed_by], references: [id])

  @@map("test_cases")
}

model Deployment {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id    String   @db.Uuid
  version       String
  environment   String   // 'development', 'staging', 'production'
  status        String   @default("pending") // 'pending', 'in_progress', 'completed', 'failed', 'rolled_back'
  deployed_by   String   @db.Uuid
  deployed_at   DateTime?
  rollback_at   DateTime?
  notes         String?
  commit_hash   String?
  build_number  String?
  deployment_url String?
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  project  Project @relation(fields: [project_id], references: [id], onDelete: Cascade)
  deployer User    @relation(fields: [deployed_by], references: [id])

  @@map("deployments")
}

model ProjectDocument {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id  String   @db.Uuid
  title       String
  content     String?  // Rich text content
  document_type String @default("general") // 'requirements', 'design', 'api', 'user_guide', 'technical', 'general'
  file_url    String?  // URL to uploaded file
  file_name   String?
  file_size   Int?
  mime_type   String?
  version     String   @default("1.0")
  is_active   Boolean  @default(true)
  created_by  String   @db.Uuid
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)
  creator User    @relation(fields: [created_by], references: [id])

  @@map("project_documents")
}

model ProjectFile {
  id                String   @id @default(cuid())
  project_id        String
  original_name     String
  stored_name       String
  file_path         String
  file_size         Int
  mime_type         String
  file_type         String   // 'document', 'image', 'spreadsheet', etc.
  upload_source     String   @default("manual") // 'manual', 'ai-analysis', 'drag-drop'

  // AI Analysis metadata
  analysis_status   String?  @default("pending") // 'pending', 'completed', 'failed'
  analysis_result   Json?    // Store AI analysis results
  extracted_requirements Int @default(0)

  // File organization
  tags              String[] @default([])
  category          String?  // 'requirements', 'design', 'technical', etc.
  version           Int      @default(1)
  parent_file_id    String?  // For versioning

  // Metadata
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  created_by        String?

  // Relations
  project           Project  @relation(fields: [project_id], references: [id], onDelete: Cascade)
  creator           User?    @relation(fields: [created_by], references: [id])
  parent_file       ProjectFile? @relation("FileVersions", fields: [parent_file_id], references: [id])
  child_files       ProjectFile[] @relation("FileVersions")

  @@map("project_files")
}
