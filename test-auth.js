// Simple test script to verify Google OAuth authentication
const API_BASE_URL = 'http://localhost:3000/api';

async function testGoogleAuth() {
  console.log('🧪 Testing Google OAuth Authentication...\n');

  // Test 1: Health check
  console.log('1. Testing API health...');
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    console.log('✅ API Health:', data.message);
  } catch (error) {
    console.log('❌ API Health failed:', error.message);
    return;
  }

  // Test 2: Test guest access to projects
  console.log('\n2. Testing guest access to projects...');
  try {
    const response = await fetch(`${API_BASE_URL}/projects/basic`);
    const projects = await response.json();
    console.log(`✅ Guest can access ${projects.length} projects`);
  } catch (error) {
    console.log('❌ Guest access failed:', error.message);
  }

  // Test 3: Test protected endpoint without auth (should fail)
  console.log('\n3. Testing protected endpoint without auth...');
  try {
    const response = await fetch(`${API_BASE_URL}/admin/users`);
    const data = await response.json();
    if (response.status === 401) {
      console.log('✅ Protected endpoint correctly requires authentication');
    } else {
      console.log('❌ Protected endpoint should require authentication');
    }
  } catch (error) {
    console.log('❌ Protected endpoint test failed:', error.message);
  }

  // Test 4: Test Google OAuth with mock data
  console.log('\n4. Testing Google OAuth with mock user...');
  try {
    const mockProfile = {
      id: 'test-google-id-123',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/avatar.jpg',
      verified_email: true
    };

    const response = await fetch(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: 'mock-google-jwt-token',
        profile: mockProfile
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Google OAuth authentication successful!');
      console.log(`   User: ${data.data.user.name} (${data.data.user.email})`);
      console.log(`   Roles: ${data.data.user.roles.join(', ')}`);
      console.log(`   Permissions: ${data.data.user.permissions.length} permissions`);
      
      // Test 5: Test authenticated request
      console.log('\n5. Testing authenticated request...');
      const authResponse = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${data.data.token}`
        }
      });
      
      if (authResponse.ok) {
        const userData = await authResponse.json();
        console.log('✅ Authenticated request successful!');
        console.log(`   Authenticated as: ${userData.data.user.name}`);
      } else {
        console.log('❌ Authenticated request failed');
      }
      
    } else {
      console.log('❌ Google OAuth authentication failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Google OAuth test failed:', error.message);
  }

  console.log('\n🎉 Authentication tests completed!');
}

// Run the test
testGoogleAuth();
