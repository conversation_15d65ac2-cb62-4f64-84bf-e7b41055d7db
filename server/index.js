import express from 'express';
import cors from 'cors';
import { PrismaClient } from './generated/prisma/index.js';

const app = express();
const port = process.env.PORT || 3000;
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: 'http://localhost:8080',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'API server is running' });
});

// Projects endpoints
app.get('/api/projects', async (req, res) => {
  try {
    const projects = await prisma.project.findMany({
      include: {
        tasks: {
          include: {
            sub_tasks: true,
            assignee_member: true
          },
          orderBy: { created_at: 'asc' }
        },
        project_lead_member: true,
        customer_lead_member: true,
        impact_type_ref: true
      },
      orderBy: { priority_order: 'asc' }
    });
    res.json(projects);
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/projects/basic', async (req, res) => {
  try {
    const projects = await prisma.project.findMany({
      orderBy: { priority_order: 'asc' }
    });
    res.json(projects);
  } catch (error) {
    console.error('Error fetching basic projects:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/projects/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        tasks: {
          include: {
            sub_tasks: true,
            assignee_member: true
          },
          orderBy: { created_at: 'asc' }
        },
        project_lead_member: true,
        customer_lead_member: true,
        impact_type_ref: true
      }
    });
    
    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }
    
    res.json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/projects', async (req, res) => {
  try {
    const project = await prisma.project.create({
      data: req.body
    });
    res.status(201).json(project);
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/projects/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const project = await prisma.project.update({
      where: { id },
      data: req.body
    });
    res.json(project);
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/projects/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await prisma.project.delete({
      where: { id }
    });
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ error: error.message });
  }
});

// Team members endpoints
app.get('/api/team-members', async (req, res) => {
  try {
    const { active } = req.query;
    const where = active === 'true' ? { is_active: true } : {};

    const teamMembers = await prisma.teamMember.findMany({
      where,
      orderBy: { name: 'asc' }
    });
    res.json(teamMembers);
  } catch (error) {
    console.error('Error fetching team members:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/team-members', async (req, res) => {
  try {
    const teamMember = await prisma.teamMember.create({
      data: req.body
    });
    res.status(201).json(teamMember);
  } catch (error) {
    console.error('Error creating team member:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/team-members/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const teamMember = await prisma.teamMember.update({
      where: { id },
      data: req.body
    });
    res.json(teamMember);
  } catch (error) {
    console.error('Error updating team member:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/team-members/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await prisma.teamMember.delete({
      where: { id }
    });
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting team member:', error);
    res.status(500).json({ error: error.message });
  }
});

// Impact types endpoints
app.get('/api/impact-types', async (req, res) => {
  try {
    const impactTypes = await prisma.impactTypes.findMany({
      where: { is_active: true },
      orderBy: { sort_order: 'asc' }
    });
    res.json(impactTypes);
  } catch (error) {
    console.error('Error fetching impact types:', error);
    res.status(500).json({ error: error.message });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`API server running at http://localhost:${port}`);
});
