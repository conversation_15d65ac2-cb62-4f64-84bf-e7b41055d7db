-- Add completed_at fields to projects, tasks, and sub_tasks tables
ALTER TABLE public.projects 
ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.tasks 
ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.sub_tasks 
ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;

-- Create function to update completed_at when status changes to completed/done
CREATE OR REPLACE FUNCTION public.update_completed_at()
RETURNS TRIGGER AS $$
BEGIN
  -- For projects table
  IF TG_TABLE_NAME = 'projects' THEN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'completed' AND OLD.status = 'completed' THEN
      NEW.completed_at = NULL;
    END IF;
  END IF;
  
  -- For tasks and sub_tasks tables
  IF TG_TABLE_NAME IN ('tasks', 'sub_tasks') THEN
    IF NEW.status = 'done' AND OLD.status != 'done' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'done' AND OLD.status = 'done' THEN
      NEW.completed_at = NULL;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic completion date updates
CREATE TRIGGER update_project_completed_at
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_completed_at();

CREATE TRIGGER update_task_completed_at
  BEFORE UPDATE ON public.tasks
  FOR EACH ROW
  EXECUTE FUNCTION public.update_completed_at();

CREATE TRIGGER update_subtask_completed_at
  BEFORE UPDATE ON public.sub_tasks
  FOR EACH ROW
  EXECUTE FUNCTION public.update_completed_at();