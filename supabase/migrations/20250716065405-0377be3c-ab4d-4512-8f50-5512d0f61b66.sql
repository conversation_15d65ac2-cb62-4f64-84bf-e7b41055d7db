-- Create function to convert task to project
CREATE OR REPLACE FUNCTION public.convert_task_to_project(
  task_id_param uuid,
  project_name text,
  project_description text DEFAULT NULL,
  project_type text DEFAULT 'Development',
  company_name text DEFAULT 'TwoDot AI',
  start_date date DEFAULT NULL,
  end_date date DEFAULT NULL,
  customer_name text DEFAULT NULL,
  customer_lead text DEFAULT NULL,
  customer_contact text DEFAULT NULL,
  prd_document_link text DEFAULT NULL,
  poc_url text DEFAULT NULL,
  effort_estimate public.effort_estimate DEFAULT 'M',
  impact_type public.impact_type DEFAULT 'Platform',
  priority_level public.priority_level DEFAULT 'P3'
)
RETURNS uuid
LANGUAGE plpgsql
AS $function$
DECLARE
  source_task RECORD;
  new_project_id UUID;
  existing_subtask RECORD;
BEGIN
  -- Get the source task details
  SELECT * INTO source_task 
  FROM public.tasks 
  WHERE id = task_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Source task not found';
  END IF;
  
  -- Create new project from task data
  INSERT INTO public.projects (
    name,
    description,
    type,
    company_name,
    project_lead,
    project_lead_id,
    start_date,
    end_date,
    customer_name,
    customer_lead,
    customer_lead_id,
    customer_contact,
    prd_document_link,
    poc_url,
    effort_estimate,
    impact_type,
    priority_level,
    status
  ) VALUES (
    project_name,
    project_description,
    project_type,
    company_name,
    source_task.assignee,
    source_task.assignee_id,
    start_date,
    end_date,
    customer_name,
    customer_lead,
    source_task.assignee_id, -- Use task assignee as customer lead if not specified
    customer_contact,
    prd_document_link,
    poc_url,
    effort_estimate,
    impact_type,
    priority_level,
    CASE 
      WHEN source_task.status = 'completed' THEN 'completed'
      WHEN source_task.status = 'in-progress' THEN 'in-progress'
      ELSE 'active'
    END
  ) RETURNING id INTO new_project_id;
  
  -- Convert existing subtasks to tasks under the new project
  FOR existing_subtask IN 
    SELECT * FROM public.sub_tasks WHERE task_id = task_id_param
  LOOP
    INSERT INTO public.tasks (
      project_id,
      name,
      description,
      assignee,
      assignee_id,
      due_date,
      status,
      completed_at
    ) VALUES (
      new_project_id,
      existing_subtask.name,
      existing_subtask.description,
      existing_subtask.assignee,
      existing_subtask.assignee_id,
      existing_subtask.due_date,
      existing_subtask.status,
      existing_subtask.completed_at
    );
  END LOOP;
  
  -- Delete the original subtasks
  DELETE FROM public.sub_tasks WHERE task_id = task_id_param;
  
  -- Delete the original task
  DELETE FROM public.tasks WHERE id = task_id_param;
  
  RETURN new_project_id;
END;
$function$;