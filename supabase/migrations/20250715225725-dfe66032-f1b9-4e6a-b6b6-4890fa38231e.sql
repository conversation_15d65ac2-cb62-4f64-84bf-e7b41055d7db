-- Remove the default value first
ALTER TABLE projects ALTER COLUMN impact_type DROP DEFAULT;

-- Create new enum with the updated impact types
CREATE TYPE impact_type_new AS ENUM ('Revenue', 'Platform', 'Bug Fix', 'R&D');

-- Update the column to use the new enum type
ALTER TABLE projects 
ALTER COLUMN impact_type TYPE impact_type_new 
USING CASE 
  WHEN impact_type::text = 'Users' THEN 'Platform'::impact_type_new
  WHEN impact_type::text = 'Tech' THEN 'Bug Fix'::impact_type_new
  WHEN impact_type::text = 'Compliance' THEN 'R&D'::impact_type_new
  ELSE impact_type::text::impact_type_new
END;

-- Drop the old enum and rename the new one
DROP TYPE impact_type;
ALTER TYPE impact_type_new RENAME TO impact_type;

-- Set the new default value
ALTER TABLE projects ALTER COLUMN impact_type SET DEFAULT 'Platform';