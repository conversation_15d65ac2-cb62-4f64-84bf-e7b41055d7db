-- Create enums for the priority system
CREATE TYPE priority_level AS ENUM ('P0', 'P1', 'P2', 'P3', 'P4');
CREATE TYPE effort_estimate AS ENUM ('S', 'M', 'L', 'XL');
CREATE TYPE impact_type AS ENUM ('Revenue', 'Users', 'Tech', 'Compliance');
CREATE TYPE dependency_type AS ENUM ('None', 'Internal', 'External');

-- Add new columns to projects table
ALTER TABLE public.projects 
ADD COLUMN priority_level priority_level DEFAULT 'P3',
ADD COLUMN effort_estimate effort_estimate DEFAULT 'M',
ADD COLUMN impact_type impact_type DEFAULT 'Tech',
ADD COLUMN dependency_type dependency_type DEFAULT 'None',
ADD COLUMN priority_assigned_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
ADD COLUMN last_reviewed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
ADD COLUMN auto_escalated BO<PERSON>EAN DEFAULT false,
ADD COLUMN days_in_priority INTEGER GENERATED ALWAYS AS (
  EXTRACT(DAY FROM (now() - priority_assigned_at))::INTEGER
) STORED;

-- Create priority history table for audit trail
CREATE TABLE public.priority_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  old_priority priority_level,
  new_priority priority_level NOT NULL,
  changed_by TEXT,
  change_reason TEXT,
  auto_escalated BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create priority rules table for configurable escalation
CREATE TABLE public.priority_rules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  from_priority priority_level NOT NULL,
  to_priority priority_level NOT NULL,
  max_days INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default escalation rules
INSERT INTO public.priority_rules (from_priority, to_priority, max_days) VALUES
('P2', 'P1', 14),
('P3', 'P2', 28);

-- Enable RLS on new tables
ALTER TABLE public.priority_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.priority_rules ENABLE ROW LEVEL SECURITY;

-- Create policies for priority_history
CREATE POLICY "Priority history is viewable by everyone" 
ON public.priority_history FOR SELECT USING (true);

CREATE POLICY "Priority history can be inserted by everyone" 
ON public.priority_history FOR INSERT WITH CHECK (true);

-- Create policies for priority_rules
CREATE POLICY "Priority rules are viewable by everyone" 
ON public.priority_rules FOR SELECT USING (true);

CREATE POLICY "Priority rules can be updated by everyone" 
ON public.priority_rules FOR UPDATE USING (true);

CREATE POLICY "Priority rules can be inserted by everyone" 
ON public.priority_rules FOR INSERT WITH CHECK (true);

-- Function to update priority_assigned_at when priority changes
CREATE OR REPLACE FUNCTION public.update_priority_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update if priority actually changed
  IF OLD.priority_level IS DISTINCT FROM NEW.priority_level THEN
    NEW.priority_assigned_at = now();
    NEW.last_reviewed_at = now();
    
    -- Insert into priority history
    INSERT INTO public.priority_history (
      project_id, 
      old_priority, 
      new_priority, 
      auto_escalated
    ) VALUES (
      NEW.id, 
      OLD.priority_level, 
      NEW.priority_level, 
      NEW.auto_escalated
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for priority timestamp updates
CREATE TRIGGER update_priority_timestamp_trigger
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_priority_timestamp();

-- Function to check and apply auto-escalation
CREATE OR REPLACE FUNCTION public.check_auto_escalation()
RETURNS INTEGER AS $$
DECLARE
  escalated_count INTEGER := 0;
  project_record RECORD;
  rule_record RECORD;
BEGIN
  -- Check each project against escalation rules
  FOR project_record IN 
    SELECT id, priority_level, priority_assigned_at, auto_escalated
    FROM public.projects 
    WHERE status != 'completed'
  LOOP
    -- Find applicable escalation rule
    SELECT * INTO rule_record
    FROM public.priority_rules
    WHERE from_priority = project_record.priority_level
      AND is_active = true
      AND EXTRACT(DAY FROM (now() - project_record.priority_assigned_at)) >= max_days;
    
    -- Apply escalation if rule found and not already escalated
    IF FOUND AND NOT project_record.auto_escalated THEN
      UPDATE public.projects 
      SET 
        priority_level = rule_record.to_priority,
        auto_escalated = true,
        updated_at = now()
      WHERE id = project_record.id;
      
      escalated_count := escalated_count + 1;
    END IF;
  END LOOP;
  
  RETURN escalated_count;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger to new tables
CREATE TRIGGER update_priority_history_updated_at
  BEFORE UPDATE ON public.priority_history
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_priority_rules_updated_at
  BEFORE UPDATE ON public.priority_rules
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();