-- Add status_changed_at column to projects table
ALTER TABLE public.projects 
ADD COLUMN status_changed_at TIMESTAMP WITH TIME ZONE;

-- Initialize existing projects with current updated_at or completed_at
UPDATE public.projects 
SET status_changed_at = COALESCE(completed_at, updated_at)
WHERE status_changed_at IS NULL;

-- Set default for new projects
ALTER TABLE public.projects 
ALTER COLUMN status_changed_at SET DEFAULT now();

-- Create trigger function to update status_changed_at only when status changes
CREATE OR REPLACE FUNCTION public.update_status_changed_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update status_changed_at if the status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    NEW.status_changed_at = now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for projects table
DROP TRIGGER IF EXISTS trigger_update_status_changed_at ON public.projects;
CREATE TRIGGER trigger_update_status_changed_at
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_status_changed_at();