-- Add 'archived' status to the project status enum
ALTER TYPE project_status ADD VALUE 'archived';

-- Add archived_at field to projects table
ALTER TABLE public.projects 
ADD COLUMN archived_at timestamp with time zone;

-- Update the trigger to handle archived status
CREATE OR REPLACE FUNCTION public.update_completed_at()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- For projects table
  IF TG_TABLE_NAME = 'projects' THEN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'completed' AND OLD.status = 'completed' THEN
      NEW.completed_at = NULL;
    END IF;
    
    -- Handle archived status
    IF NEW.status = 'archived' AND OLD.status != 'archived' THEN
      NEW.archived_at = now();
    ELSIF NEW.status != 'archived' AND OLD.status = 'archived' THEN
      NEW.archived_at = NULL;
    END IF;
  END IF;
  
  -- For tasks and sub_tasks tables (now using 'completed' instead of 'done')
  IF TG_TABLE_NAME IN ('tasks', 'sub_tasks') THEN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = now();
    ELSIF NEW.status != 'completed' AND OLD.status = 'completed' THEN
      NEW.completed_at = NULL;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$function$;