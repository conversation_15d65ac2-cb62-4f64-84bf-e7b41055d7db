-- Add CASCADE constraints for project deletion
-- First, drop existing foreign key constraints
ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_project_id_fkey;
ALTER TABLE public.sub_tasks DROP CONSTRAINT IF EXISTS sub_tasks_task_id_fkey;
ALTER TABLE public.project_integrations DROP CONSTRAINT IF EXISTS project_integrations_project_id_fkey;

-- Re-add foreign key constraints with CASCADE delete
ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.sub_tasks 
ADD CONSTRAINT sub_tasks_task_id_fkey 
FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE CASCADE;

ALTER TABLE public.project_integrations 
ADD CONSTRAINT project_integrations_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;