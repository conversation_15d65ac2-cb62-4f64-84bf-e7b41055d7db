// Verify Google OAuth Configuration
import { OAuth2Client } from 'google-auth-library';
import dotenv from 'dotenv';

dotenv.config({ path: './backend/.env' });

async function verifyGoogleConfig() {
  console.log('🔍 Verifying Google OAuth Configuration...\n');

  // Check environment variables
  console.log('1. Environment Variables:');
  console.log('   GOOGLE_CLIENT_ID:', process.env.GOOGLE_CLIENT_ID ? '✅ Set' : '❌ Missing');
  console.log('   GOOGLE_CLIENT_SECRET:', process.env.GOOGLE_CLIENT_SECRET ? '✅ Set' : '❌ Missing');
  console.log('   GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI || '❌ Missing');

  if (!process.env.GOOGLE_CLIENT_ID) {
    console.log('\n❌ GOOGLE_CLIENT_ID is required. Please check your .env file.');
    return;
  }

  // Test OAuth2Client initialization
  console.log('\n2. OAuth2Client Initialization:');
  try {
    const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
    console.log('   ✅ OAuth2Client created successfully');
    
    // Test token verification (this will fail but shows the client works)
    try {
      await client.verifyIdToken({
        idToken: 'fake-token',
        audience: process.env.GOOGLE_CLIENT_ID,
      });
    } catch (error) {
      if (error.message.includes('Wrong number of segments')) {
        console.log('   ✅ Token verification method working (expected error for fake token)');
      } else {
        console.log('   ⚠️  Unexpected error:', error.message);
      }
    }
  } catch (error) {
    console.log('   ❌ OAuth2Client initialization failed:', error.message);
  }

  // Check required Google Cloud Console settings
  console.log('\n3. Required Google Cloud Console Configuration:');
  console.log('   📋 Authorized JavaScript origins should include:');
  console.log('      - http://localhost:8080');
  console.log('      - http://localhost:3000');
  console.log('   📋 Authorized redirect URIs should include:');
  console.log('      - http://localhost:8080/auth/callback');
  console.log('      - http://localhost:3000/api/auth/google/callback');
  console.log('   📋 Required APIs should be enabled:');
  console.log('      - Google+ API (deprecated but may be needed)');
  console.log('      - People API');
  console.log('      - Identity and Access Management (IAM) API');

  console.log('\n4. Frontend Configuration:');
  console.log('   📋 VITE_GOOGLE_CLIENT_ID should match GOOGLE_CLIENT_ID');
  console.log('   📋 Google Identity Services script should be loaded');
  console.log('   📋 Scopes should include: openid, email, profile');

  console.log('\n✅ Configuration check complete!');
  console.log('\n💡 Next steps if OAuth is still not working:');
  console.log('   1. Check browser console for JavaScript errors');
  console.log('   2. Verify Google Cloud Console settings match above');
  console.log('   3. Try the debug button on the login page');
  console.log('   4. Check network tab for failed API calls');
}

verifyGoogleConfig().catch(console.error);
